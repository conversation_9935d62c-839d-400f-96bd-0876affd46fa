using FluentValidation;
using MediatR;
using Microsoft.Extensions.DependencyInjection;
using System.Reflection;
using Whimlab.AI.Agent.Platform.Application.Core.Behaviors;

namespace Whimlab.AI.Agent.Platform.Application.Core.Extensions;

/// <summary>
/// 应用层服务注册扩展
/// </summary>
public static class ServiceCollectionExtensions
{
    /// <summary>
    /// 添加应用层核心服务
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddApplicationCore(this IServiceCollection services)
    {
        // 注册MediatR
        services.AddMediatR(cfg =>
        {
            cfg.RegisterServicesFromAssembly(Assembly.GetExecutingAssembly());
        });

        // 注册管道行为
        services.AddTransient(typeof(IPipelineBehavior<,>), typeof(ValidationBehavior<,>));

        return services;
    }

    /// <summary>
    /// 添加应用层模块服务
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <param name="assemblies">要扫描的程序集</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddApplicationModule(this IServiceCollection services, params Assembly[] assemblies)
    {
        if (assemblies == null || assemblies.Length == 0)
        {
            throw new ArgumentException("至少需要提供一个程序集", nameof(assemblies));
        }

        // 注册MediatR处理器
        foreach (var assembly in assemblies)
        {
            services.AddMediatR(cfg =>
            {
                cfg.RegisterServicesFromAssembly(assembly);
            });
        }

        // 注册FluentValidation验证器
        foreach (var assembly in assemblies)
        {
            services.AddValidatorsFromAssembly(assembly);
        }

        // 注册AutoMapper配置
        services.AddAutoMapper(assemblies);

        return services;
    }

    /// <summary>
    /// 添加Identity模块服务
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddIdentityApplication(this IServiceCollection services)
    {
        var assembly = Assembly.Load("Whimlab.AI.Agent.Platform.Application.Identity");
        return services.AddApplicationModule(assembly);
    }

    /// <summary>
    /// 添加Agent模块服务
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddAgentApplication(this IServiceCollection services)
    {
        var assembly = Assembly.Load("Whimlab.AI.Agent.Platform.Application.Agent");
        return services.AddApplicationModule(assembly);
    }

    /// <summary>
    /// 添加Conversation模块服务
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddConversationApplication(this IServiceCollection services)
    {
        var assembly = Assembly.Load("Whimlab.AI.Agent.Platform.Application.Conversation");
        return services.AddApplicationModule(assembly);
    }

    /// <summary>
    /// 添加Subscription模块服务
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddSubscriptionApplication(this IServiceCollection services)
    {
        var assembly = Assembly.Load("Whimlab.AI.Agent.Platform.Application.Subscription");
        return services.AddApplicationModule(assembly);
    }

    /// <summary>
    /// 添加所有应用层服务
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddAllApplicationServices(this IServiceCollection services)
    {
        // 添加核心服务
        services.AddApplicationCore();

        // 添加所有模块服务
        services.AddIdentityApplication();
        services.AddAgentApplication();
        services.AddConversationApplication();
        services.AddSubscriptionApplication();

        return services;
    }
}
