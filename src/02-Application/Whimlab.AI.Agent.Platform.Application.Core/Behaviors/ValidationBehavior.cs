using FluentValidation;
using MediatR;
using Microsoft.Extensions.Logging;
using Whimlab.AI.Agent.Platform.Application.Core.Common;

namespace Whimlab.AI.Agent.Platform.Application.Core.Behaviors;

/// <summary>
/// 验证行为管道
/// </summary>
/// <typeparam name="TRequest">请求类型</typeparam>
/// <typeparam name="TResponse">响应类型</typeparam>
public class ValidationBehavior<TRequest, TResponse> : IPipelineBehavior<TRequest, TResponse>
    where TRequest : notnull
{
    private readonly IEnumerable<IValidator<TRequest>> _validators;

    /// <summary>
    /// 初始化验证行为
    /// </summary>
    /// <param name="validators">验证器集合</param>
    public ValidationBehavior(IEnumerable<IValidator<TRequest>> validators)
    {
        _validators = validators;
    }

    /// <summary>
    /// 处理请求
    /// </summary>
    /// <param name="request">请求</param>
    /// <param name="next">下一个处理器</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>响应</returns>
    public async Task<TResponse> Handle(TRequest request, RequestHandlerDelegate<TResponse> next, CancellationToken cancellationToken)
    {
        if (!_validators.Any())
        {
            return await next();
        }

        var context = new ValidationContext<TRequest>(request);

        var validationResults = await Task.WhenAll(
            _validators.Select(v => v.ValidateAsync(context, cancellationToken)));

        var failures = validationResults
            .SelectMany(r => r.Errors)
            .Where(f => f != null)
            .ToList();

        if (failures.Any())
        {
            var errorMessage = string.Join("; ", failures.Select(f => f.ErrorMessage));
            
            // 如果响应类型是Result或Result<T>，返回失败结果
            if (typeof(TResponse).IsGenericType && typeof(TResponse).GetGenericTypeDefinition() == typeof(Result<>))
            {
                var resultType = typeof(TResponse).GetGenericArguments()[0];
                var failureMethod = typeof(Result).GetMethod(nameof(Result.Failure), new[] { typeof(string), typeof(string) })!
                    .MakeGenericMethod(resultType);
                
                return (TResponse)failureMethod.Invoke(null, new object[] { errorMessage, "VALIDATION_FAILED" })!;
            }
            
            if (typeof(TResponse) == typeof(Result))
            {
                return (TResponse)(object)Result.Failure(errorMessage, "VALIDATION_FAILED");
            }

            throw new ValidationException(failures);
        }

        return await next();
    }
}

/// <summary>
/// 日志行为管道
/// </summary>
/// <typeparam name="TRequest">请求类型</typeparam>
/// <typeparam name="TResponse">响应类型</typeparam>
public class LoggingBehavior<TRequest, TResponse> : IPipelineBehavior<TRequest, TResponse>
    where TRequest : notnull
{
    private readonly ILogger<LoggingBehavior<TRequest, TResponse>> _logger;

    /// <summary>
    /// 初始化日志行为
    /// </summary>
    /// <param name="logger">日志记录器</param>
    public LoggingBehavior(ILogger<LoggingBehavior<TRequest, TResponse>> logger)
    {
        _logger = logger;
    }

    /// <summary>
    /// 处理请求
    /// </summary>
    /// <param name="request">请求</param>
    /// <param name="next">下一个处理器</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>响应</returns>
    public async Task<TResponse> Handle(TRequest request, RequestHandlerDelegate<TResponse> next, CancellationToken cancellationToken)
    {
        var requestName = typeof(TRequest).Name;
        var requestId = Guid.NewGuid();

        _logger.LogInformation("开始处理请求 {RequestName} ({RequestId})", requestName, requestId);

        var stopwatch = System.Diagnostics.Stopwatch.StartNew();

        try
        {
            var response = await next();
            
            stopwatch.Stop();
            
            _logger.LogInformation("完成处理请求 {RequestName} ({RequestId}) 耗时 {ElapsedMilliseconds}ms", 
                requestName, requestId, stopwatch.ElapsedMilliseconds);

            return response;
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            
            _logger.LogError(ex, "处理请求 {RequestName} ({RequestId}) 时发生错误，耗时 {ElapsedMilliseconds}ms", 
                requestName, requestId, stopwatch.ElapsedMilliseconds);

            throw;
        }
    }
}

/// <summary>
/// 性能监控行为管道
/// </summary>
/// <typeparam name="TRequest">请求类型</typeparam>
/// <typeparam name="TResponse">响应类型</typeparam>
public class PerformanceBehavior<TRequest, TResponse> : IPipelineBehavior<TRequest, TResponse>
    where TRequest : notnull
{
    private readonly ILogger<PerformanceBehavior<TRequest, TResponse>> _logger;
    private readonly long _slowRequestThresholdMs;

    /// <summary>
    /// 初始化性能监控行为
    /// </summary>
    /// <param name="logger">日志记录器</param>
    /// <param name="slowRequestThresholdMs">慢请求阈值（毫秒）</param>
    public PerformanceBehavior(
        ILogger<PerformanceBehavior<TRequest, TResponse>> logger,
        long slowRequestThresholdMs = 500)
    {
        _logger = logger;
        _slowRequestThresholdMs = slowRequestThresholdMs;
    }

    /// <summary>
    /// 处理请求
    /// </summary>
    /// <param name="request">请求</param>
    /// <param name="next">下一个处理器</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>响应</returns>
    public async Task<TResponse> Handle(TRequest request, RequestHandlerDelegate<TResponse> next, CancellationToken cancellationToken)
    {
        var stopwatch = System.Diagnostics.Stopwatch.StartNew();

        var response = await next();

        stopwatch.Stop();

        if (stopwatch.ElapsedMilliseconds > _slowRequestThresholdMs)
        {
            var requestName = typeof(TRequest).Name;
            
            _logger.LogWarning("慢请求检测: {RequestName} 耗时 {ElapsedMilliseconds}ms (阈值: {ThresholdMs}ms)", 
                requestName, stopwatch.ElapsedMilliseconds, _slowRequestThresholdMs);
        }

        return response;
    }
}

/// <summary>
/// 事务行为管道
/// </summary>
/// <typeparam name="TRequest">请求类型</typeparam>
/// <typeparam name="TResponse">响应类型</typeparam>
public class TransactionBehavior<TRequest, TResponse> : IPipelineBehavior<TRequest, TResponse>
    where TRequest : notnull
{
    private readonly ILogger<TransactionBehavior<TRequest, TResponse>> _logger;

    /// <summary>
    /// 初始化事务行为
    /// </summary>
    /// <param name="logger">日志记录器</param>
    public TransactionBehavior(ILogger<TransactionBehavior<TRequest, TResponse>> logger)
    {
        _logger = logger;
    }

    /// <summary>
    /// 处理请求
    /// </summary>
    /// <param name="request">请求</param>
    /// <param name="next">下一个处理器</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>响应</returns>
    public async Task<TResponse> Handle(TRequest request, RequestHandlerDelegate<TResponse> next, CancellationToken cancellationToken)
    {
        // 只对命令操作启用事务
        if (request is not ICommand && !IsGenericCommand(request))
        {
            return await next();
        }

        var requestName = typeof(TRequest).Name;
        
        _logger.LogInformation("开始事务处理 {RequestName}", requestName);

        try
        {
            // 这里可以集成具体的事务管理器（如Entity Framework的事务）
            var response = await next();
            
            _logger.LogInformation("事务处理成功 {RequestName}", requestName);
            
            return response;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "事务处理失败 {RequestName}", requestName);
            throw;
        }
    }

    /// <summary>
    /// 检查是否为泛型命令
    /// </summary>
    /// <param name="request">请求</param>
    /// <returns>是否为泛型命令</returns>
    private static bool IsGenericCommand(object request)
    {
        var requestType = request.GetType();
        var interfaces = requestType.GetInterfaces();
        
        return interfaces.Any(i => 
            i.IsGenericType && 
            i.GetGenericTypeDefinition() == typeof(ICommand<>));
    }
}
