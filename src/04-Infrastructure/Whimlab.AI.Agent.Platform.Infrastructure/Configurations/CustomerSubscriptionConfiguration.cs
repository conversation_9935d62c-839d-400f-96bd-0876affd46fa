using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Whimlab.AI.Agent.Platform.Domain.Subscription.Entities;

namespace Whimlab.AI.Agent.Platform.Infrastructure.Configurations;

/// <summary>
/// 客户订阅实体配置
/// </summary>
public class CustomerSubscriptionConfiguration : IEntityTypeConfiguration<CustomerSubscription>
{
    /// <summary>
    /// 配置客户订阅实体
    /// </summary>
    /// <param name="builder">实体类型构建器</param>
    public void Configure(EntityTypeBuilder<CustomerSubscription> builder)
    {
        // 表名
        builder.ToTable("CustomerSubscriptions", "Subscription");

        // 主键
        builder.HasKey(cs => cs.Id);

        // 属性配置
        builder.Property(cs => cs.Id)
            .IsRequired()
            .HasComment("订阅ID");

        builder.Property(cs => cs.CustomerId)
            .IsRequired()
            .HasComment("客户ID");

        builder.Property(cs => cs.SubscriptionPlanId)
            .IsRequired()
            .HasComment("订阅计划ID");

        builder.Property(cs => cs.Status)
            .IsRequired()
            .HasConversion<string>()
            .HasMaxLength(50)
            .HasComment("订阅状态");

        builder.Property(cs => cs.StartDate)
            .IsRequired()
            .HasComment("开始日期");

        builder.Property(cs => cs.EndDate)
            .HasComment("结束日期");

        builder.Property(cs => cs.AutoRenew)
            .IsRequired()
            .HasDefaultValue(true)
            .HasComment("是否自动续费");

        builder.Property(cs => cs.BillingCycle)
            .IsRequired()
            .HasConversion<string>()
            .HasMaxLength(50)
            .HasComment("计费周期");

        builder.Property(cs => cs.Amount)
            .IsRequired()
            .HasPrecision(18, 2)
            .HasComment("订阅金额");

        builder.Property(cs => cs.Currency)
            .IsRequired()
            .HasMaxLength(3)
            .HasDefaultValue("CNY")
            .HasComment("货币代码");

        builder.Property(cs => cs.NextBillingDate)
            .HasComment("下次计费日期");

        builder.Property(cs => cs.CancelledAt)
            .HasComment("取消时间");

        builder.Property(cs => cs.CancellationReason)
            .HasMaxLength(500)
            .HasComment("取消原因");

        builder.Property(cs => cs.CreatedAt)
            .IsRequired()
            .HasDefaultValueSql("GETUTCDATE()")
            .HasComment("创建时间");

        builder.Property(cs => cs.UpdatedAt)
            .HasComment("更新时间");

        // 索引
        builder.HasIndex(cs => cs.CustomerId)
            .HasDatabaseName("IX_CustomerSubscriptions_CustomerId");

        builder.HasIndex(cs => cs.SubscriptionPlanId)
            .HasDatabaseName("IX_CustomerSubscriptions_SubscriptionPlanId");

        builder.HasIndex(cs => cs.Status)
            .HasDatabaseName("IX_CustomerSubscriptions_Status");

        builder.HasIndex(cs => cs.StartDate)
            .HasDatabaseName("IX_CustomerSubscriptions_StartDate");

        builder.HasIndex(cs => cs.EndDate)
            .HasDatabaseName("IX_CustomerSubscriptions_EndDate");

        builder.HasIndex(cs => cs.NextBillingDate)
            .HasDatabaseName("IX_CustomerSubscriptions_NextBillingDate");

        // 复合索引
        builder.HasIndex(cs => new { cs.CustomerId, cs.Status })
            .HasDatabaseName("IX_CustomerSubscriptions_CustomerId_Status");

        builder.HasIndex(cs => new { cs.Status, cs.EndDate })
            .HasDatabaseName("IX_CustomerSubscriptions_Status_EndDate");

        // 关系配置
        builder.HasOne(cs => cs.SubscriptionPlan)
            .WithMany()
            .HasForeignKey(cs => cs.SubscriptionPlanId)
            .OnDelete(DeleteBehavior.Restrict);

        builder.HasMany(cs => cs.QuotaUsageRecords)
            .WithOne()
            .HasForeignKey(qur => qur.CustomerSubscriptionId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}
