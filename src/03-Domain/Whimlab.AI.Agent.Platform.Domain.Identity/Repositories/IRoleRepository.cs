using Whimlab.AI.Agent.Platform.Domain.Identity.Entities;

namespace Whimlab.AI.Agent.Platform.Domain.Identity.Repositories;

/// <summary>
/// 角色仓储接口
/// </summary>
public interface IRoleRepository
{
    /// <summary>
    /// 根据ID获取角色
    /// </summary>
    /// <param name="id">角色ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>角色实体</returns>
    Task<Role?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default);

    /// <summary>
    /// 根据名称获取角色
    /// </summary>
    /// <param name="name">角色名称</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>角色实体</returns>
    Task<Role?> GetByNameAsync(string name, CancellationToken cancellationToken = default);

    /// <summary>
    /// 检查角色名称是否存在
    /// </summary>
    /// <param name="name">角色名称</param>
    /// <param name="excludeRoleId">排除的角色ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>是否存在</returns>
    Task<bool> IsNameExistsAsync(string name, Guid? excludeRoleId = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取角色列表
    /// </summary>
    /// <param name="isActive">是否激活</param>
    /// <param name="includeSystemRoles">是否包含系统角色</param>
    /// <param name="searchTerm">搜索关键词</param>
    /// <param name="pageNumber">页码</param>
    /// <param name="pageSize">页大小</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>角色列表</returns>
    Task<(IEnumerable<Role> Roles, int TotalCount)> GetRolesAsync(
        bool? isActive = null,
        bool includeSystemRoles = true,
        string? searchTerm = null,
        int pageNumber = 1,
        int pageSize = 20,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取角色的权限
    /// </summary>
    /// <param name="roleId">角色ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>权限列表</returns>
    Task<IEnumerable<Permission>> GetRolePermissionsAsync(Guid roleId, CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取拥有指定角色的用户
    /// </summary>
    /// <param name="roleId">角色ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>用户列表</returns>
    Task<IEnumerable<User>> GetUsersInRoleAsync(Guid roleId, CancellationToken cancellationToken = default);

    /// <summary>
    /// 添加角色
    /// </summary>
    /// <param name="role">角色实体</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>任务</returns>
    Task AddAsync(Role role, CancellationToken cancellationToken = default);

    /// <summary>
    /// 更新角色
    /// </summary>
    /// <param name="role">角色实体</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>任务</returns>
    Task UpdateAsync(Role role, CancellationToken cancellationToken = default);

    /// <summary>
    /// 删除角色
    /// </summary>
    /// <param name="role">角色实体</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>任务</returns>
    Task DeleteAsync(Role role, CancellationToken cancellationToken = default);

    /// <summary>
    /// 保存更改
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>受影响的行数</returns>
    Task<int> SaveChangesAsync(CancellationToken cancellationToken = default);
}

/// <summary>
/// 权限仓储接口
/// </summary>
public interface IPermissionRepository
{
    /// <summary>
    /// 根据ID获取权限
    /// </summary>
    /// <param name="id">权限ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>权限实体</returns>
    Task<Permission?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default);

    /// <summary>
    /// 根据名称获取权限
    /// </summary>
    /// <param name="name">权限名称</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>权限实体</returns>
    Task<Permission?> GetByNameAsync(string name, CancellationToken cancellationToken = default);

    /// <summary>
    /// 检查权限名称是否存在
    /// </summary>
    /// <param name="name">权限名称</param>
    /// <param name="excludePermissionId">排除的权限ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>是否存在</returns>
    Task<bool> IsNameExistsAsync(string name, Guid? excludePermissionId = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取权限列表
    /// </summary>
    /// <param name="group">权限分组</param>
    /// <param name="isActive">是否激活</param>
    /// <param name="includeSystemPermissions">是否包含系统权限</param>
    /// <param name="searchTerm">搜索关键词</param>
    /// <param name="pageNumber">页码</param>
    /// <param name="pageSize">页大小</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>权限列表</returns>
    Task<(IEnumerable<Permission> Permissions, int TotalCount)> GetPermissionsAsync(
        string? group = null,
        bool? isActive = null,
        bool includeSystemPermissions = true,
        string? searchTerm = null,
        int pageNumber = 1,
        int pageSize = 20,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取权限分组列表
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>分组列表</returns>
    Task<IEnumerable<string>> GetGroupsAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 添加权限
    /// </summary>
    /// <param name="permission">权限实体</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>任务</returns>
    Task AddAsync(Permission permission, CancellationToken cancellationToken = default);

    /// <summary>
    /// 更新权限
    /// </summary>
    /// <param name="permission">权限实体</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>任务</returns>
    Task UpdateAsync(Permission permission, CancellationToken cancellationToken = default);

    /// <summary>
    /// 删除权限
    /// </summary>
    /// <param name="permission">权限实体</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>任务</returns>
    Task DeleteAsync(Permission permission, CancellationToken cancellationToken = default);

    /// <summary>
    /// 保存更改
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>受影响的行数</returns>
    Task<int> SaveChangesAsync(CancellationToken cancellationToken = default);
}
