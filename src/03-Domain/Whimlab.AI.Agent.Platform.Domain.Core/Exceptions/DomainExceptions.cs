using System.Runtime.Serialization;
using Whimlab.AI.Agent.Platform.Domain.Core.Common;
using Whimlab.AI.Agent.Platform.Shared.Exceptions.Base;
using DomainValidationError = Whimlab.AI.Agent.Platform.Domain.Core.Common.ValidationError;
using SharedValidationError = Whimlab.AI.Agent.Platform.Shared.Exceptions.Base.ValidationError;

namespace Whimlab.AI.Agent.Platform.Domain.Core.Exceptions;

/// <summary>
/// 领域验证异常
/// </summary>
[Serializable]
public class DomainValidationException : ValidationException
{
    /// <summary>
    /// 初始化领域验证异常
    /// </summary>
    /// <param name="validationErrors">验证错误列表</param>
    public DomainValidationException(IEnumerable<DomainValidationError> validationErrors)
        : base("DOMAIN_VALIDATION_FAILED", "领域验证失败", validationErrors.Select(e => new SharedValidationError(e.PropertyName, e.ErrorMessage, e.ErrorCode)))
    {
    }

    /// <summary>
    /// 初始化领域验证异常
    /// </summary>
    /// <param name="validationError">验证错误</param>
    public DomainValidationException(DomainValidationError validationError)
        : this(new[] { validationError })
    {
    }

    /// <summary>
    /// 初始化领域验证异常
    /// </summary>
    /// <param name="propertyName">属性名称</param>
    /// <param name="errorMessage">错误消息</param>
    /// <param name="errorCode">错误代码</param>
    public DomainValidationException(string propertyName, string errorMessage, string? errorCode = null)
        : this(new DomainValidationError(propertyName, errorMessage, errorCode))
    {
    }

    /// <summary>
    /// 序列化构造函数
    /// </summary>
    /// <param name="info">序列化信息</param>
    /// <param name="context">流上下文</param>
    [Obsolete("This API supports obsolete formatter-based serialization. It should not be called or extended by application code.", DiagnosticId = "SYSLIB0051", UrlFormat = "https://aka.ms/dotnet-warnings/{0}")]
    protected DomainValidationException(SerializationInfo info, StreamingContext context)
        : base(info, context)
    {
    }
}

/// <summary>
/// 业务规则验证异常
/// </summary>
[Serializable]
public class BusinessRuleValidationException : BusinessException
{
    /// <summary>
    /// 违反的业务规则
    /// </summary>
    public IReadOnlyList<IBusinessRule> ViolatedRules { get; }

    /// <summary>
    /// 初始化业务规则验证异常
    /// </summary>
    /// <param name="violatedRule">违反的业务规则</param>
    public BusinessRuleValidationException(IBusinessRule violatedRule)
        : base(violatedRule.ErrorCode, violatedRule.Message, violatedRule)
    {
        ViolatedRules = new[] { violatedRule };
    }

    /// <summary>
    /// 初始化业务规则验证异常
    /// </summary>
    /// <param name="violatedRules">违反的业务规则列表</param>
    public BusinessRuleValidationException(IEnumerable<IBusinessRule> violatedRules)
        : base("BUSINESS_RULES_VIOLATED", "业务规则验证失败", violatedRules)
    {
        ViolatedRules = violatedRules.ToList().AsReadOnly();
    }

    /// <summary>
    /// 序列化构造函数
    /// </summary>
    /// <param name="info">序列化信息</param>
    /// <param name="context">流上下文</param>
    [Obsolete("This API supports obsolete formatter-based serialization. It should not be called or extended by application code.", DiagnosticId = "SYSLIB0051", UrlFormat = "https://aka.ms/dotnet-warnings/{0}")]
    protected BusinessRuleValidationException(SerializationInfo info, StreamingContext context)
        : base(info, context)
    {
        var rules = info.GetValue(nameof(ViolatedRules), typeof(List<IBusinessRule>)) as List<IBusinessRule>;
        ViolatedRules = rules?.AsReadOnly() ?? new List<IBusinessRule>().AsReadOnly();
    }

    /// <summary>
    /// 获取对象数据
    /// </summary>
    /// <param name="info">序列化信息</param>
    /// <param name="context">流上下文</param>
    [Obsolete("This API supports obsolete formatter-based serialization. It should not be called or extended by application code.", DiagnosticId = "SYSLIB0051", UrlFormat = "https://aka.ms/dotnet-warnings/{0}")]
    public override void GetObjectData(SerializationInfo info, StreamingContext context)
    {
        base.GetObjectData(info, context);
        info.AddValue(nameof(ViolatedRules), ViolatedRules.ToList());
    }
}

/// <summary>
/// 实体未找到异常
/// </summary>
[Serializable]
public class EntityNotFoundException : BusinessException
{
    /// <summary>
    /// 实体类型
    /// </summary>
    public string EntityType { get; }

    /// <summary>
    /// 实体标识符
    /// </summary>
    public object EntityId { get; }

    /// <summary>
    /// 初始化实体未找到异常
    /// </summary>
    /// <param name="entityType">实体类型</param>
    /// <param name="entityId">实体标识符</param>
    public EntityNotFoundException(string entityType, object entityId)
        : base("ENTITY_NOT_FOUND", $"未找到类型为 {entityType} 且标识符为 {entityId} 的实体", new { EntityType = entityType, EntityId = entityId })
    {
        EntityType = entityType;
        EntityId = entityId;
    }

    /// <summary>
    /// 初始化实体未找到异常
    /// </summary>
    /// <typeparam name="T">实体类型</typeparam>
    /// <param name="entityId">实体标识符</param>
    public static EntityNotFoundException For<T>(object entityId)
    {
        return new EntityNotFoundException(typeof(T).Name, entityId);
    }

    /// <summary>
    /// 序列化构造函数
    /// </summary>
    /// <param name="info">序列化信息</param>
    /// <param name="context">流上下文</param>
    [Obsolete("This API supports obsolete formatter-based serialization. It should not be called or extended by application code.", DiagnosticId = "SYSLIB0051", UrlFormat = "https://aka.ms/dotnet-warnings/{0}")]
    protected EntityNotFoundException(SerializationInfo info, StreamingContext context)
        : base(info, context)
    {
        EntityType = info.GetString(nameof(EntityType)) ?? string.Empty;
        EntityId = info.GetValue(nameof(EntityId), typeof(object)) ?? new object();
    }

    /// <summary>
    /// 获取对象数据
    /// </summary>
    /// <param name="info">序列化信息</param>
    /// <param name="context">流上下文</param>
    [Obsolete("This API supports obsolete formatter-based serialization. It should not be called or extended by application code.", DiagnosticId = "SYSLIB0051", UrlFormat = "https://aka.ms/dotnet-warnings/{0}")]
    public override void GetObjectData(SerializationInfo info, StreamingContext context)
    {
        base.GetObjectData(info, context);
        info.AddValue(nameof(EntityType), EntityType);
        info.AddValue(nameof(EntityId), EntityId);
    }
}

/// <summary>
/// 实体已存在异常
/// </summary>
[Serializable]
public class EntityAlreadyExistsException : BusinessException
{
    /// <summary>
    /// 实体类型
    /// </summary>
    public string EntityType { get; }

    /// <summary>
    /// 冲突的属性
    /// </summary>
    public string ConflictProperty { get; }

    /// <summary>
    /// 冲突的值
    /// </summary>
    public object ConflictValue { get; }

    /// <summary>
    /// 初始化实体已存在异常
    /// </summary>
    /// <param name="entityType">实体类型</param>
    /// <param name="conflictProperty">冲突的属性</param>
    /// <param name="conflictValue">冲突的值</param>
    public EntityAlreadyExistsException(string entityType, string conflictProperty, object conflictValue)
        : base("ENTITY_ALREADY_EXISTS", $"类型为 {entityType} 且 {conflictProperty} 为 {conflictValue} 的实体已存在", 
               new { EntityType = entityType, ConflictProperty = conflictProperty, ConflictValue = conflictValue })
    {
        EntityType = entityType;
        ConflictProperty = conflictProperty;
        ConflictValue = conflictValue;
    }

    /// <summary>
    /// 初始化实体已存在异常
    /// </summary>
    /// <typeparam name="T">实体类型</typeparam>
    /// <param name="conflictProperty">冲突的属性</param>
    /// <param name="conflictValue">冲突的值</param>
    public static EntityAlreadyExistsException For<T>(string conflictProperty, object conflictValue)
    {
        return new EntityAlreadyExistsException(typeof(T).Name, conflictProperty, conflictValue);
    }

    /// <summary>
    /// 序列化构造函数
    /// </summary>
    /// <param name="info">序列化信息</param>
    /// <param name="context">流上下文</param>
    [Obsolete("This API supports obsolete formatter-based serialization. It should not be called or extended by application code.", DiagnosticId = "SYSLIB0051", UrlFormat = "https://aka.ms/dotnet-warnings/{0}")]
    protected EntityAlreadyExistsException(SerializationInfo info, StreamingContext context)
        : base(info, context)
    {
        EntityType = info.GetString(nameof(EntityType)) ?? string.Empty;
        ConflictProperty = info.GetString(nameof(ConflictProperty)) ?? string.Empty;
        ConflictValue = info.GetValue(nameof(ConflictValue), typeof(object)) ?? new object();
    }

    /// <summary>
    /// 获取对象数据
    /// </summary>
    /// <param name="info">序列化信息</param>
    /// <param name="context">流上下文</param>
    [Obsolete("This API supports obsolete formatter-based serialization. It should not be called or extended by application code.", DiagnosticId = "SYSLIB0051", UrlFormat = "https://aka.ms/dotnet-warnings/{0}")]
    public override void GetObjectData(SerializationInfo info, StreamingContext context)
    {
        base.GetObjectData(info, context);
        info.AddValue(nameof(EntityType), EntityType);
        info.AddValue(nameof(ConflictProperty), ConflictProperty);
        info.AddValue(nameof(ConflictValue), ConflictValue);
    }
}

/// <summary>
/// 并发冲突异常
/// </summary>
[Serializable]
public class ConcurrencyConflictException : BusinessException
{
    /// <summary>
    /// 实体类型
    /// </summary>
    public string EntityType { get; }

    /// <summary>
    /// 实体标识符
    /// </summary>
    public object EntityId { get; }

    /// <summary>
    /// 期望的版本号
    /// </summary>
    public long ExpectedVersion { get; }

    /// <summary>
    /// 实际的版本号
    /// </summary>
    public long ActualVersion { get; }

    /// <summary>
    /// 初始化并发冲突异常
    /// </summary>
    /// <param name="entityType">实体类型</param>
    /// <param name="entityId">实体标识符</param>
    /// <param name="expectedVersion">期望的版本号</param>
    /// <param name="actualVersion">实际的版本号</param>
    public ConcurrencyConflictException(string entityType, object entityId, long expectedVersion, long actualVersion)
        : base("CONCURRENCY_CONFLICT", $"实体 {entityType}({entityId}) 发生并发冲突，期望版本 {expectedVersion}，实际版本 {actualVersion}", 
               new { EntityType = entityType, EntityId = entityId, ExpectedVersion = expectedVersion, ActualVersion = actualVersion })
    {
        EntityType = entityType;
        EntityId = entityId;
        ExpectedVersion = expectedVersion;
        ActualVersion = actualVersion;
    }

    /// <summary>
    /// 初始化并发冲突异常
    /// </summary>
    /// <typeparam name="T">实体类型</typeparam>
    /// <param name="entityId">实体标识符</param>
    /// <param name="expectedVersion">期望的版本号</param>
    /// <param name="actualVersion">实际的版本号</param>
    public static ConcurrencyConflictException For<T>(object entityId, long expectedVersion, long actualVersion)
    {
        return new ConcurrencyConflictException(typeof(T).Name, entityId, expectedVersion, actualVersion);
    }

    /// <summary>
    /// 序列化构造函数
    /// </summary>
    /// <param name="info">序列化信息</param>
    /// <param name="context">流上下文</param>
    [Obsolete("This API supports obsolete formatter-based serialization. It should not be called or extended by application code.", DiagnosticId = "SYSLIB0051", UrlFormat = "https://aka.ms/dotnet-warnings/{0}")]
    protected ConcurrencyConflictException(SerializationInfo info, StreamingContext context)
        : base(info, context)
    {
        EntityType = info.GetString(nameof(EntityType)) ?? string.Empty;
        EntityId = info.GetValue(nameof(EntityId), typeof(object)) ?? new object();
        ExpectedVersion = info.GetInt64(nameof(ExpectedVersion));
        ActualVersion = info.GetInt64(nameof(ActualVersion));
    }

    /// <summary>
    /// 获取对象数据
    /// </summary>
    /// <param name="info">序列化信息</param>
    /// <param name="context">流上下文</param>
    [Obsolete("This API supports obsolete formatter-based serialization. It should not be called or extended by application code.", DiagnosticId = "SYSLIB0051", UrlFormat = "https://aka.ms/dotnet-warnings/{0}")]
    public override void GetObjectData(SerializationInfo info, StreamingContext context)
    {
        base.GetObjectData(info, context);
        info.AddValue(nameof(EntityType), EntityType);
        info.AddValue(nameof(EntityId), EntityId);
        info.AddValue(nameof(ExpectedVersion), ExpectedVersion);
        info.AddValue(nameof(ActualVersion), ActualVersion);
    }
}
