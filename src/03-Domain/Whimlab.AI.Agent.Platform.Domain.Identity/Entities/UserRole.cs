using Whimlab.AI.Agent.Platform.Domain.Core.Common;

namespace Whimlab.AI.Agent.Platform.Domain.Identity.Entities;

/// <summary>
/// 用户角色关联实体
/// </summary>
public class UserRole : Entity
{
    /// <summary>
    /// 用户ID
    /// </summary>
    public Guid UserId { get; private set; }

    /// <summary>
    /// 角色ID
    /// </summary>
    public Guid RoleId { get; private set; }

    /// <summary>
    /// 分配时间
    /// </summary>
    public DateTime AssignedAt { get; private set; }

    /// <summary>
    /// 分配者ID
    /// </summary>
    public Guid? AssignedBy { get; private set; }

    /// <summary>
    /// 过期时间（可选）
    /// </summary>
    public DateTime? ExpiresAt { get; private set; }

    /// <summary>
    /// 是否激活
    /// </summary>
    public bool IsActive { get; private set; }

    /// <summary>
    /// 用户导航属性
    /// </summary>
    public User? User { get; private set; }

    /// <summary>
    /// 角色导航属性
    /// </summary>
    public Role? Role { get; private set; }

    /// <summary>
    /// 私有构造函数（用于EF Core）
    /// </summary>
    private UserRole() { }

    /// <summary>
    /// 创建用户角色关联
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="roleId">角色ID</param>
    /// <param name="assignedBy">分配者ID</param>
    /// <param name="expiresAt">过期时间</param>
    /// <returns>用户角色关联实体</returns>
    public static UserRole Create(Guid userId, Guid roleId, Guid? assignedBy = null, DateTime? expiresAt = null)
    {
        return new UserRole
        {
            UserId = userId,
            RoleId = roleId,
            AssignedAt = DateTime.UtcNow,
            AssignedBy = assignedBy,
            ExpiresAt = expiresAt,
            IsActive = true
        };
    }

    /// <summary>
    /// 检查角色是否已过期
    /// </summary>
    /// <returns>是否已过期</returns>
    public bool IsExpired()
    {
        return ExpiresAt.HasValue && ExpiresAt.Value < DateTime.UtcNow;
    }

    /// <summary>
    /// 激活角色
    /// </summary>
    public void Activate()
    {
        if (IsActive) return;

        IsActive = true;
        MarkAsModified();
    }

    /// <summary>
    /// 停用角色
    /// </summary>
    public void Deactivate()
    {
        if (!IsActive) return;

        IsActive = false;
        MarkAsModified();
    }

    /// <summary>
    /// 延长过期时间
    /// </summary>
    /// <param name="newExpiresAt">新的过期时间</param>
    public void ExtendExpiry(DateTime? newExpiresAt)
    {
        ExpiresAt = newExpiresAt;
        MarkAsModified();
    }

    /// <summary>
    /// 检查角色是否有效
    /// </summary>
    /// <returns>是否有效</returns>
    public bool IsValid()
    {
        return IsActive && !IsExpired();
    }
}
