using Whimlab.AI.Agent.Platform.Domain.Core.Common;
using Whimlab.AI.Agent.Platform.Domain.Identity.Events;

namespace Whimlab.AI.Agent.Platform.Domain.Identity.Entities;

/// <summary>
/// 角色聚合根
/// </summary>
public class Role : AggregateRoot
{
    private readonly List<RolePermission> _rolePermissions = new();

    /// <summary>
    /// 角色名称
    /// </summary>
    public string Name { get; private set; } = string.Empty;

    /// <summary>
    /// 角色显示名称
    /// </summary>
    public string DisplayName { get; private set; } = string.Empty;

    /// <summary>
    /// 角色描述
    /// </summary>
    public string? Description { get; private set; }

    /// <summary>
    /// 是否为系统角色
    /// </summary>
    public bool IsSystemRole { get; private set; }

    /// <summary>
    /// 是否激活
    /// </summary>
    public bool IsActive { get; private set; }

    /// <summary>
    /// 角色权限集合（只读）
    /// </summary>
    public IReadOnlyList<RolePermission> RolePermissions => _rolePermissions.AsReadOnly();

    /// <summary>
    /// 私有构造函数（用于EF Core）
    /// </summary>
    private Role() { }

    /// <summary>
    /// 创建新角色
    /// </summary>
    /// <param name="name">角色名称</param>
    /// <param name="displayName">角色显示名称</param>
    /// <param name="description">角色描述</param>
    /// <param name="isSystemRole">是否为系统角色</param>
    /// <returns>角色实体</returns>
    public static Role Create(string name, string displayName, string? description = null, bool isSystemRole = false)
    {
        var role = new Role
        {
            Name = name,
            DisplayName = displayName,
            Description = description,
            IsSystemRole = isSystemRole,
            IsActive = true
        };

        role.AddDomainEvent(new RoleCreatedEvent(role.Id, role.Name));

        return role;
    }

    /// <summary>
    /// 更新角色信息
    /// </summary>
    /// <param name="displayName">角色显示名称</param>
    /// <param name="description">角色描述</param>
    /// <exception cref="InvalidOperationException">系统角色不能修改</exception>
    public void Update(string displayName, string? description = null)
    {
        if (IsSystemRole)
            throw new InvalidOperationException("系统角色不能修改");

        DisplayName = displayName;
        Description = description;
        MarkAsModified();

        AddDomainEvent(new RoleUpdatedEvent(Id, Name));
    }

    /// <summary>
    /// 分配权限
    /// </summary>
    /// <param name="permission">权限</param>
    /// <exception cref="InvalidOperationException">权限已存在</exception>
    public void AssignPermission(Permission permission)
    {
        if (_rolePermissions.Any(rp => rp.PermissionId == permission.Id))
            throw new InvalidOperationException($"角色 {Name} 已拥有权限 {permission.Name}");

        var rolePermission = RolePermission.Create(Id, permission.Id);
        _rolePermissions.Add(rolePermission);
        MarkAsModified();

        AddDomainEvent(new RolePermissionAssignedEvent(Id, permission.Id, permission.Name));
    }

    /// <summary>
    /// 移除权限
    /// </summary>
    /// <param name="permission">权限</param>
    /// <exception cref="InvalidOperationException">权限不存在</exception>
    public void RemovePermission(Permission permission)
    {
        var rolePermission = _rolePermissions.FirstOrDefault(rp => rp.PermissionId == permission.Id);
        if (rolePermission == null)
            throw new InvalidOperationException($"角色 {Name} 不拥有权限 {permission.Name}");

        _rolePermissions.Remove(rolePermission);
        MarkAsModified();

        AddDomainEvent(new RolePermissionRemovedEvent(Id, permission.Id, permission.Name));
    }

    /// <summary>
    /// 检查是否拥有权限
    /// </summary>
    /// <param name="permissionName">权限名称</param>
    /// <returns>是否拥有权限</returns>
    public bool HasPermission(string permissionName)
    {
        return _rolePermissions.Any(rp => rp.Permission?.Name == permissionName);
    }

    /// <summary>
    /// 激活角色
    /// </summary>
    public void Activate()
    {
        if (IsActive) return;

        IsActive = true;
        MarkAsModified();
    }

    /// <summary>
    /// 停用角色
    /// </summary>
    /// <exception cref="InvalidOperationException">系统角色不能停用</exception>
    public void Deactivate()
    {
        if (IsSystemRole)
            throw new InvalidOperationException("系统角色不能停用");

        if (!IsActive) return;

        IsActive = false;
        MarkAsModified();
    }

    /// <summary>
    /// 删除角色
    /// </summary>
    /// <exception cref="InvalidOperationException">系统角色不能删除</exception>
    public void Delete()
    {
        if (IsSystemRole)
            throw new InvalidOperationException("系统角色不能删除");

        AddDomainEvent(new RoleDeletedEvent(Id, Name));
    }
}

/// <summary>
/// 角色权限关联实体
/// </summary>
public class RolePermission : Entity
{
    /// <summary>
    /// 角色ID
    /// </summary>
    public Guid RoleId { get; private set; }

    /// <summary>
    /// 权限ID
    /// </summary>
    public Guid PermissionId { get; private set; }

    /// <summary>
    /// 角色导航属性
    /// </summary>
    public Role? Role { get; private set; }

    /// <summary>
    /// 权限导航属性
    /// </summary>
    public Permission? Permission { get; private set; }

    /// <summary>
    /// 私有构造函数（用于EF Core）
    /// </summary>
    private RolePermission() { }

    /// <summary>
    /// 创建角色权限关联
    /// </summary>
    /// <param name="roleId">角色ID</param>
    /// <param name="permissionId">权限ID</param>
    /// <returns>角色权限关联实体</returns>
    public static RolePermission Create(Guid roleId, Guid permissionId)
    {
        return new RolePermission
        {
            RoleId = roleId,
            PermissionId = permissionId
        };
    }
}
