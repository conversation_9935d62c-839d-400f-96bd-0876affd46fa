using Whimlab.AI.Agent.Platform.Domain.Core.Common;

namespace Whimlab.AI.Agent.Platform.Domain.Subscription.Entities;

/// <summary>
/// 计划功能实体
/// </summary>
public class PlanFeature : Entity
{
    /// <summary>
    /// 订阅计划ID
    /// </summary>
    public Guid SubscriptionPlanId { get; private set; }

    /// <summary>
    /// 功能名称
    /// </summary>
    public string Name { get; private set; } = string.Empty;

    /// <summary>
    /// 功能值
    /// </summary>
    public string? Value { get; private set; }

    /// <summary>
    /// 功能描述
    /// </summary>
    public string? Description { get; private set; }

    /// <summary>
    /// 是否启用
    /// </summary>
    public bool IsEnabled { get; private set; }

    /// <summary>
    /// 排序顺序
    /// </summary>
    public int SortOrder { get; private set; }

    /// <summary>
    /// 订阅计划导航属性
    /// </summary>
    public SubscriptionPlan? SubscriptionPlan { get; private set; }

    /// <summary>
    /// 私有构造函数（用于EF Core）
    /// </summary>
    private PlanFeature() { }

    /// <summary>
    /// 创建计划功能
    /// </summary>
    /// <param name="subscriptionPlanId">订阅计划ID</param>
    /// <param name="name">功能名称</param>
    /// <param name="value">功能值</param>
    /// <param name="description">功能描述</param>
    /// <param name="sortOrder">排序顺序</param>
    /// <returns>计划功能实体</returns>
    public static PlanFeature Create(
        Guid subscriptionPlanId,
        string name,
        string? value = null,
        string? description = null,
        int sortOrder = 0)
    {
        if (subscriptionPlanId == Guid.Empty)
            throw new ArgumentException("订阅计划ID不能为空", nameof(subscriptionPlanId));

        if (string.IsNullOrWhiteSpace(name))
            throw new ArgumentException("功能名称不能为空", nameof(name));

        return new PlanFeature
        {
            SubscriptionPlanId = subscriptionPlanId,
            Name = name.Trim(),
            Value = value?.Trim(),
            Description = description?.Trim(),
            IsEnabled = true,
            SortOrder = sortOrder
        };
    }

    /// <summary>
    /// 更新功能信息
    /// </summary>
    /// <param name="value">功能值</param>
    /// <param name="description">功能描述</param>
    /// <param name="sortOrder">排序顺序</param>
    public void Update(string? value = null, string? description = null, int? sortOrder = null)
    {
        Value = value?.Trim();
        Description = description?.Trim();
        
        if (sortOrder.HasValue)
            SortOrder = sortOrder.Value;

        MarkAsModified();
    }

    /// <summary>
    /// 启用功能
    /// </summary>
    public void Enable()
    {
        IsEnabled = true;
        MarkAsModified();
    }

    /// <summary>
    /// 禁用功能
    /// </summary>
    public void Disable()
    {
        IsEnabled = false;
        MarkAsModified();
    }
}

/// <summary>
/// 计划配额实体
/// </summary>
public class PlanQuota : Entity
{
    /// <summary>
    /// 订阅计划ID
    /// </summary>
    public Guid SubscriptionPlanId { get; private set; }

    /// <summary>
    /// 配额类型
    /// </summary>
    public string QuotaType { get; private set; } = string.Empty;

    /// <summary>
    /// 配额限制
    /// </summary>
    public long Limit { get; private set; }

    /// <summary>
    /// 配额周期
    /// </summary>
    public string Period { get; private set; } = string.Empty;

    /// <summary>
    /// 配额描述
    /// </summary>
    public string? Description { get; private set; }

    /// <summary>
    /// 是否启用
    /// </summary>
    public bool IsEnabled { get; private set; }

    /// <summary>
    /// 订阅计划导航属性
    /// </summary>
    public SubscriptionPlan? SubscriptionPlan { get; private set; }

    /// <summary>
    /// 私有构造函数（用于EF Core）
    /// </summary>
    private PlanQuota() { }

    /// <summary>
    /// 创建计划配额
    /// </summary>
    /// <param name="subscriptionPlanId">订阅计划ID</param>
    /// <param name="quotaType">配额类型</param>
    /// <param name="limit">配额限制</param>
    /// <param name="period">配额周期</param>
    /// <param name="description">配额描述</param>
    /// <returns>计划配额实体</returns>
    public static PlanQuota Create(
        Guid subscriptionPlanId,
        string quotaType,
        long limit,
        string period = "monthly",
        string? description = null)
    {
        if (subscriptionPlanId == Guid.Empty)
            throw new ArgumentException("订阅计划ID不能为空", nameof(subscriptionPlanId));

        if (string.IsNullOrWhiteSpace(quotaType))
            throw new ArgumentException("配额类型不能为空", nameof(quotaType));

        if (limit < 0)
            throw new ArgumentException("配额限制不能为负数", nameof(limit));

        if (string.IsNullOrWhiteSpace(period))
            throw new ArgumentException("配额周期不能为空", nameof(period));

        return new PlanQuota
        {
            SubscriptionPlanId = subscriptionPlanId,
            QuotaType = quotaType.Trim(),
            Limit = limit,
            Period = period.Trim().ToLowerInvariant(),
            Description = description?.Trim(),
            IsEnabled = true
        };
    }

    /// <summary>
    /// 更新配额限制
    /// </summary>
    /// <param name="limit">新的配额限制</param>
    public void UpdateLimit(long limit)
    {
        if (limit < 0)
            throw new ArgumentException("配额限制不能为负数", nameof(limit));

        Limit = limit;
        MarkAsModified();
    }

    /// <summary>
    /// 更新配额信息
    /// </summary>
    /// <param name="limit">配额限制</param>
    /// <param name="period">配额周期</param>
    /// <param name="description">配额描述</param>
    public void Update(long? limit = null, string? period = null, string? description = null)
    {
        if (limit.HasValue)
        {
            if (limit.Value < 0)
                throw new ArgumentException("配额限制不能为负数", nameof(limit));
            Limit = limit.Value;
        }

        if (!string.IsNullOrWhiteSpace(period))
            Period = period.Trim().ToLowerInvariant();

        Description = description?.Trim();

        MarkAsModified();
    }

    /// <summary>
    /// 启用配额
    /// </summary>
    public void Enable()
    {
        IsEnabled = true;
        MarkAsModified();
    }

    /// <summary>
    /// 禁用配额
    /// </summary>
    public void Disable()
    {
        IsEnabled = false;
        MarkAsModified();
    }

    /// <summary>
    /// 检查是否为无限制配额
    /// </summary>
    /// <returns>是否为无限制</returns>
    public bool IsUnlimited()
    {
        return Limit == -1;
    }

    /// <summary>
    /// 设置为无限制
    /// </summary>
    public void SetUnlimited()
    {
        Limit = -1;
        MarkAsModified();
    }
}
