using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System.Linq.Expressions;
using Whimlab.AI.Agent.Platform.Domain.Subscription.Entities;
using Whimlab.AI.Agent.Platform.Domain.Subscription.Repositories;
using Whimlab.AI.Agent.Platform.Infrastructure.Data;

namespace Whimlab.AI.Agent.Platform.Infrastructure.Repositories;

/// <summary>
/// 客户订阅仓储实现
/// </summary>
public class CustomerSubscriptionRepository : BaseRepository<CustomerSubscription, Guid>, ICustomerSubscriptionRepository
{
    /// <summary>
    /// 初始化客户订阅仓储
    /// </summary>
    /// <param name="context">数据库上下文</param>
    /// <param name="logger">日志记录器</param>
    public CustomerSubscriptionRepository(
        AgentPlatformDbContext context,
        ILogger<CustomerSubscriptionRepository> logger) : base(context, logger)
    {
    }

    /// <summary>
    /// 根据客户ID获取订阅
    /// </summary>
    /// <param name="customerId">客户ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>客户订阅</returns>
    public async Task<CustomerSubscription?> GetByCustomerIdAsync(Guid customerId, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("根据客户ID获取订阅: {CustomerId}", customerId);

            return await _dbSet
                .Include(cs => cs.SubscriptionPlan)
                .Include(cs => cs.QuotaUsageRecords)
                .FirstOrDefaultAsync(cs => cs.CustomerId == customerId, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "根据客户ID获取订阅时发生错误: {CustomerId}", customerId);
            throw;
        }
    }

    /// <summary>
    /// 根据订阅计划ID获取订阅列表
    /// </summary>
    /// <param name="planId">订阅计划ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>客户订阅列表</returns>
    public async Task<List<CustomerSubscription>> GetByPlanIdAsync(Guid planId, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("根据订阅计划ID获取订阅列表: {PlanId}", planId);

            return await _dbSet
                .Include(cs => cs.SubscriptionPlan)
                .Where(cs => cs.SubscriptionPlanId == planId)
                .ToListAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "根据订阅计划ID获取订阅列表时发生错误: {PlanId}", planId);
            throw;
        }
    }

    /// <summary>
    /// 获取活跃订阅列表
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>活跃订阅列表</returns>
    public async Task<List<CustomerSubscription>> GetActiveSubscriptionsAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("获取活跃订阅列表");

            return await _dbSet
                .Include(cs => cs.SubscriptionPlan)
                .Where(cs => cs.Status == SubscriptionStatus.Active)
                .ToListAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取活跃订阅列表时发生错误");
            throw;
        }
    }

    /// <summary>
    /// 获取即将到期的订阅列表
    /// </summary>
    /// <param name="days">天数</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>即将到期的订阅列表</returns>
    public async Task<List<CustomerSubscription>> GetExpiringSubscriptionsAsync(int days, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("获取即将到期的订阅列表，天数: {Days}", days);

            var expirationThreshold = DateTime.UtcNow.AddDays(days);

            return await _dbSet
                .Include(cs => cs.SubscriptionPlan)
                .Where(cs => cs.Status == SubscriptionStatus.Active && 
                           cs.EndDate.HasValue && 
                           cs.EndDate.Value <= expirationThreshold)
                .ToListAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取即将到期的订阅列表时发生错误，天数: {Days}", days);
            throw;
        }
    }



    /// <summary>
    /// 获取需要配额重置的订阅列表
    /// </summary>
    /// <param name="quotaType">配额类型</param>
    /// <param name="resetThreshold">重置阈值时间</param>
    /// <param name="batchSize">批次大小</param>
    /// <param name="offset">偏移量</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>需要重置的订阅列表</returns>
    public async Task<List<CustomerSubscription>> GetSubscriptionsForQuotaResetAsync(
        string quotaType,
        DateTime resetThreshold,
        int batchSize,
        int offset,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("获取需要配额重置的订阅列表，配额类型: {QuotaType}，重置阈值: {ResetThreshold}，批次大小: {BatchSize}，偏移量: {Offset}",
                quotaType, resetThreshold, batchSize, offset);

            return await _dbSet
                .Include(cs => cs.SubscriptionPlan)
                .Where(cs => cs.Status == SubscriptionStatus.Active)
                .Where(cs => cs.QuotaUsageRecords.Any(qur => 
                    qur.QuotaType == quotaType && 
                    qur.LastResetAt < resetThreshold))
                .Skip(offset)
                .Take(batchSize)
                .ToListAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取需要配额重置的订阅列表时发生错误，配额类型: {QuotaType}", quotaType);
            throw;
        }
    }

    /// <summary>
    /// 获取配额超限的客户列表
    /// </summary>
    /// <param name="quotaType">配额类型</param>
    /// <param name="batchSize">批次大小</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>超限客户ID列表</returns>
    public async Task<IEnumerable<Guid>> GetOverLimitCustomersAsync(
        string quotaType,
        int batchSize,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("获取配额超限的客户列表，配额类型: {QuotaType}，批次大小: {BatchSize}", quotaType, batchSize);

            return await _dbSet
                .Include(cs => cs.SubscriptionPlan)
                .Include(cs => cs.QuotaUsageRecords)
                .Where(cs => cs.Status == SubscriptionStatus.Active)
                .Where(cs => cs.QuotaUsageRecords.Any(qur => 
                    qur.QuotaType == quotaType && 
                    qur.UsedAmount >= qur.LimitAmount))
                .Take(batchSize)
                .Select(cs => cs.CustomerId)
                .ToListAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取配额超限的客户列表时发生错误，配额类型: {QuotaType}", quotaType);
            throw;
        }
    }

    /// <summary>
    /// 获取配额使用统计
    /// </summary>
    /// <param name="customerId">客户ID</param>
    /// <param name="quotaType">配额类型</param>
    /// <param name="startDate">开始日期</param>
    /// <param name="endDate">结束日期</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>配额使用统计</returns>
    public async Task<QuotaUsageStatistics> GetQuotaUsageStatisticsAsync(
        Guid customerId,
        string quotaType,
        DateTime startDate,
        DateTime endDate,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("获取配额使用统计，客户ID: {CustomerId}，配额类型: {QuotaType}，开始日期: {StartDate}，结束日期: {EndDate}",
                customerId, quotaType, startDate, endDate);

            var subscription = await _dbSet
                .Include(cs => cs.QuotaUsageRecords)
                .FirstOrDefaultAsync(cs => cs.CustomerId == customerId, cancellationToken);

            if (subscription == null)
            {
                return new QuotaUsageStatistics
                {
                    CustomerId = customerId,
                    QuotaType = quotaType,
                    StartDate = startDate,
                    EndDate = endDate,
                    TotalUsage = 0,
                    DailyUsages = new List<DailyUsage>()
                };
            }

            var quotaRecord = subscription.QuotaUsageRecords
                .FirstOrDefault(qur => qur.QuotaType == quotaType);

            if (quotaRecord == null)
            {
                return new QuotaUsageStatistics
                {
                    CustomerId = customerId,
                    QuotaType = quotaType,
                    StartDate = startDate,
                    EndDate = endDate,
                    TotalUsage = 0,
                    DailyUsages = new List<DailyUsage>()
                };
            }

            // 这里应该从详细的使用记录表中获取统计数据
            // 为了简化，这里返回基本统计信息
            return new QuotaUsageStatistics
            {
                CustomerId = customerId,
                QuotaType = quotaType,
                StartDate = startDate,
                EndDate = endDate,
                TotalUsage = quotaRecord.UsedAmount,
                DailyUsages = new List<DailyUsage>
                {
                    new DailyUsage
                    {
                        Date = DateTime.UtcNow.Date,
                        Usage = quotaRecord.UsedAmount
                    }
                }
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取配额使用统计时发生错误，客户ID: {CustomerId}，配额类型: {QuotaType}", customerId, quotaType);
            throw;
        }
    }
}
