using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Whimlab.AI.Agent.Platform.Domain.Core.Interfaces;

namespace Whimlab.AI.Agent.Platform.Infrastructure.Services;

/// <summary>
/// 企业级配置管理服务
/// </summary>
public class ConfigurationService : IConfigurationService
{
    private readonly IConfiguration _configuration;
    private readonly ILogger<ConfigurationService> _logger;
    private readonly ICacheService _cacheService;

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="configuration">配置</param>
    /// <param name="logger">日志记录器</param>
    /// <param name="cacheService">缓存服务</param>
    public ConfigurationService(
        IConfiguration configuration,
        ILogger<ConfigurationService> logger,
        ICacheService cacheService)
    {
        _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _cacheService = cacheService ?? throw new ArgumentNullException(nameof(cacheService));
    }

    /// <summary>
    /// 获取配置值
    /// </summary>
    /// <typeparam name="T">配置类型</typeparam>
    /// <param name="key">配置键</param>
    /// <param name="defaultValue">默认值</param>
    /// <param name="useCache">是否使用缓存</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>配置值</returns>
    public async Task<T?> GetAsync<T>(string key, T? defaultValue = default, bool useCache = true, CancellationToken cancellationToken = default)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(key))
            {
                _logger.LogWarning("配置键不能为空");
                return defaultValue;
            }

            var cacheKey = $"config:{key}";

            // 如果使用缓存，先尝试从缓存获取
            if (useCache)
            {
                var cachedValue = await _cacheService.GetAsync<T>(cacheKey, cancellationToken);
                if (cachedValue != null)
                {
                    _logger.LogDebug("从缓存获取配置: {Key}", key);
                    return cachedValue;
                }
            }

            // 从配置源获取
            var configValue = _configuration.GetValue(key, defaultValue);
            
            _logger.LogDebug("从配置源获取配置: {Key}, 值: {Value}", key, configValue);

            // 如果使用缓存且值不为空，则缓存配置值
            if (useCache && configValue != null)
            {
                await _cacheService.SetAsync(cacheKey, configValue, TimeSpan.FromMinutes(30), cancellationToken);
            }

            return configValue;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取配置时发生错误: {Key}", key);
            return defaultValue;
        }
    }

    /// <summary>
    /// 获取配置节
    /// </summary>
    /// <typeparam name="T">配置类型</typeparam>
    /// <param name="sectionName">节名称</param>
    /// <param name="useCache">是否使用缓存</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>配置节对象</returns>
    public async Task<T?> GetSectionAsync<T>(string sectionName, bool useCache = true, CancellationToken cancellationToken = default) where T : class, new()
    {
        try
        {
            if (string.IsNullOrWhiteSpace(sectionName))
            {
                _logger.LogWarning("配置节名称不能为空");
                return null;
            }

            var cacheKey = $"config:section:{sectionName}";

            // 如果使用缓存，先尝试从缓存获取
            if (useCache)
            {
                var cachedValue = await _cacheService.GetAsync<T>(cacheKey, cancellationToken);
                if (cachedValue != null)
                {
                    _logger.LogDebug("从缓存获取配置节: {SectionName}", sectionName);
                    return cachedValue;
                }
            }

            // 从配置源获取
            var section = _configuration.GetSection(sectionName);
            if (!section.Exists())
            {
                _logger.LogWarning("配置节不存在: {SectionName}", sectionName);
                return null;
            }

            var configObject = new T();
            section.Bind(configObject);

            _logger.LogDebug("从配置源获取配置节: {SectionName}", sectionName);

            // 如果使用缓存，则缓存配置对象
            if (useCache)
            {
                await _cacheService.SetAsync(cacheKey, configObject, TimeSpan.FromMinutes(30), cancellationToken);
            }

            return configObject;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取配置节时发生错误: {SectionName}", sectionName);
            return null;
        }
    }

    /// <summary>
    /// 获取连接字符串
    /// </summary>
    /// <param name="name">连接字符串名称</param>
    /// <param name="useCache">是否使用缓存</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>连接字符串</returns>
    public async Task<string?> GetConnectionStringAsync(string name, bool useCache = true, CancellationToken cancellationToken = default)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(name))
            {
                _logger.LogWarning("连接字符串名称不能为空");
                return null;
            }

            var cacheKey = $"config:connectionstring:{name}";

            // 如果使用缓存，先尝试从缓存获取
            if (useCache)
            {
                var cachedValue = await _cacheService.GetAsync<string>(cacheKey, cancellationToken);
                if (!string.IsNullOrEmpty(cachedValue))
                {
                    _logger.LogDebug("从缓存获取连接字符串: {Name}", name);
                    return cachedValue;
                }
            }

            // 从配置源获取
            var connectionString = _configuration.GetConnectionString(name);
            
            if (string.IsNullOrEmpty(connectionString))
            {
                _logger.LogWarning("连接字符串不存在: {Name}", name);
                return null;
            }

            _logger.LogDebug("从配置源获取连接字符串: {Name}", name);

            // 如果使用缓存，则缓存连接字符串
            if (useCache)
            {
                await _cacheService.SetAsync(cacheKey, connectionString, TimeSpan.FromMinutes(30), cancellationToken);
            }

            return connectionString;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取连接字符串时发生错误: {Name}", name);
            return null;
        }
    }

    /// <summary>
    /// 检查配置是否存在
    /// </summary>
    /// <param name="key">配置键</param>
    /// <returns>是否存在</returns>
    public bool Exists(string key)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(key))
            {
                return false;
            }

            var section = _configuration.GetSection(key);
            return section.Exists();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "检查配置存在性时发生错误: {Key}", key);
            return false;
        }
    }

    /// <summary>
    /// 获取所有配置键
    /// </summary>
    /// <param name="sectionName">节名称（可选）</param>
    /// <returns>配置键集合</returns>
    public IEnumerable<string> GetKeys(string? sectionName = null)
    {
        try
        {
            var section = string.IsNullOrWhiteSpace(sectionName) 
                ? _configuration 
                : _configuration.GetSection(sectionName);

            return section.GetChildren().Select(c => c.Key);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取配置键时发生错误: {SectionName}", sectionName);
            return Enumerable.Empty<string>();
        }
    }

    /// <summary>
    /// 刷新配置缓存
    /// </summary>
    /// <param name="key">配置键（可选，为空则刷新所有配置缓存）</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>任务</returns>
    public async Task RefreshCacheAsync(string? key = null, CancellationToken cancellationToken = default)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(key))
            {
                _logger.LogInformation("刷新所有配置缓存");
                
                // 这里可以实现更复杂的缓存清理逻辑
                // 由于Redis不直接支持按模式删除，这里简化处理
                _logger.LogWarning("刷新所有配置缓存功能需要Redis支持，当前为简化实现");
            }
            else
            {
                var cacheKey = $"config:{key}";
                await _cacheService.RemoveAsync(cacheKey, cancellationToken);
                
                _logger.LogDebug("刷新配置缓存: {Key}", key);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "刷新配置缓存时发生错误: {Key}", key);
        }
    }

    /// <summary>
    /// 获取环境名称
    /// </summary>
    /// <returns>环境名称</returns>
    public string GetEnvironmentName()
    {
        return _configuration["ASPNETCORE_ENVIRONMENT"] ?? "Production";
    }

    /// <summary>
    /// 检查是否为开发环境
    /// </summary>
    /// <returns>是否为开发环境</returns>
    public bool IsDevelopment()
    {
        return GetEnvironmentName().Equals("Development", StringComparison.OrdinalIgnoreCase);
    }

    /// <summary>
    /// 检查是否为生产环境
    /// </summary>
    /// <returns>是否为生产环境</returns>
    public bool IsProduction()
    {
        return GetEnvironmentName().Equals("Production", StringComparison.OrdinalIgnoreCase);
    }
}
