using Whimlab.AI.Agent.Platform.Shared.Common.Enums;

namespace Whimlab.AI.Agent.Platform.Application.Subscription.DTOs;

/// <summary>
/// 订阅计划DTO
/// </summary>
public class SubscriptionPlanDto
{
    /// <summary>
    /// 计划ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// 计划名称
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 计划描述
    /// </summary>
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// 计划类型
    /// </summary>
    public PlanType PlanType { get; set; }

    /// <summary>
    /// 月费价格
    /// </summary>
    public decimal MonthlyPrice { get; set; }

    /// <summary>
    /// 年费价格
    /// </summary>
    public decimal YearlyPrice { get; set; }

    /// <summary>
    /// 是否激活
    /// </summary>
    public bool IsActive { get; set; }

    /// <summary>
    /// 是否公开
    /// </summary>
    public bool IsPublic { get; set; }

    /// <summary>
    /// 排序顺序
    /// </summary>
    public int SortOrder { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedAt { get; set; }

    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime UpdatedAt { get; set; }

    /// <summary>
    /// 配额限制列表
    /// </summary>
    public List<QuotaLimitDto> QuotaLimits { get; set; } = new();

    /// <summary>
    /// 功能列表
    /// </summary>
    public List<string> Features { get; set; } = new();
}

/// <summary>
/// 配额限制DTO
/// </summary>
public class QuotaLimitDto
{
    /// <summary>
    /// 配额类型
    /// </summary>
    public string QuotaType { get; set; } = string.Empty;

    /// <summary>
    /// 限制值
    /// </summary>
    public long LimitValue { get; set; }

    /// <summary>
    /// 重置周期
    /// </summary>
    public string ResetPeriod { get; set; } = string.Empty;

    /// <summary>
    /// 是否无限制
    /// </summary>
    public bool IsUnlimited { get; set; }
}

/// <summary>
/// 客户订阅DTO
/// </summary>
public class CustomerSubscriptionDto
{
    /// <summary>
    /// 订阅ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// 客户ID
    /// </summary>
    public Guid CustomerId { get; set; }

    /// <summary>
    /// 订阅计划ID
    /// </summary>
    public Guid SubscriptionPlanId { get; set; }

    /// <summary>
    /// 订阅计划名称
    /// </summary>
    public string PlanName { get; set; } = string.Empty;

    /// <summary>
    /// 订阅状态
    /// </summary>
    public SubscriptionStatus Status { get; set; }

    /// <summary>
    /// 计费周期
    /// </summary>
    public BillingCycle BillingCycle { get; set; }

    /// <summary>
    /// 开始时间
    /// </summary>
    public DateTime StartDate { get; set; }

    /// <summary>
    /// 结束时间
    /// </summary>
    public DateTime? EndDate { get; set; }

    /// <summary>
    /// 下次计费时间
    /// </summary>
    public DateTime? NextBillingDate { get; set; }

    /// <summary>
    /// 试用期结束时间
    /// </summary>
    public DateTime? TrialEndDate { get; set; }

    /// <summary>
    /// 是否自动续费
    /// </summary>
    public bool AutoRenew { get; set; }

    /// <summary>
    /// 取消时间
    /// </summary>
    public DateTime? CancelledAt { get; set; }

    /// <summary>
    /// 取消原因
    /// </summary>
    public string? CancellationReason { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedAt { get; set; }

    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime UpdatedAt { get; set; }

    /// <summary>
    /// 配额使用情况
    /// </summary>
    public List<QuotaUsageDto> QuotaUsages { get; set; } = new();

    /// <summary>
    /// 订阅历史
    /// </summary>
    public List<SubscriptionHistoryDto> History { get; set; } = new();
}

/// <summary>
/// 配额使用DTO
/// </summary>
public class QuotaUsageDto
{
    /// <summary>
    /// 配额类型
    /// </summary>
    public string QuotaType { get; set; } = string.Empty;

    /// <summary>
    /// 当前使用量
    /// </summary>
    public long CurrentUsage { get; set; }

    /// <summary>
    /// 配额限制
    /// </summary>
    public long QuotaLimit { get; set; }

    /// <summary>
    /// 剩余配额
    /// </summary>
    public long RemainingQuota { get; set; }

    /// <summary>
    /// 使用率
    /// </summary>
    public double UsageRate { get; set; }

    /// <summary>
    /// 重置周期
    /// </summary>
    public string ResetPeriod { get; set; } = string.Empty;

    /// <summary>
    /// 下次重置时间
    /// </summary>
    public DateTime? NextResetDate { get; set; }

    /// <summary>
    /// 是否无限制
    /// </summary>
    public bool IsUnlimited { get; set; }

    /// <summary>
    /// 是否超限
    /// </summary>
    public bool IsOverLimit { get; set; }
}

/// <summary>
/// 订阅历史DTO
/// </summary>
public class SubscriptionHistoryDto
{
    /// <summary>
    /// 历史记录ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// 操作类型
    /// </summary>
    public string Action { get; set; } = string.Empty;

    /// <summary>
    /// 操作描述
    /// </summary>
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// 原订阅计划ID
    /// </summary>
    public Guid? OldPlanId { get; set; }

    /// <summary>
    /// 新订阅计划ID
    /// </summary>
    public Guid? NewPlanId { get; set; }

    /// <summary>
    /// 操作时间
    /// </summary>
    public DateTime CreatedAt { get; set; }

    /// <summary>
    /// 操作者ID
    /// </summary>
    public Guid? OperatedBy { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    public string? Notes { get; set; }
}

/// <summary>
/// 创建订阅计划DTO
/// </summary>
public class CreateSubscriptionPlanDto
{
    /// <summary>
    /// 计划名称
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 计划描述
    /// </summary>
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// 计划类型
    /// </summary>
    public PlanType PlanType { get; set; }

    /// <summary>
    /// 月费价格
    /// </summary>
    public decimal MonthlyPrice { get; set; }

    /// <summary>
    /// 年费价格
    /// </summary>
    public decimal YearlyPrice { get; set; }

    /// <summary>
    /// 是否公开
    /// </summary>
    public bool IsPublic { get; set; } = true;

    /// <summary>
    /// 排序顺序
    /// </summary>
    public int SortOrder { get; set; }

    /// <summary>
    /// 配额限制列表
    /// </summary>
    public List<CreateQuotaLimitDto> QuotaLimits { get; set; } = new();

    /// <summary>
    /// 功能列表
    /// </summary>
    public List<string> Features { get; set; } = new();
}

/// <summary>
/// 创建配额限制DTO
/// </summary>
public class CreateQuotaLimitDto
{
    /// <summary>
    /// 配额类型
    /// </summary>
    public string QuotaType { get; set; } = string.Empty;

    /// <summary>
    /// 限制值
    /// </summary>
    public long LimitValue { get; set; }

    /// <summary>
    /// 重置周期
    /// </summary>
    public string ResetPeriod { get; set; } = string.Empty;

    /// <summary>
    /// 是否无限制
    /// </summary>
    public bool IsUnlimited { get; set; }
}

/// <summary>
/// 更新订阅计划DTO
/// </summary>
public class UpdateSubscriptionPlanDto
{
    /// <summary>
    /// 计划ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// 计划名称
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 计划描述
    /// </summary>
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// 月费价格
    /// </summary>
    public decimal MonthlyPrice { get; set; }

    /// <summary>
    /// 年费价格
    /// </summary>
    public decimal YearlyPrice { get; set; }

    /// <summary>
    /// 是否公开
    /// </summary>
    public bool IsPublic { get; set; }

    /// <summary>
    /// 排序顺序
    /// </summary>
    public int SortOrder { get; set; }

    /// <summary>
    /// 功能列表
    /// </summary>
    public List<string> Features { get; set; } = new();
}

/// <summary>
/// 创建客户订阅DTO
/// </summary>
public class CreateCustomerSubscriptionDto
{
    /// <summary>
    /// 客户ID
    /// </summary>
    public Guid CustomerId { get; set; }

    /// <summary>
    /// 订阅计划ID
    /// </summary>
    public Guid SubscriptionPlanId { get; set; }

    /// <summary>
    /// 计费周期
    /// </summary>
    public BillingCycle BillingCycle { get; set; }

    /// <summary>
    /// 开始时间
    /// </summary>
    public DateTime StartDate { get; set; }

    /// <summary>
    /// 试用天数
    /// </summary>
    public int? TrialDays { get; set; }

    /// <summary>
    /// 是否自动续费
    /// </summary>
    public bool AutoRenew { get; set; } = true;

    /// <summary>
    /// 促销代码
    /// </summary>
    public string? PromoCode { get; set; }
}

/// <summary>
/// 订阅统计DTO
/// </summary>
public class SubscriptionStatisticsDto
{
    /// <summary>
    /// 总订阅数
    /// </summary>
    public int TotalSubscriptions { get; set; }

    /// <summary>
    /// 活跃订阅数
    /// </summary>
    public int ActiveSubscriptions { get; set; }

    /// <summary>
    /// 试用订阅数
    /// </summary>
    public int TrialSubscriptions { get; set; }

    /// <summary>
    /// 已取消订阅数
    /// </summary>
    public int CancelledSubscriptions { get; set; }

    /// <summary>
    /// 已过期订阅数
    /// </summary>
    public int ExpiredSubscriptions { get; set; }

    /// <summary>
    /// 月度经常性收入
    /// </summary>
    public decimal MonthlyRecurringRevenue { get; set; }

    /// <summary>
    /// 年度经常性收入
    /// </summary>
    public decimal AnnualRecurringRevenue { get; set; }

    /// <summary>
    /// 平均每用户收入
    /// </summary>
    public decimal AverageRevenuePerUser { get; set; }

    /// <summary>
    /// 客户流失率
    /// </summary>
    public double ChurnRate { get; set; }

    /// <summary>
    /// 客户生命周期价值
    /// </summary>
    public decimal CustomerLifetimeValue { get; set; }

    /// <summary>
    /// 按计划分组的统计
    /// </summary>
    public Dictionary<Guid, PlanStatisticsDto> PlanStatistics { get; set; } = new();

    /// <summary>
    /// 按日期分组的统计
    /// </summary>
    public Dictionary<DateTime, DailySubscriptionStatisticsDto> DailyStatistics { get; set; } = new();

    /// <summary>
    /// 收入趋势
    /// </summary>
    public List<RevenueDataPointDto> RevenueTrend { get; set; } = new();
}

/// <summary>
/// 计划统计DTO
/// </summary>
public class PlanStatisticsDto
{
    /// <summary>
    /// 计划ID
    /// </summary>
    public Guid PlanId { get; set; }

    /// <summary>
    /// 计划名称
    /// </summary>
    public string PlanName { get; set; } = string.Empty;

    /// <summary>
    /// 订阅数量
    /// </summary>
    public int SubscriptionCount { get; set; }

    /// <summary>
    /// 活跃订阅数
    /// </summary>
    public int ActiveCount { get; set; }

    /// <summary>
    /// 月度收入
    /// </summary>
    public decimal MonthlyRevenue { get; set; }

    /// <summary>
    /// 年度收入
    /// </summary>
    public decimal AnnualRevenue { get; set; }

    /// <summary>
    /// 转换率
    /// </summary>
    public double ConversionRate { get; set; }

    /// <summary>
    /// 流失率
    /// </summary>
    public double ChurnRate { get; set; }
}

/// <summary>
/// 每日订阅统计DTO
/// </summary>
public class DailySubscriptionStatisticsDto
{
    /// <summary>
    /// 日期
    /// </summary>
    public DateTime Date { get; set; }

    /// <summary>
    /// 新增订阅数
    /// </summary>
    public int NewSubscriptions { get; set; }

    /// <summary>
    /// 取消订阅数
    /// </summary>
    public int CancelledSubscriptions { get; set; }

    /// <summary>
    /// 升级订阅数
    /// </summary>
    public int UpgradedSubscriptions { get; set; }

    /// <summary>
    /// 降级订阅数
    /// </summary>
    public int DowngradedSubscriptions { get; set; }

    /// <summary>
    /// 当日收入
    /// </summary>
    public decimal DailyRevenue { get; set; }

    /// <summary>
    /// 累计收入
    /// </summary>
    public decimal CumulativeRevenue { get; set; }
}

/// <summary>
/// 收入数据点DTO
/// </summary>
public class RevenueDataPointDto
{
    /// <summary>
    /// 日期
    /// </summary>
    public DateTime Date { get; set; }

    /// <summary>
    /// 收入金额
    /// </summary>
    public decimal Amount { get; set; }

    /// <summary>
    /// 收入类型
    /// </summary>
    public string RevenueType { get; set; } = string.Empty;
}

/// <summary>
/// 配额分析DTO
/// </summary>
public class QuotaAnalysisDto
{
    /// <summary>
    /// 客户ID
    /// </summary>
    public Guid CustomerId { get; set; }

    /// <summary>
    /// 分析时间范围开始
    /// </summary>
    public DateTime StartDate { get; set; }

    /// <summary>
    /// 分析时间范围结束
    /// </summary>
    public DateTime EndDate { get; set; }

    /// <summary>
    /// 配额使用详情
    /// </summary>
    public List<QuotaUsageDetailDto> QuotaUsageDetails { get; set; } = new();

    /// <summary>
    /// 使用趋势
    /// </summary>
    public List<QuotaUsageTrendDto> UsageTrends { get; set; } = new();

    /// <summary>
    /// 预测信息
    /// </summary>
    public QuotaPredictionDto? Prediction { get; set; }
}

/// <summary>
/// 配额使用详情DTO
/// </summary>
public class QuotaUsageDetailDto
{
    /// <summary>
    /// 配额类型
    /// </summary>
    public string QuotaType { get; set; } = string.Empty;

    /// <summary>
    /// 总使用量
    /// </summary>
    public long TotalUsage { get; set; }

    /// <summary>
    /// 平均每日使用量
    /// </summary>
    public double AverageDailyUsage { get; set; }

    /// <summary>
    /// 峰值使用量
    /// </summary>
    public long PeakUsage { get; set; }

    /// <summary>
    /// 峰值使用日期
    /// </summary>
    public DateTime PeakUsageDate { get; set; }

    /// <summary>
    /// 使用率
    /// </summary>
    public double UsageRate { get; set; }

    /// <summary>
    /// 超限次数
    /// </summary>
    public int OverLimitCount { get; set; }
}

/// <summary>
/// 配额使用趋势DTO
/// </summary>
public class QuotaUsageTrendDto
{
    /// <summary>
    /// 配额类型
    /// </summary>
    public string QuotaType { get; set; } = string.Empty;

    /// <summary>
    /// 趋势数据点
    /// </summary>
    public List<UsageDataPointDto> DataPoints { get; set; } = new();

    /// <summary>
    /// 趋势方向
    /// </summary>
    public TrendDirection TrendDirection { get; set; }

    /// <summary>
    /// 趋势强度
    /// </summary>
    public double TrendStrength { get; set; }
}

/// <summary>
/// 使用数据点DTO
/// </summary>
public class UsageDataPointDto
{
    /// <summary>
    /// 日期
    /// </summary>
    public DateTime Date { get; set; }

    /// <summary>
    /// 使用量
    /// </summary>
    public long Usage { get; set; }

    /// <summary>
    /// 配额限制
    /// </summary>
    public long Limit { get; set; }
}

/// <summary>
/// 配额预测DTO
/// </summary>
public class QuotaPredictionDto
{
    /// <summary>
    /// 预测的配额耗尽时间
    /// </summary>
    public Dictionary<string, DateTime?> PredictedExhaustionDates { get; set; } = new();

    /// <summary>
    /// 建议的计划升级
    /// </summary>
    public List<PlanUpgradeRecommendationDto> UpgradeRecommendations { get; set; } = new();

    /// <summary>
    /// 预测准确度
    /// </summary>
    public double PredictionAccuracy { get; set; }
}

/// <summary>
/// 计划升级建议DTO
/// </summary>
public class PlanUpgradeRecommendationDto
{
    /// <summary>
    /// 建议的计划ID
    /// </summary>
    public Guid RecommendedPlanId { get; set; }

    /// <summary>
    /// 建议的计划名称
    /// </summary>
    public string RecommendedPlanName { get; set; } = string.Empty;

    /// <summary>
    /// 建议原因
    /// </summary>
    public string Reason { get; set; } = string.Empty;

    /// <summary>
    /// 预期收益
    /// </summary>
    public string ExpectedBenefit { get; set; } = string.Empty;

    /// <summary>
    /// 价格差异
    /// </summary>
    public decimal PriceDifference { get; set; }

    /// <summary>
    /// 建议优先级
    /// </summary>
    public int Priority { get; set; }
}
