using Whimlab.AI.Agent.Platform.Domain.Core.Common;
using Whimlab.AI.Agent.Platform.Shared.Common.Enums;

namespace Whimlab.AI.Agent.Platform.Domain.Conversation.Entities;

/// <summary>
/// 消息实体
/// </summary>
public class Message : Entity
{
    private readonly List<MessageAttachment> _attachments = new();

    /// <summary>
    /// 对话ID
    /// </summary>
    public Guid ConversationId { get; private set; }

    /// <summary>
    /// 消息内容
    /// </summary>
    public string Content { get; private set; } = string.Empty;

    /// <summary>
    /// 发送者类型
    /// </summary>
    public SenderType SenderType { get; private set; }

    /// <summary>
    /// 消息序号
    /// </summary>
    public int SequenceNumber { get; private set; }

    /// <summary>
    /// 使用的Token数量
    /// </summary>
    public int TokensUsed { get; private set; }

    /// <summary>
    /// 模型信息
    /// </summary>
    public string? ModelInfo { get; private set; }

    /// <summary>
    /// 消息类型（用于系统消息）
    /// </summary>
    public string? MessageType { get; private set; }

    /// <summary>
    /// 是否已删除
    /// </summary>
    public bool IsDeleted { get; private set; }

    /// <summary>
    /// 删除时间
    /// </summary>
    public DateTime? DeletedAt { get; private set; }

    /// <summary>
    /// 附件列表（只读）
    /// </summary>
    public IReadOnlyList<MessageAttachment> Attachments => _attachments.AsReadOnly();

    /// <summary>
    /// 对话导航属性
    /// </summary>
    public Conversation? Conversation { get; private set; }

    /// <summary>
    /// 私有构造函数（用于EF Core）
    /// </summary>
    private Message() { }

    /// <summary>
    /// 创建用户消息
    /// </summary>
    /// <param name="conversationId">对话ID</param>
    /// <param name="content">消息内容</param>
    /// <param name="attachments">附件</param>
    /// <returns>消息实体</returns>
    public static Message CreateUserMessage(Guid conversationId, string content, IEnumerable<MessageAttachment>? attachments = null)
    {
        var message = new Message
        {
            ConversationId = conversationId,
            Content = content.Trim(),
            SenderType = SenderType.User,
            TokensUsed = 0,
            IsDeleted = false
        };

        if (attachments != null)
        {
            foreach (var attachment in attachments)
            {
                message._attachments.Add(attachment);
            }
        }

        return message;
    }

    /// <summary>
    /// 创建智能体消息
    /// </summary>
    /// <param name="conversationId">对话ID</param>
    /// <param name="content">消息内容</param>
    /// <param name="tokensUsed">使用的Token数量</param>
    /// <param name="modelInfo">模型信息</param>
    /// <param name="attachments">附件</param>
    /// <returns>消息实体</returns>
    public static Message CreateAgentMessage(
        Guid conversationId, 
        string content, 
        int tokensUsed = 0, 
        string? modelInfo = null,
        IEnumerable<MessageAttachment>? attachments = null)
    {
        var message = new Message
        {
            ConversationId = conversationId,
            Content = content.Trim(),
            SenderType = SenderType.Agent,
            TokensUsed = tokensUsed,
            ModelInfo = modelInfo,
            IsDeleted = false
        };

        if (attachments != null)
        {
            foreach (var attachment in attachments)
            {
                message._attachments.Add(attachment);
            }
        }

        return message;
    }

    /// <summary>
    /// 创建系统消息
    /// </summary>
    /// <param name="conversationId">对话ID</param>
    /// <param name="content">消息内容</param>
    /// <param name="messageType">消息类型</param>
    /// <returns>消息实体</returns>
    public static Message CreateSystemMessage(Guid conversationId, string content, string? messageType = null)
    {
        return new Message
        {
            ConversationId = conversationId,
            Content = content.Trim(),
            SenderType = SenderType.System,
            MessageType = messageType,
            TokensUsed = 0,
            IsDeleted = false
        };
    }

    /// <summary>
    /// 设置序号
    /// </summary>
    /// <param name="sequenceNumber">序号</param>
    public void SetSequenceNumber(int sequenceNumber)
    {
        SequenceNumber = sequenceNumber;
        MarkAsModified();
    }

    /// <summary>
    /// 更新内容
    /// </summary>
    /// <param name="content">新内容</param>
    public void UpdateContent(string content)
    {
        if (IsDeleted)
            throw new InvalidOperationException("已删除的消息不能修改");

        if (SenderType == SenderType.System)
            throw new InvalidOperationException("系统消息不能修改");

        Content = content.Trim();
        MarkAsModified();
    }

    /// <summary>
    /// 添加附件
    /// </summary>
    /// <param name="attachment">附件</param>
    public void AddAttachment(MessageAttachment attachment)
    {
        if (IsDeleted)
            throw new InvalidOperationException("已删除的消息不能添加附件");

        _attachments.Add(attachment);
        MarkAsModified();
    }

    /// <summary>
    /// 移除附件
    /// </summary>
    /// <param name="attachmentId">附件ID</param>
    public void RemoveAttachment(Guid attachmentId)
    {
        if (IsDeleted)
            throw new InvalidOperationException("已删除的消息不能移除附件");

        var attachment = _attachments.FirstOrDefault(a => a.Id == attachmentId);
        if (attachment != null)
        {
            _attachments.Remove(attachment);
            MarkAsModified();
        }
    }

    /// <summary>
    /// 删除消息
    /// </summary>
    public void Delete()
    {
        if (IsDeleted)
            return;

        IsDeleted = true;
        DeletedAt = DateTime.UtcNow;
        MarkAsModified();
    }

    /// <summary>
    /// 恢复消息
    /// </summary>
    public void Restore()
    {
        if (!IsDeleted)
            return;

        IsDeleted = false;
        DeletedAt = null;
        MarkAsModified();
    }

    /// <summary>
    /// 获取消息摘要
    /// </summary>
    /// <param name="maxLength">最大长度</param>
    /// <returns>消息摘要</returns>
    public string GetSummary(int maxLength = 100)
    {
        if (Content.Length <= maxLength)
            return Content;

        return Content[..maxLength] + "...";
    }

    /// <summary>
    /// 检查是否有附件
    /// </summary>
    /// <returns>是否有附件</returns>
    public bool HasAttachments()
    {
        return _attachments.Any();
    }

    /// <summary>
    /// 获取附件总大小
    /// </summary>
    /// <returns>附件总大小（字节）</returns>
    public long GetTotalAttachmentSize()
    {
        return _attachments.Sum(a => a.FileSize);
    }
}

/// <summary>
/// 消息附件实体
/// </summary>
public class MessageAttachment : Entity
{
    /// <summary>
    /// 消息ID
    /// </summary>
    public Guid MessageId { get; private set; }

    /// <summary>
    /// 文件名
    /// </summary>
    public string FileName { get; private set; } = string.Empty;

    /// <summary>
    /// 文件类型
    /// </summary>
    public string FileType { get; private set; } = string.Empty;

    /// <summary>
    /// 文件大小（字节）
    /// </summary>
    public long FileSize { get; private set; }

    /// <summary>
    /// 文件URL
    /// </summary>
    public string FileUrl { get; private set; } = string.Empty;

    /// <summary>
    /// 缩略图URL
    /// </summary>
    public string? ThumbnailUrl { get; private set; }

    /// <summary>
    /// 消息导航属性
    /// </summary>
    public Message? Message { get; private set; }

    /// <summary>
    /// 私有构造函数（用于EF Core）
    /// </summary>
    private MessageAttachment() { }

    /// <summary>
    /// 创建消息附件
    /// </summary>
    /// <param name="messageId">消息ID</param>
    /// <param name="fileName">文件名</param>
    /// <param name="fileType">文件类型</param>
    /// <param name="fileSize">文件大小</param>
    /// <param name="fileUrl">文件URL</param>
    /// <param name="thumbnailUrl">缩略图URL</param>
    /// <returns>消息附件实体</returns>
    public static MessageAttachment Create(
        Guid messageId,
        string fileName,
        string fileType,
        long fileSize,
        string fileUrl,
        string? thumbnailUrl = null)
    {
        if (messageId == Guid.Empty)
            throw new ArgumentException("消息ID不能为空", nameof(messageId));

        if (string.IsNullOrWhiteSpace(fileName))
            throw new ArgumentException("文件名不能为空", nameof(fileName));

        if (string.IsNullOrWhiteSpace(fileType))
            throw new ArgumentException("文件类型不能为空", nameof(fileType));

        if (fileSize <= 0)
            throw new ArgumentException("文件大小必须大于0", nameof(fileSize));

        if (string.IsNullOrWhiteSpace(fileUrl))
            throw new ArgumentException("文件URL不能为空", nameof(fileUrl));

        return new MessageAttachment
        {
            MessageId = messageId,
            FileName = fileName.Trim(),
            FileType = fileType.Trim().ToLowerInvariant(),
            FileSize = fileSize,
            FileUrl = fileUrl.Trim(),
            ThumbnailUrl = thumbnailUrl?.Trim()
        };
    }

    /// <summary>
    /// 检查是否为图片文件
    /// </summary>
    /// <returns>是否为图片文件</returns>
    public bool IsImage()
    {
        var imageTypes = new[] { "jpg", "jpeg", "png", "gif", "webp", "bmp", "svg" };
        return imageTypes.Contains(FileType);
    }

    /// <summary>
    /// 检查是否为文档文件
    /// </summary>
    /// <returns>是否为文档文件</returns>
    public bool IsDocument()
    {
        var documentTypes = new[] { "pdf", "doc", "docx", "txt", "md", "rtf", "odt" };
        return documentTypes.Contains(FileType);
    }

    /// <summary>
    /// 获取格式化的文件大小
    /// </summary>
    /// <returns>格式化的文件大小</returns>
    public string GetFormattedFileSize()
    {
        const long kb = 1024;
        const long mb = kb * 1024;
        const long gb = mb * 1024;

        return FileSize switch
        {
            < kb => $"{FileSize} B",
            < mb => $"{FileSize / (double)kb:F1} KB",
            < gb => $"{FileSize / (double)mb:F1} MB",
            _ => $"{FileSize / (double)gb:F1} GB"
        };
    }
}
