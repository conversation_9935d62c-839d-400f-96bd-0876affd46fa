using AutoMapper;
using Microsoft.Extensions.Logging;
using Whimlab.AI.Agent.Platform.Application.Core.Common;
using Whimlab.AI.Agent.Platform.Application.Core.Services;
using Whimlab.AI.Agent.Platform.Application.Conversation.Commands;
using Whimlab.AI.Agent.Platform.Application.Conversation.DTOs;
using Whimlab.AI.Agent.Platform.Application.Identity.Services;
using Whimlab.AI.Agent.Platform.Domain.Conversation.Entities;
using Whimlab.AI.Agent.Platform.Domain.Conversation.Repositories;
using Whimlab.AI.Agent.Platform.Domain.Subscription.Services;
using Whimlab.AI.Agent.Platform.Shared.Common.Enums;

namespace Whimlab.AI.Agent.Platform.Application.Conversation.Handlers;

/// <summary>
/// 创建对话命令处理器
/// </summary>
public class CreateConversationCommandHandler : ICommandHandler<CreateConversationCommand, ConversationDto>
{
    private readonly IConversationRepository _conversationRepository;
    private readonly IAuthorizationService _authorizationService;
    private readonly IQuotaManagementService _quotaManagementService;
    private readonly IMapper _mapper;
    private readonly ILogger<CreateConversationCommandHandler> _logger;

    /// <summary>
    /// 初始化创建对话命令处理器
    /// </summary>
    public CreateConversationCommandHandler(
        IConversationRepository conversationRepository,
        IAuthorizationService authorizationService,
        IQuotaManagementService quotaManagementService,
        IMapper mapper,
        ILogger<CreateConversationCommandHandler> logger)
    {
        _conversationRepository = conversationRepository;
        _authorizationService = authorizationService;
        _quotaManagementService = quotaManagementService;
        _mapper = mapper;
        _logger = logger;
    }

    /// <summary>
    /// 处理创建对话命令
    /// </summary>
    public async Task<Result<ConversationDto>> Handle(CreateConversationCommand request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("开始创建对话: {Title} - 用户: {UserId}", request.Title, request.UserId);

            // 1. 验证权限
            var hasPermission = await _authorizationService.HasPermissionAsync(
                request.UserId,
                ResourcePermissions.ConversationPermissions.CreateConversation,
                cancellationToken);

            if (!hasPermission)
            {
                _logger.LogWarning("用户 {UserId} 没有创建对话的权限", request.UserId);
                return Result.Failure<ConversationDto>("没有创建对话的权限", "PERMISSION_DENIED");
            }

            // 2. 验证配额
            var quotaCheckResult = await _quotaManagementService.CheckQuotaAsync(
                request.UserId,
                QuotaTypes.Conversation.CreateConversation,
                1,
                cancellationToken);

            if (!quotaCheckResult.IsAllowed)
            {
                _logger.LogWarning("用户 {UserId} 对话创建配额不足: {Reason}",
                    request.UserId, quotaCheckResult.DenialReason);
                return Result.Failure<ConversationDto>(
                    quotaCheckResult.DenialReason ?? "对话创建配额不足",
                    "QUOTA_EXCEEDED");
            }

            // 3. 创建对话实体
            var conversation = Domain.Conversation.Entities.Conversation.Create(
                request.Title,
                request.UserId,
                request.AgentId);

            // 如果有初始消息，创建消息并检查消息配额
            if (!string.IsNullOrWhiteSpace(request.InitialMessage))
            {
                // 检查消息配额
                var messageQuotaResult = await _quotaManagementService.CheckQuotaAsync(
                    request.UserId,
                    QuotaTypes.Conversation.MessageCount,
                    1,
                    cancellationToken);

                if (!messageQuotaResult.IsAllowed)
                {
                    _logger.LogWarning("用户 {UserId} 消息配额不足: {Reason}",
                        request.UserId, messageQuotaResult.DenialReason);
                    return Result.Failure<ConversationDto>(
                        messageQuotaResult.DenialReason ?? "消息配额不足",
                        "QUOTA_EXCEEDED");
                }

                conversation.AddUserMessage(request.InitialMessage);
            }

            // 4. 保存对话
            await _conversationRepository.AddAsync(conversation, cancellationToken);
            await _conversationRepository.SaveChangesAsync(cancellationToken);

            // 5. 使用配额
            var quotaUsageResult = await _quotaManagementService.UseQuotaAsync(
                request.UserId,
                QuotaTypes.Conversation.CreateConversation,
                1,
                $"创建对话: {request.Title}",
                cancellationToken);

            if (!quotaUsageResult.IsSuccess)
            {
                _logger.LogError("使用对话创建配额失败，用户 {UserId}: {ErrorMessage}",
                    request.UserId, quotaUsageResult.ErrorMessage);
                // 注意：这里对话已经创建，但配额使用失败，可能需要回滚或记录异常
            }

            // 如果有初始消息，使用消息配额
            if (!string.IsNullOrWhiteSpace(request.InitialMessage))
            {
                var messageQuotaUsageResult = await _quotaManagementService.UseQuotaAsync(
                    request.UserId,
                    QuotaTypes.Conversation.MessageCount,
                    1,
                    $"对话初始消息: {conversation.Id}",
                    cancellationToken);

                if (!messageQuotaUsageResult.IsSuccess)
                {
                    _logger.LogError("使用消息配额失败，用户 {UserId}: {ErrorMessage}",
                        request.UserId, messageQuotaUsageResult.ErrorMessage);
                }
            }

            // 6. 重新获取对话
            var createdConversation = await _conversationRepository.GetByIdAsync(conversation.Id, cancellationToken);
            if (createdConversation == null)
            {
                return Result.Failure<ConversationDto>("对话创建失败", "CONVERSATION_CREATION_FAILED");
            }

            var conversationDto = _mapper.Map<ConversationDto>(createdConversation);

            _logger.LogInformation("对话创建成功: {ConversationId} - {Title}，剩余对话配额: {RemainingQuota}",
                conversation.Id, request.Title, quotaUsageResult.RemainingQuota);

            return Result.Success(conversationDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "创建对话时发生错误: {Title}", request.Title);
            return Result.Failure<ConversationDto>("创建对话时发生内部错误", "INTERNAL_ERROR");
        }
    }
}

/// <summary>
/// 更新对话命令处理器
/// </summary>
public class UpdateConversationCommandHandler : ICommandHandler<UpdateConversationCommand, ConversationDto>
{
    private readonly IConversationRepository _conversationRepository;
    private readonly IAuthorizationService _authorizationService;
    private readonly IMapper _mapper;
    private readonly ILogger<UpdateConversationCommandHandler> _logger;

    /// <summary>
    /// 初始化更新对话命令处理器
    /// </summary>
    public UpdateConversationCommandHandler(
        IConversationRepository conversationRepository,
        IAuthorizationService authorizationService,
        IMapper mapper,
        ILogger<UpdateConversationCommandHandler> logger)
    {
        _conversationRepository = conversationRepository;
        _authorizationService = authorizationService;
        _mapper = mapper;
        _logger = logger;
    }

    /// <summary>
    /// 处理更新对话命令
    /// </summary>
    public async Task<Result<ConversationDto>> Handle(UpdateConversationCommand request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("开始更新对话: {ConversationId}", request.Id);

            // 1. 获取对话
            var conversation = await _conversationRepository.GetByIdAsync(request.Id, cancellationToken);
            if (conversation == null)
            {
                return Result.Failure<ConversationDto>("对话不存在", "CONVERSATION_NOT_FOUND");
            }

            // 2. 验证权限 - 检查资源访问权限
            var canAccess = await _authorizationService.CanAccessResourceAsync(
                request.OperatorId,
                ResourcePermissions.ResourceTypes.Conversation,
                request.Id,
                ResourcePermissions.Operations.Update,
                cancellationToken);

            if (!canAccess)
            {
                _logger.LogWarning("用户 {UserId} 没有权限修改对话 {ConversationId}",
                    request.OperatorId, request.Id);
                return Result.Failure<ConversationDto>("没有权限修改此对话", "PERMISSION_DENIED");
            }

            // 更新对话信息
            conversation.UpdateTitle(request.Title);

            if (!string.IsNullOrWhiteSpace(request.Summary))
            {
                conversation.UpdateContext(request.Summary);
            }

            // 保存更改
            await _conversationRepository.UpdateAsync(conversation, cancellationToken);
            await _conversationRepository.SaveChangesAsync(cancellationToken);

            // 重新获取对话
            var updatedConversation = await _conversationRepository.GetByIdAsync(conversation.Id, cancellationToken);
            var conversationDto = _mapper.Map<ConversationDto>(updatedConversation);

            _logger.LogInformation("对话更新成功: {ConversationId}", request.Id);

            return Result.Success(conversationDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新对话时发生错误: {ConversationId}", request.Id);
            return Result.Failure<ConversationDto>("更新对话时发生内部错误", "INTERNAL_ERROR");
        }
    }
}

/// <summary>
/// 归档对话命令处理器
/// </summary>
public class ArchiveConversationCommandHandler : ICommandHandler<ArchiveConversationCommand>
{
    private readonly IConversationRepository _conversationRepository;
    private readonly ILogger<ArchiveConversationCommandHandler> _logger;

    /// <summary>
    /// 初始化归档对话命令处理器
    /// </summary>
    public ArchiveConversationCommandHandler(
        IConversationRepository conversationRepository,
        ILogger<ArchiveConversationCommandHandler> logger)
    {
        _conversationRepository = conversationRepository;
        _logger = logger;
    }

    /// <summary>
    /// 处理归档对话命令
    /// </summary>
    public async Task<Result> Handle(ArchiveConversationCommand request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("开始归档对话: {ConversationId}", request.Id);

            // 获取对话
            var conversation = await _conversationRepository.GetByIdAsync(request.Id, cancellationToken);
            if (conversation == null)
            {
                return Result.Failure("对话不存在", "CONVERSATION_NOT_FOUND");
            }

            // 检查权限（简化版本）
            if (conversation.UserId != request.OperatorId)
            {
                return Result.Failure("没有权限归档此对话", "PERMISSION_DENIED");
            }

            // 归档对话
            conversation.Archive();

            // 保存更改
            await _conversationRepository.UpdateAsync(conversation, cancellationToken);
            await _conversationRepository.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("对话归档成功: {ConversationId}", request.Id);

            return Result.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "归档对话时发生错误: {ConversationId}", request.Id);
            return Result.Failure("归档对话时发生内部错误", "INTERNAL_ERROR");
        }
    }
}

/// <summary>
/// 删除对话命令处理器
/// </summary>
public class DeleteConversationCommandHandler : ICommandHandler<DeleteConversationCommand>
{
    private readonly IConversationRepository _conversationRepository;
    private readonly ILogger<DeleteConversationCommandHandler> _logger;

    /// <summary>
    /// 初始化删除对话命令处理器
    /// </summary>
    public DeleteConversationCommandHandler(
        IConversationRepository conversationRepository,
        ILogger<DeleteConversationCommandHandler> logger)
    {
        _conversationRepository = conversationRepository;
        _logger = logger;
    }

    /// <summary>
    /// 处理删除对话命令
    /// </summary>
    public async Task<Result> Handle(DeleteConversationCommand request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("开始删除对话: {ConversationId}", request.Id);

            // 获取对话
            var conversation = await _conversationRepository.GetByIdAsync(request.Id, cancellationToken);
            if (conversation == null)
            {
                return Result.Failure("对话不存在", "CONVERSATION_NOT_FOUND");
            }

            // 检查权限（简化版本）
            if (conversation.UserId != request.OperatorId)
            {
                return Result.Failure("没有权限删除此对话", "PERMISSION_DENIED");
            }

            // 删除对话
            await _conversationRepository.DeleteAsync(conversation, cancellationToken);
            await _conversationRepository.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("对话删除成功: {ConversationId}", request.Id);

            return Result.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "删除对话时发生错误: {ConversationId}", request.Id);
            return Result.Failure("删除对话时发生内部错误", "INTERNAL_ERROR");
        }
    }
}

/// <summary>
/// 创建消息命令处理器
/// </summary>
public class CreateMessageCommandHandler : ICommandHandler<CreateMessageCommand, MessageDto>
{
    private readonly IConversationRepository _conversationRepository;
    private readonly IAuthorizationService _authorizationService;
    private readonly IQuotaManagementService _quotaManagementService;
    private readonly IMapper _mapper;
    private readonly ILogger<CreateMessageCommandHandler> _logger;

    /// <summary>
    /// 初始化创建消息命令处理器
    /// </summary>
    public CreateMessageCommandHandler(
        IConversationRepository conversationRepository,
        IAuthorizationService authorizationService,
        IQuotaManagementService quotaManagementService,
        IMapper mapper,
        ILogger<CreateMessageCommandHandler> logger)
    {
        _conversationRepository = conversationRepository;
        _authorizationService = authorizationService;
        _quotaManagementService = quotaManagementService;
        _mapper = mapper;
        _logger = logger;
    }

    /// <summary>
    /// 处理创建消息命令
    /// </summary>
    public async Task<Result<MessageDto>> Handle(CreateMessageCommand request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("开始创建消息，对话 {ConversationId}，用户 {UserId}",
                request.ConversationId, request.OperatorId);

            // 1. 获取对话
            var conversation = await _conversationRepository.GetByIdAsync(request.ConversationId, cancellationToken);
            if (conversation == null)
            {
                return Result.Failure<MessageDto>("对话不存在", "CONVERSATION_NOT_FOUND");
            }

            // 2. 验证权限 - 检查对话访问权限
            var canAccess = await _authorizationService.CanAccessResourceAsync(
                request.OperatorId,
                ResourcePermissions.ResourceTypes.Conversation,
                request.ConversationId,
                ResourcePermissions.Operations.Update,
                cancellationToken);

            if (!canAccess)
            {
                _logger.LogWarning("用户 {UserId} 没有权限在对话 {ConversationId} 中创建消息",
                    request.OperatorId, request.ConversationId);
                return Result.Failure<MessageDto>("没有权限在此对话中创建消息", "PERMISSION_DENIED");
            }

            // 3. 验证消息配额
            var quotaCheckResult = await _quotaManagementService.CheckQuotaAsync(
                request.OperatorId,
                QuotaTypes.Conversation.MessageCount,
                1,
                cancellationToken);

            if (!quotaCheckResult.IsAllowed)
            {
                _logger.LogWarning("用户 {UserId} 消息配额不足: {Reason}",
                    request.OperatorId, quotaCheckResult.DenialReason);
                return Result.Failure<MessageDto>(
                    quotaCheckResult.DenialReason ?? "消息配额不足",
                    "QUOTA_EXCEEDED");
            }

            // 4. 创建消息
            Message message;
            switch (request.Role)
            {
                case MessageRole.User:
                    message = conversation.AddUserMessage(request.Content);
                    break;
                case MessageRole.Assistant:
                    message = conversation.AddAgentMessage(request.Content);
                    break;
                case MessageRole.System:
                    message = conversation.AddSystemMessage(request.Content);
                    break;
                default:
                    return Result.Failure<MessageDto>("不支持的消息角色", "INVALID_MESSAGE_ROLE");
            }

            // 5. 保存更改
            await _conversationRepository.UpdateAsync(conversation, cancellationToken);
            await _conversationRepository.SaveChangesAsync(cancellationToken);

            // 6. 使用消息配额
            var quotaUsageResult = await _quotaManagementService.UseQuotaAsync(
                request.OperatorId,
                QuotaTypes.Conversation.MessageCount,
                1,
                $"创建消息: {message.Id}",
                cancellationToken);

            if (!quotaUsageResult.IsSuccess)
            {
                _logger.LogError("使用消息配额失败，用户 {UserId}: {ErrorMessage}",
                    request.OperatorId, quotaUsageResult.ErrorMessage);
            }

            var messageDto = _mapper.Map<MessageDto>(message);

            _logger.LogInformation("消息创建成功: {MessageId}，剩余消息配额: {RemainingQuota}",
                message.Id, quotaUsageResult.RemainingQuota);

            return Result.Success(messageDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "创建消息时发生错误，对话 {ConversationId}", request.ConversationId);
            return Result.Failure<MessageDto>("创建消息时发生内部错误", "INTERNAL_ERROR");
        }
    }
}

/// <summary>
/// 发送消息并获取AI回复命令处理器
/// </summary>
public class SendMessageCommandHandler : ICommandHandler<SendMessageCommand, MessageDto>
{
    private readonly IConversationRepository _conversationRepository;
    private readonly IAuthorizationService _authorizationService;
    private readonly IQuotaManagementService _quotaManagementService;
    private readonly IMapper _mapper;
    private readonly ILogger<SendMessageCommandHandler> _logger;

    /// <summary>
    /// 初始化发送消息命令处理器
    /// </summary>
    public SendMessageCommandHandler(
        IConversationRepository conversationRepository,
        IAuthorizationService authorizationService,
        IQuotaManagementService quotaManagementService,
        IMapper mapper,
        ILogger<SendMessageCommandHandler> logger)
    {
        _conversationRepository = conversationRepository;
        _authorizationService = authorizationService;
        _quotaManagementService = quotaManagementService;
        _mapper = mapper;
        _logger = logger;
    }

    /// <summary>
    /// 处理发送消息命令
    /// </summary>
    public async Task<Result<MessageDto>> Handle(SendMessageCommand request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("开始发送消息，对话 {ConversationId}，用户 {UserId}",
                request.ConversationId, request.UserId);

            // 1. 获取对话
            var conversation = await _conversationRepository.GetByIdAsync(request.ConversationId, cancellationToken);
            if (conversation == null)
            {
                return Result.Failure<MessageDto>("对话不存在", "CONVERSATION_NOT_FOUND");
            }

            // 2. 验证权限 - 检查对话访问权限
            var canAccess = await _authorizationService.CanAccessResourceAsync(
                request.UserId,
                ResourcePermissions.ResourceTypes.Conversation,
                request.ConversationId,
                ResourcePermissions.Operations.Update,
                cancellationToken);

            if (!canAccess)
            {
                _logger.LogWarning("用户 {UserId} 没有权限在对话 {ConversationId} 中发送消息",
                    request.UserId, request.ConversationId);
                return Result.Failure<MessageDto>("没有权限在此对话中发送消息", "PERMISSION_DENIED");
            }

            // 3. 验证消息配额
            var messageQuotaResult = await _quotaManagementService.CheckQuotaAsync(
                request.UserId,
                QuotaTypes.Conversation.MessageCount,
                1,
                cancellationToken);

            if (!messageQuotaResult.IsAllowed)
            {
                _logger.LogWarning("用户 {UserId} 消息配额不足: {Reason}",
                    request.UserId, messageQuotaResult.DenialReason);
                return Result.Failure<MessageDto>(
                    messageQuotaResult.DenialReason ?? "消息配额不足",
                    "QUOTA_EXCEEDED");
            }

            // 4. 估算Token使用量（简化实现，实际应该根据模型和内容计算）
            var estimatedTokens = EstimateTokenUsage(request.Content);

            // 验证Token配额
            var tokenQuotaResult = await _quotaManagementService.CheckQuotaAsync(
                request.UserId,
                QuotaTypes.Conversation.TokenUsage,
                estimatedTokens,
                cancellationToken);

            if (!tokenQuotaResult.IsAllowed)
            {
                _logger.LogWarning("用户 {UserId} Token配额不足: {Reason}",
                    request.UserId, tokenQuotaResult.DenialReason);
                return Result.Failure<MessageDto>(
                    tokenQuotaResult.DenialReason ?? "Token配额不足",
                    "QUOTA_EXCEEDED");
            }

            // 5. 添加用户消息
            var userMessage = conversation.AddUserMessage(request.Content);

            // 6. 这里应该调用AI服务获取回复（简化实现）
            // var aiResponse = await _aiService.GetResponseAsync(conversation, cancellationToken);
            var aiResponseContent = "这是AI的模拟回复"; // 实际实现需要调用AI服务
            var actualTokensUsed = EstimateTokenUsage(request.Content + aiResponseContent); // 实际应该从AI服务获取

            var aiMessage = conversation.AddAgentMessage(aiResponseContent, (int)actualTokensUsed);

            // 7. 保存更改
            await _conversationRepository.UpdateAsync(conversation, cancellationToken);
            await _conversationRepository.SaveChangesAsync(cancellationToken);

            // 8. 使用配额
            var messageQuotaUsageResult = await _quotaManagementService.UseQuotaAsync(
                request.UserId,
                QuotaTypes.Conversation.MessageCount,
                2, // 用户消息 + AI回复
                $"对话消息: {conversation.Id}",
                cancellationToken);

            var tokenQuotaUsageResult = await _quotaManagementService.UseQuotaAsync(
                request.UserId,
                QuotaTypes.Conversation.TokenUsage,
                actualTokensUsed,
                $"Token使用: {conversation.Id}",
                cancellationToken);

            if (!messageQuotaUsageResult.IsSuccess)
            {
                _logger.LogError("使用消息配额失败，用户 {UserId}: {ErrorMessage}",
                    request.UserId, messageQuotaUsageResult.ErrorMessage);
            }

            if (!tokenQuotaUsageResult.IsSuccess)
            {
                _logger.LogError("使用Token配额失败，用户 {UserId}: {ErrorMessage}",
                    request.UserId, tokenQuotaUsageResult.ErrorMessage);
            }

            var responseMessageDto = _mapper.Map<MessageDto>(aiMessage);

            _logger.LogInformation("消息发送成功: {MessageId}，Token使用: {TokensUsed}，剩余Token配额: {RemainingTokens}",
                aiMessage.Id, actualTokensUsed, tokenQuotaUsageResult.RemainingQuota);

            return Result.Success(responseMessageDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "发送消息时发生错误，对话 {ConversationId}", request.ConversationId);
            return Result.Failure<MessageDto>("发送消息时发生内部错误", "INTERNAL_ERROR");
        }
    }

    /// <summary>
    /// 估算Token使用量（简化实现）
    /// </summary>
    /// <param name="content">消息内容</param>
    /// <returns>估算的Token数量</returns>
    private static long EstimateTokenUsage(string content)
    {
        // 简化的Token估算：大约每4个字符1个Token
        // 实际实现应该使用具体的Tokenizer
        return Math.Max(1, content.Length / 4);
    }
}
