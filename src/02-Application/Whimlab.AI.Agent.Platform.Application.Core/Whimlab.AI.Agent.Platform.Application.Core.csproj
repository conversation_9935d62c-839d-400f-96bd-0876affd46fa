<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <RootNamespace>Whimlab.AI.Agent.Platform.Application.Core</RootNamespace>
    <AssemblyName>Whimlab.AI.Agent.Platform.Application.Core</AssemblyName>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="../../03-Domain/Whimlab.AI.Agent.Platform.Domain.Core/Whimlab.AI.Agent.Platform.Domain.Core.csproj" />
    <ProjectReference Include="../../05-Shared/Whimlab.AI.Agent.Platform.Shared.Common/Whimlab.AI.Agent.Platform.Shared.Common.csproj" />
    <ProjectReference Include="../../05-Shared/Whimlab.AI.Agent.Platform.Shared.Exceptions/Whimlab.AI.Agent.Platform.Shared.Exceptions.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="MediatR" />
    <PackageReference Include="FluentValidation" />
    <PackageReference Include="FluentValidation.DependencyInjectionExtensions" />
    <PackageReference Include="AutoMapper" />
    <PackageReference Include="AutoMapper.Extensions.Microsoft.DependencyInjection" />
    <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions" />
  </ItemGroup>

</Project>
