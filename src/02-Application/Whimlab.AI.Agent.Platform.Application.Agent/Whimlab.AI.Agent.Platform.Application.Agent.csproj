<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <RootNamespace>Whimlab.AI.Agent.Platform.Application.Agent</RootNamespace>
    <AssemblyName>Whimlab.AI.Agent.Platform.Application.Agent</AssemblyName>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="../Whimlab.AI.Agent.Platform.Application.Core/Whimlab.AI.Agent.Platform.Application.Core.csproj" />
    <ProjectReference Include="../../03-Domain/Whimlab.AI.Agent.Platform.Domain.Agent/Whimlab.AI.Agent.Platform.Domain.Agent.csproj" />
    <ProjectReference Include="..\Whimlab.AI.Agent.Platform.Application.Identity\Whimlab.AI.Agent.Platform.Application.Identity.csproj" />
    <ProjectReference Include="..\Whimlab.AI.Agent.Platform.Application.Subscription\Whimlab.AI.Agent.Platform.Application.Subscription.csproj" />
    <ProjectReference Include="..\..\03-Domain\Whimlab.AI.Agent.Platform.Domain.Subscription\Whimlab.AI.Agent.Platform.Domain.Subscription.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="MediatR" />
    <PackageReference Include="FluentValidation" />
    <PackageReference Include="FluentValidation.DependencyInjectionExtensions" />
    <PackageReference Include="AutoMapper" />
    <PackageReference Include="AutoMapper.Extensions.Microsoft.DependencyInjection" />
  </ItemGroup>

</Project>
