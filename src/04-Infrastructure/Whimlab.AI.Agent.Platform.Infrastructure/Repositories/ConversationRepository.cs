using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System.Linq.Expressions;
using Whimlab.AI.Agent.Platform.Domain.Conversation.Entities;
using Whimlab.AI.Agent.Platform.Domain.Conversation.Repositories;
using Whimlab.AI.Agent.Platform.Infrastructure.Data;
using Whimlab.AI.Agent.Platform.Shared.Common.Enums;

namespace Whimlab.AI.Agent.Platform.Infrastructure.Repositories;

/// <summary>
/// 对话仓储实现
/// </summary>
public class ConversationRepository : BaseRepository<Conversation, Guid>, IConversationRepository
{
    /// <summary>
    /// 初始化对话仓储
    /// </summary>
    /// <param name="context">数据库上下文</param>
    /// <param name="logger">日志记录器</param>
    public ConversationRepository(
        AgentPlatformDbContext context,
        ILogger<ConversationRepository> logger) : base(context, logger)
    {
    }

    /// <summary>
    /// 根据用户ID获取对话列表
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>对话列表</returns>
    public async Task<List<Conversation>> GetByUserIdAsync(Guid userId, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("根据用户ID获取对话列表: {UserId}", userId);

            return await _dbSet
                .Include(c => c.Messages.OrderBy(m => m.CreatedAt))
                .Where(c => c.UserId == userId)
                .OrderByDescending(c => c.UpdatedAt)
                .ToListAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "根据用户ID获取对话列表时发生错误: {UserId}", userId);
            throw;
        }
    }

    /// <summary>
    /// 根据智能体ID获取对话列表
    /// </summary>
    /// <param name="agentId">智能体ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>对话列表</returns>
    public async Task<List<Conversation>> GetByAgentIdAsync(Guid agentId, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("根据智能体ID获取对话列表: {AgentId}", agentId);

            return await _dbSet
                .Include(c => c.Messages.OrderBy(m => m.CreatedAt))
                .Where(c => c.AgentId == agentId)
                .OrderByDescending(c => c.UpdatedAt)
                .ToListAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "根据智能体ID获取对话列表时发生错误: {AgentId}", agentId);
            throw;
        }
    }

    /// <summary>
    /// 根据状态获取对话列表
    /// </summary>
    /// <param name="status">对话状态</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>对话列表</returns>
    public async Task<List<Conversation>> GetByStatusAsync(ConversationStatus status, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("根据状态获取对话列表: {Status}", status);

            return await _dbSet
                .Include(c => c.Messages.OrderBy(m => m.CreatedAt))
                .Where(c => c.Status == status)
                .OrderByDescending(c => c.UpdatedAt)
                .ToListAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "根据状态获取对话列表时发生错误: {Status}", status);
            throw;
        }
    }

    /// <summary>
    /// 获取活跃对话列表
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>活跃对话列表</returns>
    public async Task<List<Conversation>> GetActiveConversationsAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("获取活跃对话列表");

            return await _dbSet
                .Include(c => c.Messages.OrderBy(m => m.CreatedAt))
                .Where(c => c.Status == ConversationStatus.Active)
                .OrderByDescending(c => c.UpdatedAt)
                .ToListAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取活跃对话列表时发生错误");
            throw;
        }
    }

    /// <summary>
    /// 根据用户ID和智能体ID获取对话
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="agentId">智能体ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>对话</returns>
    public async Task<Conversation?> GetByUserAndAgentAsync(Guid userId, Guid agentId, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("根据用户ID和智能体ID获取对话: {UserId}, {AgentId}", userId, agentId);

            return await _dbSet
                .Include(c => c.Messages.OrderBy(m => m.CreatedAt))
                .Where(c => c.UserId == userId && c.AgentId == agentId && c.Status == ConversationStatus.Active)
                .OrderByDescending(c => c.UpdatedAt)
                .FirstOrDefaultAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "根据用户ID和智能体ID获取对话时发生错误: {UserId}, {AgentId}", userId, agentId);
            throw;
        }
    }

    /// <summary>
    /// 获取最近的对话列表
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="count">数量</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>最近的对话列表</returns>
    public async Task<List<Conversation>> GetRecentConversationsAsync(Guid userId, int count, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("获取最近的对话列表: {UserId}, 数量: {Count}", userId, count);

            return await _dbSet
                .Include(c => c.Messages.OrderBy(m => m.CreatedAt))
                .Where(c => c.UserId == userId)
                .OrderByDescending(c => c.UpdatedAt)
                .Take(count)
                .ToListAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取最近的对话列表时发生错误: {UserId}, 数量: {Count}", userId, count);
            throw;
        }
    }



    /// <summary>
    /// 搜索对话
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="searchTerm">搜索词</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>匹配的对话列表</returns>
    public async Task<List<Conversation>> SearchConversationsAsync(Guid userId, string searchTerm, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("搜索对话: {UserId}, 搜索词: {SearchTerm}", userId, searchTerm);

            if (string.IsNullOrWhiteSpace(searchTerm))
            {
                return new List<Conversation>();
            }

            var searchTermLower = searchTerm.ToLower();

            return await _dbSet
                .Include(c => c.Messages.OrderBy(m => m.CreatedAt))
                .Where(c => c.UserId == userId)
                .Where(c => c.Title.ToLower().Contains(searchTermLower) ||
                           c.Messages.Any(m => m.Content.ToLower().Contains(searchTermLower)))
                .OrderByDescending(c => c.UpdatedAt)
                .ToListAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "搜索对话时发生错误: {UserId}, 搜索词: {SearchTerm}", userId, searchTerm);
            throw;
        }
    }

    /// <summary>
    /// 获取对话统计信息
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>对话统计信息</returns>
    public async Task<ConversationStatistics> GetStatisticsAsync(Guid userId, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("获取对话统计信息: {UserId}", userId);

            var conversations = await _dbSet
                .Where(c => c.UserId == userId)
                .ToListAsync(cancellationToken);

            var totalConversations = conversations.Count;
            var activeConversations = conversations.Count(c => c.Status == ConversationStatus.Active);
            var archivedConversations = conversations.Count(c => c.Status == ConversationStatus.Archived);

            var totalMessages = await _context.Messages
                .Where(m => conversations.Select(c => c.Id).Contains(m.ConversationId))
                .CountAsync(cancellationToken);

            var totalTokensUsed = conversations.Sum(c => c.TotalTokensUsed);

            return new ConversationStatistics
            {
                TotalConversations = totalConversations,
                ActiveConversations = activeConversations,
                ArchivedConversations = archivedConversations,
                TotalMessages = totalMessages,
                TotalTokensUsed = totalTokensUsed,
                AverageMessagesPerConversation = totalConversations > 0 ? (double)totalMessages / totalConversations : 0,
                AverageTokensPerConversation = totalConversations > 0 ? (double)totalTokensUsed / totalConversations : 0
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取对话统计信息时发生错误: {UserId}", userId);
            throw;
        }
    }

    /// <summary>
    /// 重写GetByIdAsync以包含消息
    /// </summary>
    /// <param name="id">对话ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>对话</returns>
    public override async Task<Conversation?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("获取对话详情: {ConversationId}", id);

            return await _dbSet
                .Include(c => c.Messages.OrderBy(m => m.CreatedAt))
                .FirstOrDefaultAsync(c => c.Id == id, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取对话详情时发生错误: {ConversationId}", id);
            throw;
        }
    }
}
