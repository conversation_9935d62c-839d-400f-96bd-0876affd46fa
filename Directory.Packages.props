<Project>
  <PropertyGroup>
    <ManagePackageVersionsCentrally>true</ManagePackageVersionsCentrally>
    <CentralPackageTransitivePinningEnabled>true</CentralPackageTransitivePinningEnabled>
  </PropertyGroup>

  <ItemGroup>
    <!-- Microsoft Core -->
    <PackageVersion Include="Microsoft.Extensions.Logging" Version="9.0.0" />
    <PackageVersion Include="Microsoft.Extensions.DependencyInjection" Version="9.0.0" />
    <PackageVersion Include="Microsoft.Extensions.Configuration" Version="9.0.0" />
    <PackageVersion Include="Microsoft.Extensions.Configuration.Abstractions" Version="9.0.0" />
    <PackageVersion Include="Microsoft.Extensions.Options" Version="9.0.0" />
    <PackageVersion Include="Microsoft.Extensions.Hosting" Version="9.0.0" />
    <PackageVersion Include="Microsoft.Extensions.Http" Version="9.0.0" />
    <PackageVersion Include="Microsoft.Extensions.Caching.Memory" Version="9.0.0" />
    <PackageVersion Include="Microsoft.Extensions.Caching.StackExchangeRedis" Version="9.0.0" />
    <PackageVersion Include="Microsoft.Extensions.Primitives" Version="9.0.0" />
    <PackageVersion Include="Microsoft.Extensions.Identity.Core" Version="9.0.0" />
    <PackageVersion Include="Microsoft.Extensions.Logging.Abstractions" Version="9.0.0" />
    <PackageVersion Include="Microsoft.Extensions.DependencyInjection.Abstractions" Version="9.0.0" />

    <!-- ASP.NET Core -->
    <PackageVersion Include="Microsoft.AspNetCore.App" Version="9.0.0" />
    <PackageVersion Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="9.0.0" />
    <PackageVersion Include="Microsoft.AspNetCore.Identity.EntityFrameworkCore" Version="9.0.0" />
    <PackageVersion Include="Microsoft.AspNetCore.SignalR" Version="9.0.0" />
    <PackageVersion Include="Microsoft.AspNetCore.OpenApi" Version="9.0.0" />

    <!-- Entity Framework Core -->
    <PackageVersion Include="Microsoft.EntityFrameworkCore" Version="9.0.0" />
    <PackageVersion Include="Microsoft.EntityFrameworkCore.Design" Version="9.0.0" />
    <PackageVersion Include="Microsoft.EntityFrameworkCore.Tools" Version="9.0.0" />
    <PackageVersion Include="Microsoft.EntityFrameworkCore.SqlServer" Version="9.0.0" />
    <PackageVersion Include="Npgsql.EntityFrameworkCore.PostgreSQL" Version="9.0.0" />
    <PackageVersion Include="Microsoft.EntityFrameworkCore.InMemory" Version="9.0.0" />

    <!-- Blazor -->
    <PackageVersion Include="Microsoft.AspNetCore.Components.Web" Version="9.0.0" />
    <PackageVersion Include="Microsoft.AspNetCore.Components.WebAssembly" Version="9.0.0" />
    <PackageVersion Include="Microsoft.AspNetCore.Components.WebAssembly.Server" Version="9.0.0" />
    <PackageVersion Include="Microsoft.AspNetCore.Components.WebAssembly.DevServer" Version="9.0.0" />

    <!-- MudBlazor -->
    <PackageVersion Include="MudBlazor" Version="6.11.2" />
    <PackageVersion Include="MudBlazor.ThemeManager" Version="1.0.8" />

    <!-- Fluxor -->
    <PackageVersion Include="Fluxor.Blazor.Web" Version="5.9.1" />
    <PackageVersion Include="Fluxor.Blazor.Web.ReduxDevTools" Version="5.9.1" />

    <!-- MediatR -->
    <PackageVersion Include="MediatR" Version="12.2.0" />
    <PackageVersion Include="MediatR.Extensions.Microsoft.DependencyInjection" Version="11.1.0" />

    <!-- FluentValidation -->
    <PackageVersion Include="FluentValidation" Version="11.9.0" />
    <PackageVersion Include="FluentValidation.AspNetCore" Version="11.3.0" />
    <PackageVersion Include="FluentValidation.DependencyInjectionExtensions" Version="11.9.0" />

    <!-- AutoMapper -->
    <PackageVersion Include="AutoMapper" Version="12.0.1" />
    <PackageVersion Include="AutoMapper.Extensions.Microsoft.DependencyInjection" Version="12.0.1" />

    <!-- AI & ML -->
    <PackageVersion Include="Microsoft.SemanticKernel" Version="1.4.0" />
    <PackageVersion Include="Microsoft.SemanticKernel.Plugins.Core" Version="1.4.0-alpha" />
    <PackageVersion Include="Microsoft.SemanticKernel.Connectors.OpenAI" Version="1.4.0" />

    <!-- Data Access -->
    <PackageVersion Include="Dapper" Version="2.1.24" />
    <PackageVersion Include="MongoDB.Driver" Version="2.23.1" />
    <PackageVersion Include="StackExchange.Redis" Version="2.7.10" />

    <!-- Messaging -->
    <PackageVersion Include="RabbitMQ.Client" Version="6.8.1" />

    <!-- Logging & Monitoring -->
    <PackageVersion Include="Serilog" Version="3.1.1" />
    <PackageVersion Include="Serilog.AspNetCore" Version="8.0.0" />
    <PackageVersion Include="Serilog.Sinks.Console" Version="5.0.1" />
    <PackageVersion Include="Serilog.Sinks.File" Version="5.0.0" />
    <PackageVersion Include="Serilog.Sinks.Elasticsearch" Version="9.0.3" />
    <PackageVersion Include="Serilog.Enrichers.Environment" Version="2.3.0" />
    <PackageVersion Include="Serilog.Enrichers.Process" Version="2.0.2" />
    <PackageVersion Include="Serilog.Enrichers.Thread" Version="3.1.0" />

    <!-- OpenTelemetry -->
    <PackageVersion Include="OpenTelemetry" Version="1.7.0" />
    <PackageVersion Include="OpenTelemetry.Extensions.Hosting" Version="1.7.0" />
    <PackageVersion Include="OpenTelemetry.Instrumentation.AspNetCore" Version="1.7.1" />
    <PackageVersion Include="OpenTelemetry.Instrumentation.Http" Version="1.7.1" />
    <PackageVersion Include="OpenTelemetry.Instrumentation.EntityFrameworkCore" Version="1.0.0-beta.8" />
    <PackageVersion Include="OpenTelemetry.Exporter.Prometheus.AspNetCore" Version="1.7.0-rc.1" />

    <!-- Security -->
    <PackageVersion Include="Microsoft.IdentityModel.Tokens" Version="7.1.2" />
    <PackageVersion Include="System.IdentityModel.Tokens.Jwt" Version="7.1.2" />

    <!-- Utilities -->
    <PackageVersion Include="Polly" Version="8.2.0" />
    <PackageVersion Include="Polly.Extensions.Http" Version="3.0.0" />
    <PackageVersion Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageVersion Include="System.Text.Json" Version="9.0.0" />
    <PackageVersion Include="Blazored.LocalStorage" Version="4.4.0" />
    <PackageVersion Include="Blazored.SessionStorage" Version="2.4.0" />

    <!-- Health Checks -->
    <PackageVersion Include="AspNetCore.HealthChecks.UI" Version="7.0.2" />
    <PackageVersion Include="AspNetCore.HealthChecks.UI.Client" Version="7.1.0" />
    <PackageVersion Include="AspNetCore.HealthChecks.Npgsql" Version="7.1.0" />
    <PackageVersion Include="AspNetCore.HealthChecks.Redis" Version="7.1.0" />
    <PackageVersion Include="AspNetCore.HealthChecks.MongoDb" Version="7.0.0" />
    <PackageVersion Include="AspNetCore.HealthChecks.RabbitMQ" Version="7.1.0" />

    <!-- Testing -->
    <PackageVersion Include="Microsoft.NET.Test.Sdk" Version="17.8.0" />
    <PackageVersion Include="xunit" Version="2.6.4" />
    <PackageVersion Include="xunit.runner.visualstudio" Version="2.5.5" />
    <PackageVersion Include="FluentAssertions" Version="6.12.0" />
    <PackageVersion Include="Moq" Version="4.20.69" />
    <PackageVersion Include="AutoFixture" Version="4.18.1" />
    <PackageVersion Include="AutoFixture.Xunit2" Version="4.18.1" />
    <PackageVersion Include="Microsoft.AspNetCore.Mvc.Testing" Version="9.0.0" />
    <PackageVersion Include="Testcontainers.PostgreSql" Version="3.6.0" />
    <PackageVersion Include="Testcontainers.Redis" Version="3.6.0" />
    <PackageVersion Include="Testcontainers.MongoDb" Version="3.6.0" />
    <PackageVersion Include="Testcontainers.RabbitMq" Version="3.6.0" />

    <!-- Build Tools -->
    <PackageVersion Include="Microsoft.Build.Traversal" Version="3.4.0" />
  </ItemGroup>
</Project>
