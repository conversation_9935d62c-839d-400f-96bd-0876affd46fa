using AutoMapper;
using Microsoft.Extensions.Logging;
using Whimlab.AI.Agent.Platform.Application.Core.Common;
using Whimlab.AI.Agent.Platform.Application.Identity.Commands;
using Whimlab.AI.Agent.Platform.Application.Identity.DTOs;
using Whimlab.AI.Agent.Platform.Domain.Identity.Entities;
using Whimlab.AI.Agent.Platform.Domain.Identity.Repositories;
using Whimlab.AI.Agent.Platform.Domain.Identity.Services;
using Whimlab.AI.Agent.Platform.Domain.Identity.ValueObjects;

namespace Whimlab.AI.Agent.Platform.Application.Identity.Handlers;

/// <summary>
/// 创建用户命令处理器
/// </summary>
public class CreateUserCommandHandler : ICommandHandler<CreateUserCommand, UserDto>
{
    private readonly IUserRepository _userRepository;
    private readonly IRoleRepository _roleRepository;
    private readonly IUserDomainService _userDomainService;
    private readonly IMapper _mapper;
    private readonly ILogger<CreateUserCommandHandler> _logger;

    /// <summary>
    /// 初始化创建用户命令处理器
    /// </summary>
    /// <param name="userRepository">用户仓储</param>
    /// <param name="roleRepository">角色仓储</param>
    /// <param name="userDomainService">用户领域服务</param>
    /// <param name="mapper">映射器</param>
    /// <param name="logger">日志记录器</param>
    public CreateUserCommandHandler(
        IUserRepository userRepository,
        IRoleRepository roleRepository,
        IUserDomainService userDomainService,
        IMapper mapper,
        ILogger<CreateUserCommandHandler> logger)
    {
        _userRepository = userRepository;
        _roleRepository = roleRepository;
        _userDomainService = userDomainService;
        _mapper = mapper;
        _logger = logger;
    }

    /// <summary>
    /// 处理创建用户命令
    /// </summary>
    /// <param name="request">命令请求</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>用户DTO</returns>
    public async Task<Result<UserDto>> Handle(CreateUserCommand request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("开始创建用户: {Username}", request.Username);

            // 检查用户名是否可用
            var isUsernameAvailable = await _userDomainService.IsUsernameAvailableAsync(
                request.Username, cancellationToken: cancellationToken);
            if (!isUsernameAvailable)
            {
                return Result.Failure<UserDto>("用户名已存在", "USERNAME_EXISTS");
            }

            // 检查邮箱是否可用
            var isEmailAvailable = await _userDomainService.IsEmailAvailableAsync(
                request.Email, cancellationToken: cancellationToken);
            if (!isEmailAvailable)
            {
                return Result.Failure<UserDto>("邮箱地址已存在", "EMAIL_EXISTS");
            }

            // 检查手机号是否可用（如果提供）
            if (!string.IsNullOrWhiteSpace(request.PhoneNumber))
            {
                var isPhoneAvailable = await _userDomainService.IsPhoneNumberAvailableAsync(
                    request.PhoneNumber, cancellationToken: cancellationToken);
                if (!isPhoneAvailable)
                {
                    return Result.Failure<UserDto>("手机号已存在", "PHONE_EXISTS");
                }
            }

            // 验证密码策略
            var passwordPolicyResult = _userDomainService.ValidatePasswordPolicy(request.Password);
            if (!passwordPolicyResult.IsValid)
            {
                var errorMessage = string.Join("; ", passwordPolicyResult.Errors);
                return Result.Failure<UserDto>(errorMessage, "PASSWORD_POLICY_VIOLATION");
            }

            // 创建值对象
            var email = Email.Create(request.Email);
            var password = Password.Create(request.Password);
            PhoneNumber? phoneNumber = null;
            
            if (!string.IsNullOrWhiteSpace(request.PhoneNumber))
            {
                phoneNumber = PhoneNumber.Create(request.PhoneNumber);
            }

            // 创建用户实体
            var user = User.Create(
                request.Username,
                email,
                password,
                request.DisplayName ?? request.Username,
                request.UserType,
                phoneNumber);

            // 保存用户
            await _userRepository.AddAsync(user, cancellationToken);
            await _userRepository.SaveChangesAsync(cancellationToken);

            // 分配角色
            if (request.RoleIds.Any())
            {
                foreach (var roleId in request.RoleIds)
                {
                    await _userRepository.AssignRoleAsync(user.Id, roleId, cancellationToken: cancellationToken);
                }
                await _userRepository.SaveChangesAsync(cancellationToken);
            }

            // 重新获取用户
            var createdUser = await _userRepository.GetByIdAsync(user.Id, cancellationToken);
            if (createdUser == null)
            {
                return Result.Failure<UserDto>("用户创建失败", "USER_CREATION_FAILED");
            }

            var userDto = _mapper.Map<UserDto>(createdUser);

            _logger.LogInformation("用户创建成功: {UserId} - {Username}", user.Id, request.Username);

            return Result.Success(userDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "创建用户时发生错误: {Username}", request.Username);
            return Result.Failure<UserDto>("创建用户时发生内部错误", "INTERNAL_ERROR");
        }
    }
}

/// <summary>
/// 更新用户命令处理器
/// </summary>
public class UpdateUserCommandHandler : ICommandHandler<UpdateUserCommand, UserDto>
{
    private readonly IUserRepository _userRepository;
    private readonly IUserDomainService _userDomainService;
    private readonly IMapper _mapper;
    private readonly ILogger<UpdateUserCommandHandler> _logger;

    /// <summary>
    /// 初始化更新用户命令处理器
    /// </summary>
    /// <param name="userRepository">用户仓储</param>
    /// <param name="userDomainService">用户领域服务</param>
    /// <param name="mapper">映射器</param>
    /// <param name="logger">日志记录器</param>
    public UpdateUserCommandHandler(
        IUserRepository userRepository,
        IUserDomainService userDomainService,
        IMapper mapper,
        ILogger<UpdateUserCommandHandler> logger)
    {
        _userRepository = userRepository;
        _userDomainService = userDomainService;
        _mapper = mapper;
        _logger = logger;
    }

    /// <summary>
    /// 处理更新用户命令
    /// </summary>
    /// <param name="request">命令请求</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>用户DTO</returns>
    public async Task<Result<UserDto>> Handle(UpdateUserCommand request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("开始更新用户: {UserId}", request.Id);

            // 获取用户
            var user = await _userRepository.GetByIdAsync(request.Id, cancellationToken);
            if (user == null)
            {
                return Result.Failure<UserDto>("用户不存在", "USER_NOT_FOUND");
            }

            // 检查手机号是否可用（如果提供且有变化）
            if (!string.IsNullOrWhiteSpace(request.PhoneNumber) && 
                (user.PhoneNumber == null || user.PhoneNumber.Value != request.PhoneNumber))
            {
                var isPhoneAvailable = await _userDomainService.IsPhoneNumberAvailableAsync(
                    request.PhoneNumber, request.Id, cancellationToken);
                if (!isPhoneAvailable)
                {
                    return Result.Failure<UserDto>("手机号已存在", "PHONE_EXISTS");
                }
            }

            // 更新用户信息
            PhoneNumber? phoneNumber = null;
            if (!string.IsNullOrWhiteSpace(request.PhoneNumber))
            {
                phoneNumber = PhoneNumber.Create(request.PhoneNumber);
            }

            user.UpdateProfile(request.DisplayName ?? user.DisplayName, request.AvatarUrl);

            // 注意：手机号更新需要在领域层添加相应方法，这里暂时跳过

            // 保存更改
            await _userRepository.UpdateAsync(user, cancellationToken);
            await _userRepository.SaveChangesAsync(cancellationToken);

            // 重新获取用户
            var updatedUser = await _userRepository.GetByIdAsync(user.Id, cancellationToken);
            var userDto = _mapper.Map<UserDto>(updatedUser);

            _logger.LogInformation("用户更新成功: {UserId}", request.Id);

            return Result.Success(userDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新用户时发生错误: {UserId}", request.Id);
            return Result.Failure<UserDto>("更新用户时发生内部错误", "INTERNAL_ERROR");
        }
    }
}

/// <summary>
/// 删除用户命令处理器
/// </summary>
public class DeleteUserCommandHandler : ICommandHandler<DeleteUserCommand>
{
    private readonly IUserRepository _userRepository;
    private readonly ILogger<DeleteUserCommandHandler> _logger;

    /// <summary>
    /// 初始化删除用户命令处理器
    /// </summary>
    public DeleteUserCommandHandler(
        IUserRepository userRepository,
        ILogger<DeleteUserCommandHandler> logger)
    {
        _userRepository = userRepository;
        _logger = logger;
    }

    /// <summary>
    /// 处理删除用户命令
    /// </summary>
    public async Task<Result> Handle(DeleteUserCommand request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("开始删除用户: {UserId}", request.Id);

            var user = await _userRepository.GetByIdAsync(request.Id, cancellationToken);
            if (user == null)
            {
                return Result.Failure("用户不存在", "USER_NOT_FOUND");
            }

            await _userRepository.DeleteAsync(user, cancellationToken);
            await _userRepository.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("用户删除成功: {UserId}", request.Id);
            return Result.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "删除用户时发生错误: {UserId}", request.Id);
            return Result.Failure("删除用户时发生内部错误", "INTERNAL_ERROR");
        }
    }
}

/// <summary>
/// 激活用户命令处理器
/// </summary>
public class ActivateUserCommandHandler : ICommandHandler<ActivateUserCommand>
{
    private readonly IUserRepository _userRepository;
    private readonly ILogger<ActivateUserCommandHandler> _logger;

    /// <summary>
    /// 初始化激活用户命令处理器
    /// </summary>
    public ActivateUserCommandHandler(
        IUserRepository userRepository,
        ILogger<ActivateUserCommandHandler> logger)
    {
        _userRepository = userRepository;
        _logger = logger;
    }

    /// <summary>
    /// 处理激活用户命令
    /// </summary>
    public async Task<Result> Handle(ActivateUserCommand request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("开始激活用户: {UserId}", request.Id);

            var user = await _userRepository.GetByIdAsync(request.Id, cancellationToken);
            if (user == null)
            {
                return Result.Failure("用户不存在", "USER_NOT_FOUND");
            }

            user.Activate();

            await _userRepository.UpdateAsync(user, cancellationToken);
            await _userRepository.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("用户激活成功: {UserId}", request.Id);
            return Result.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "激活用户时发生错误: {UserId}", request.Id);
            return Result.Failure("激活用户时发生内部错误", "INTERNAL_ERROR");
        }
    }
}

/// <summary>
/// 停用用户命令处理器
/// </summary>
public class DeactivateUserCommandHandler : ICommandHandler<DeactivateUserCommand>
{
    private readonly IUserRepository _userRepository;
    private readonly ILogger<DeactivateUserCommandHandler> _logger;

    /// <summary>
    /// 初始化停用用户命令处理器
    /// </summary>
    public DeactivateUserCommandHandler(
        IUserRepository userRepository,
        ILogger<DeactivateUserCommandHandler> logger)
    {
        _userRepository = userRepository;
        _logger = logger;
    }

    /// <summary>
    /// 处理停用用户命令
    /// </summary>
    public async Task<Result> Handle(DeactivateUserCommand request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("开始停用用户: {UserId}", request.Id);

            var user = await _userRepository.GetByIdAsync(request.Id, cancellationToken);
            if (user == null)
            {
                return Result.Failure("用户不存在", "USER_NOT_FOUND");
            }

            user.Deactivate();

            await _userRepository.UpdateAsync(user, cancellationToken);
            await _userRepository.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("用户停用成功: {UserId}", request.Id);
            return Result.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "停用用户时发生错误: {UserId}", request.Id);
            return Result.Failure("停用用户时发生内部错误", "INTERNAL_ERROR");
        }
    }
}
