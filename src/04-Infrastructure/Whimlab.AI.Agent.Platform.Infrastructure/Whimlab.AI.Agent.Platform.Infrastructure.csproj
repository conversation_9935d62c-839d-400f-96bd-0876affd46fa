﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <GenerateDocumentationFile>true</GenerateDocumentationFile>
    <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
    <WarningsAsErrors />
    <WarningsNotAsErrors>CS1591</WarningsNotAsErrors>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.EntityFrameworkCore" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.Extensions.Configuration.Abstractions" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions" />
    <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\03-Domain\Whimlab.AI.Agent.Platform.Domain.Identity\Whimlab.AI.Agent.Platform.Domain.Identity.csproj" />
    <ProjectReference Include="..\..\03-Domain\Whimlab.AI.Agent.Platform.Domain.Agent\Whimlab.AI.Agent.Platform.Domain.Agent.csproj" />
    <ProjectReference Include="..\..\03-Domain\Whimlab.AI.Agent.Platform.Domain.Conversation\Whimlab.AI.Agent.Platform.Domain.Conversation.csproj" />
    <ProjectReference Include="..\..\03-Domain\Whimlab.AI.Agent.Platform.Domain.Subscription\Whimlab.AI.Agent.Platform.Domain.Subscription.csproj" />
    <ProjectReference Include="..\..\05-Shared\Whimlab.AI.Agent.Platform.Shared.Common\Whimlab.AI.Agent.Platform.Shared.Common.csproj" />
  </ItemGroup>

</Project>
