using System.Text.RegularExpressions;
using Whimlab.AI.Agent.Platform.Domain.Core.Common;
using Whimlab.AI.Agent.Platform.Shared.Common.Extensions;

namespace Whimlab.AI.Agent.Platform.Domain.Identity.ValueObjects;

/// <summary>
/// 手机号码值对象
/// </summary>
public sealed class PhoneNumber : SingleValueObject<string>
{
    private static readonly Regex PhoneRegex = new(
        @"^1[3-9]\d{9}$",
        RegexOptions.Compiled);

    /// <summary>
    /// 初始化手机号码
    /// </summary>
    /// <param name="value">手机号码</param>
    private PhoneNumber(string value) : base(value)
    {
    }

    /// <summary>
    /// 创建手机号码
    /// </summary>
    /// <param name="phoneNumber">手机号码</param>
    /// <returns>手机号码值对象</returns>
    /// <exception cref="ArgumentException">手机号码格式无效</exception>
    public static PhoneNumber Create(string phoneNumber)
    {
        if (phoneNumber.IsNullOrWhiteSpace())
            throw new ArgumentException("手机号码不能为空", nameof(phoneNumber));

        var normalizedPhone = phoneNumber.Trim();

        if (!PhoneRegex.IsMatch(normalizedPhone))
            throw new ArgumentException($"手机号码格式无效: {phoneNumber}", nameof(phoneNumber));

        return new PhoneNumber(normalizedPhone);
    }

    /// <summary>
    /// 尝试创建手机号码
    /// </summary>
    /// <param name="phoneNumber">手机号码</param>
    /// <param name="result">创建结果</param>
    /// <returns>是否创建成功</returns>
    public static bool TryCreate(string phoneNumber, out PhoneNumber? result)
    {
        result = null;
        
        try
        {
            result = Create(phoneNumber);
            return true;
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// 获取运营商类型
    /// </summary>
    /// <returns>运营商类型</returns>
    public string GetCarrierType()
    {
        var prefix = Value[..3];
        
        return prefix switch
        {
            "134" or "135" or "136" or "137" or "138" or "139" or "147" or "150" or "151" or "152" or "157" or "158" or "159" or "178" or "182" or "183" or "184" or "187" or "188" or "198" => "中国移动",
            "130" or "131" or "132" or "145" or "155" or "156" or "166" or "171" or "175" or "176" or "185" or "186" or "196" => "中国联通",
            "133" or "149" or "153" or "173" or "177" or "180" or "181" or "189" or "191" or "193" or "199" => "中国电信",
            _ => "未知运营商"
        };
    }

    /// <summary>
    /// 掩码手机号码
    /// </summary>
    /// <returns>掩码后的手机号码</returns>
    public string ToMaskedString()
    {
        return Value.Mask(3, 4);
    }

    /// <summary>
    /// 隐式转换为字符串
    /// </summary>
    /// <param name="phoneNumber">手机号码</param>
    /// <returns>手机号码字符串</returns>
    public static implicit operator string(PhoneNumber phoneNumber) => phoneNumber.Value;

    /// <summary>
    /// 显式转换为手机号码
    /// </summary>
    /// <param name="phoneNumber">手机号码字符串</param>
    /// <returns>手机号码值对象</returns>
    public static explicit operator PhoneNumber(string phoneNumber) => Create(phoneNumber);
}
