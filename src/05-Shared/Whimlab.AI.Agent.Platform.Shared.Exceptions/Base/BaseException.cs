using System.Runtime.Serialization;

namespace Whimlab.AI.Agent.Platform.Shared.Exceptions.Base;

/// <summary>
/// 基础异常类
/// </summary>
[Serializable]
public abstract class BaseException : Exception
{
    /// <summary>
    /// 错误代码
    /// </summary>
    public string ErrorCode { get; }

    /// <summary>
    /// 错误详情
    /// </summary>
    public object? Details { get; }

    /// <summary>
    /// 初始化基础异常
    /// </summary>
    /// <param name="errorCode">错误代码</param>
    /// <param name="message">错误消息</param>
    /// <param name="details">错误详情</param>
    protected BaseException(string errorCode, string message, object? details = null)
        : base(message)
    {
        ErrorCode = errorCode;
        Details = details;
    }

    /// <summary>
    /// 初始化基础异常
    /// </summary>
    /// <param name="errorCode">错误代码</param>
    /// <param name="message">错误消息</param>
    /// <param name="innerException">内部异常</param>
    /// <param name="details">错误详情</param>
    protected BaseException(string errorCode, string message, Exception innerException, object? details = null)
        : base(message, innerException)
    {
        ErrorCode = errorCode;
        Details = details;
    }

    /// <summary>
    /// 序列化构造函数
    /// </summary>
    /// <param name="info">序列化信息</param>
    /// <param name="context">流上下文</param>
    [Obsolete("This API supports obsolete formatter-based serialization. It should not be called or extended by application code.", DiagnosticId = "SYSLIB0051", UrlFormat = "https://aka.ms/dotnet-warnings/{0}")]
    protected BaseException(SerializationInfo info, StreamingContext context)
        : base(info, context)
    {
        ErrorCode = info.GetString(nameof(ErrorCode)) ?? string.Empty;
        Details = info.GetValue(nameof(Details), typeof(object));
    }

    /// <summary>
    /// 获取对象数据
    /// </summary>
    /// <param name="info">序列化信息</param>
    /// <param name="context">流上下文</param>
    [Obsolete("This API supports obsolete formatter-based serialization. It should not be called or extended by application code.", DiagnosticId = "SYSLIB0051", UrlFormat = "https://aka.ms/dotnet-warnings/{0}")]
    public override void GetObjectData(SerializationInfo info, StreamingContext context)
    {
        base.GetObjectData(info, context);
        info.AddValue(nameof(ErrorCode), ErrorCode);
        info.AddValue(nameof(Details), Details);
    }

    /// <summary>
    /// 转换为字符串
    /// </summary>
    /// <returns>异常的字符串表示</returns>
    public override string ToString()
    {
        var result = $"[{ErrorCode}] {Message}";
        
        if (Details != null)
        {
            result += $"\nDetails: {Details}";
        }
        
        if (InnerException != null)
        {
            result += $"\nInner Exception: {InnerException}";
        }
        
        return result;
    }
}

/// <summary>
/// 业务异常基类
/// </summary>
[Serializable]
public abstract class BusinessException : BaseException
{
    /// <summary>
    /// 初始化业务异常
    /// </summary>
    /// <param name="errorCode">错误代码</param>
    /// <param name="message">错误消息</param>
    /// <param name="details">错误详情</param>
    protected BusinessException(string errorCode, string message, object? details = null)
        : base(errorCode, message, details)
    {
    }

    /// <summary>
    /// 初始化业务异常
    /// </summary>
    /// <param name="errorCode">错误代码</param>
    /// <param name="message">错误消息</param>
    /// <param name="innerException">内部异常</param>
    /// <param name="details">错误详情</param>
    protected BusinessException(string errorCode, string message, Exception innerException, object? details = null)
        : base(errorCode, message, innerException, details)
    {
    }

    /// <summary>
    /// 序列化构造函数
    /// </summary>
    /// <param name="info">序列化信息</param>
    /// <param name="context">流上下文</param>
    [Obsolete("This API supports obsolete formatter-based serialization. It should not be called or extended by application code.", DiagnosticId = "SYSLIB0051", UrlFormat = "https://aka.ms/dotnet-warnings/{0}")]
    protected BusinessException(SerializationInfo info, StreamingContext context)
        : base(info, context)
    {
    }
}

/// <summary>
/// 验证异常基类
/// </summary>
[Serializable]
public abstract class ValidationException : BaseException
{
    /// <summary>
    /// 验证错误列表
    /// </summary>
    public IReadOnlyList<ValidationError> ValidationErrors { get; }

    /// <summary>
    /// 初始化验证异常
    /// </summary>
    /// <param name="errorCode">错误代码</param>
    /// <param name="message">错误消息</param>
    /// <param name="validationErrors">验证错误列表</param>
    protected ValidationException(string errorCode, string message, IEnumerable<ValidationError> validationErrors)
        : base(errorCode, message, validationErrors)
    {
        ValidationErrors = validationErrors.ToList().AsReadOnly();
    }

    /// <summary>
    /// 序列化构造函数
    /// </summary>
    /// <param name="info">序列化信息</param>
    /// <param name="context">流上下文</param>
    [Obsolete("This API supports obsolete formatter-based serialization. It should not be called or extended by application code.", DiagnosticId = "SYSLIB0051", UrlFormat = "https://aka.ms/dotnet-warnings/{0}")]
    protected ValidationException(SerializationInfo info, StreamingContext context)
        : base(info, context)
    {
        var errors = info.GetValue(nameof(ValidationErrors), typeof(List<ValidationError>)) as List<ValidationError>;
        ValidationErrors = errors?.AsReadOnly() ?? new List<ValidationError>().AsReadOnly();
    }

    /// <summary>
    /// 获取对象数据
    /// </summary>
    /// <param name="info">序列化信息</param>
    /// <param name="context">流上下文</param>
    [Obsolete("This API supports obsolete formatter-based serialization. It should not be called or extended by application code.", DiagnosticId = "SYSLIB0051", UrlFormat = "https://aka.ms/dotnet-warnings/{0}")]
    public override void GetObjectData(SerializationInfo info, StreamingContext context)
    {
        base.GetObjectData(info, context);
        info.AddValue(nameof(ValidationErrors), ValidationErrors.ToList());
    }
}

/// <summary>
/// 基础设施异常基类
/// </summary>
[Serializable]
public abstract class InfrastructureException : BaseException
{
    /// <summary>
    /// 初始化基础设施异常
    /// </summary>
    /// <param name="errorCode">错误代码</param>
    /// <param name="message">错误消息</param>
    /// <param name="details">错误详情</param>
    protected InfrastructureException(string errorCode, string message, object? details = null)
        : base(errorCode, message, details)
    {
    }

    /// <summary>
    /// 初始化基础设施异常
    /// </summary>
    /// <param name="errorCode">错误代码</param>
    /// <param name="message">错误消息</param>
    /// <param name="innerException">内部异常</param>
    /// <param name="details">错误详情</param>
    protected InfrastructureException(string errorCode, string message, Exception innerException, object? details = null)
        : base(errorCode, message, innerException, details)
    {
    }

    /// <summary>
    /// 序列化构造函数
    /// </summary>
    /// <param name="info">序列化信息</param>
    /// <param name="context">流上下文</param>
    [Obsolete("This API supports obsolete formatter-based serialization. It should not be called or extended by application code.", DiagnosticId = "SYSLIB0051", UrlFormat = "https://aka.ms/dotnet-warnings/{0}")]
    protected InfrastructureException(SerializationInfo info, StreamingContext context)
        : base(info, context)
    {
    }
}

/// <summary>
/// 验证错误
/// </summary>
[Serializable]
public class ValidationError
{
    /// <summary>
    /// 属性名称
    /// </summary>
    public string PropertyName { get; }

    /// <summary>
    /// 错误消息
    /// </summary>
    public string ErrorMessage { get; }

    /// <summary>
    /// 错误代码
    /// </summary>
    public string? ErrorCode { get; }

    /// <summary>
    /// 尝试的值
    /// </summary>
    public object? AttemptedValue { get; }

    /// <summary>
    /// 初始化验证错误
    /// </summary>
    /// <param name="propertyName">属性名称</param>
    /// <param name="errorMessage">错误消息</param>
    /// <param name="errorCode">错误代码</param>
    /// <param name="attemptedValue">尝试的值</param>
    public ValidationError(string propertyName, string errorMessage, string? errorCode = null, object? attemptedValue = null)
    {
        PropertyName = propertyName;
        ErrorMessage = errorMessage;
        ErrorCode = errorCode;
        AttemptedValue = attemptedValue;
    }

    /// <summary>
    /// 转换为字符串
    /// </summary>
    /// <returns>验证错误的字符串表示</returns>
    public override string ToString()
    {
        var result = $"{PropertyName}: {ErrorMessage}";
        
        if (!string.IsNullOrEmpty(ErrorCode))
        {
            result = $"[{ErrorCode}] {result}";
        }
        
        if (AttemptedValue != null)
        {
            result += $" (Attempted value: {AttemptedValue})";
        }
        
        return result;
    }
}
