namespace Whimlab.AI.Agent.Platform.Infrastructure.Models.Statistics;

/// <summary>
/// 统计信息基类
/// </summary>
public abstract class BaseStatistics
{
    /// <summary>
    /// 统计开始时间
    /// </summary>
    public DateTime StartDate { get; set; }

    /// <summary>
    /// 统计结束时间
    /// </summary>
    public DateTime EndDate { get; set; }

    /// <summary>
    /// 统计生成时间
    /// </summary>
    public DateTime GeneratedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 统计类型
    /// </summary>
    public abstract string StatisticsType { get; }
}

/// <summary>
/// 消息统计信息
/// </summary>
public class MessageStatistics : BaseStatistics
{
    /// <summary>
    /// 统计类型
    /// </summary>
    public override string StatisticsType => "Message";

    /// <summary>
    /// 对话ID
    /// </summary>
    public Guid ConversationId { get; set; }

    /// <summary>
    /// 总消息数
    /// </summary>
    public int TotalMessages { get; set; }

    /// <summary>
    /// 用户消息数
    /// </summary>
    public int UserMessages { get; set; }

    /// <summary>
    /// 助手消息数
    /// </summary>
    public int AssistantMessages { get; set; }

    /// <summary>
    /// 系统消息数
    /// </summary>
    public int SystemMessages { get; set; }

    /// <summary>
    /// 总Token使用量
    /// </summary>
    public long TotalTokensUsed { get; set; }

    /// <summary>
    /// 平均每条消息Token数
    /// </summary>
    public double AverageTokensPerMessage { get; set; }

    /// <summary>
    /// 附件总数
    /// </summary>
    public int TotalAttachments { get; set; }

    /// <summary>
    /// 附件总大小（字节）
    /// </summary>
    public long TotalAttachmentSize { get; set; }
}

/// <summary>
/// Token使用统计信息
/// </summary>
public class TokenUsageStatistics : BaseStatistics
{
    /// <summary>
    /// 统计类型
    /// </summary>
    public override string StatisticsType => "TokenUsage";

    /// <summary>
    /// 对话ID
    /// </summary>
    public Guid ConversationId { get; set; }

    /// <summary>
    /// 总Token数
    /// </summary>
    public long TotalTokens { get; set; }

    /// <summary>
    /// 用户Token数
    /// </summary>
    public long UserTokens { get; set; }

    /// <summary>
    /// 助手Token数
    /// </summary>
    public long AssistantTokens { get; set; }

    /// <summary>
    /// 消息数量
    /// </summary>
    public int MessageCount { get; set; }

    /// <summary>
    /// 对话数量
    /// </summary>
    public int ConversationCount { get; set; }

    /// <summary>
    /// 平均每对话Token数
    /// </summary>
    public double AverageTokensPerConversation { get; set; }

    /// <summary>
    /// 每日使用量列表
    /// </summary>
    public List<DailyTokenUsage> DailyUsages { get; set; } = new();
}

/// <summary>
/// 每日Token使用量
/// </summary>
public class DailyTokenUsage
{
    /// <summary>
    /// 日期
    /// </summary>
    public DateTime Date { get; set; }

    /// <summary>
    /// Token使用量
    /// </summary>
    public long TokensUsed { get; set; }

    /// <summary>
    /// 消息数量
    /// </summary>
    public int MessageCount { get; set; }

    /// <summary>
    /// 对话数量
    /// </summary>
    public int ConversationCount { get; set; }
}

/// <summary>
/// 智能体统计信息
/// </summary>
public class AgentStatistics : BaseStatistics
{
    /// <summary>
    /// 统计类型
    /// </summary>
    public override string StatisticsType => "Agent";

    /// <summary>
    /// 总数量
    /// </summary>
    public int TotalCount { get; set; }

    /// <summary>
    /// 已发布数量
    /// </summary>
    public int PublishedCount { get; set; }

    /// <summary>
    /// 草稿数量
    /// </summary>
    public int DraftCount { get; set; }

    /// <summary>
    /// 已归档数量
    /// </summary>
    public int ArchivedCount { get; set; }

    /// <summary>
    /// 总使用次数
    /// </summary>
    public long TotalUsageCount { get; set; }

    /// <summary>
    /// 平均评分
    /// </summary>
    public double AverageRating { get; set; }

    /// <summary>
    /// 按类型分组的数量
    /// </summary>
    public Dictionary<string, int> CountByType { get; set; } = new();

    /// <summary>
    /// 按分类分组的数量
    /// </summary>
    public Dictionary<string, int> CountByCategory { get; set; } = new();
}

/// <summary>
/// 配额使用统计信息
/// </summary>
public class QuotaUsageStatistics : BaseStatistics
{
    /// <summary>
    /// 统计类型
    /// </summary>
    public override string StatisticsType => "QuotaUsage";

    /// <summary>
    /// 客户ID
    /// </summary>
    public Guid CustomerId { get; set; }

    /// <summary>
    /// 配额类型
    /// </summary>
    public string QuotaType { get; set; } = string.Empty;

    /// <summary>
    /// 总使用量
    /// </summary>
    public long TotalUsage { get; set; }

    /// <summary>
    /// 配额限制
    /// </summary>
    public long QuotaLimit { get; set; }

    /// <summary>
    /// 剩余配额
    /// </summary>
    public long RemainingQuota => QuotaLimit - TotalUsage;

    /// <summary>
    /// 使用率
    /// </summary>
    public double UsagePercentage => QuotaLimit > 0 ? (double)TotalUsage / QuotaLimit * 100 : 0;

    /// <summary>
    /// 每日使用量列表
    /// </summary>
    public List<DailyUsage> DailyUsages { get; set; } = new();
}

/// <summary>
/// 每日使用量
/// </summary>
public class DailyUsage
{
    /// <summary>
    /// 日期
    /// </summary>
    public DateTime Date { get; set; }

    /// <summary>
    /// 使用量
    /// </summary>
    public long Usage { get; set; }
}

/// <summary>
/// 订阅计划使用统计信息
/// </summary>
public class SubscriptionPlanUsageStatistics : BaseStatistics
{
    /// <summary>
    /// 统计类型
    /// </summary>
    public override string StatisticsType => "SubscriptionPlanUsage";

    /// <summary>
    /// 计划ID
    /// </summary>
    public Guid PlanId { get; set; }

    /// <summary>
    /// 计划名称
    /// </summary>
    public string PlanName { get; set; } = string.Empty;

    /// <summary>
    /// 活跃订阅数
    /// </summary>
    public int ActiveSubscriptions { get; set; }

    /// <summary>
    /// 总订阅数
    /// </summary>
    public int TotalSubscriptions { get; set; }

    /// <summary>
    /// 总收入
    /// </summary>
    public decimal TotalRevenue { get; set; }

    /// <summary>
    /// 平均订阅时长（天）
    /// </summary>
    public double AverageSubscriptionDuration { get; set; }

    /// <summary>
    /// 流失率
    /// </summary>
    public double ChurnRate { get; set; }
}

/// <summary>
/// 订阅收入统计信息
/// </summary>
public class SubscriptionRevenueStatistics : BaseStatistics
{
    /// <summary>
    /// 统计类型
    /// </summary>
    public override string StatisticsType => "SubscriptionRevenue";

    /// <summary>
    /// 总收入
    /// </summary>
    public decimal TotalRevenue { get; set; }

    /// <summary>
    /// 月度经常性收入
    /// </summary>
    public decimal MonthlyRecurringRevenue { get; set; }

    /// <summary>
    /// 年度经常性收入
    /// </summary>
    public decimal AnnualRecurringRevenue { get; set; }

    /// <summary>
    /// 平均每用户收入
    /// </summary>
    public decimal AverageRevenuePerUser { get; set; }

    /// <summary>
    /// 客户生命周期价值
    /// </summary>
    public decimal CustomerLifetimeValue { get; set; }

    /// <summary>
    /// 按计划分组的收入
    /// </summary>
    public Dictionary<string, decimal> RevenueByPlan { get; set; } = new();

    /// <summary>
    /// 按月份分组的收入
    /// </summary>
    public Dictionary<string, decimal> RevenueByMonth { get; set; } = new();
}
