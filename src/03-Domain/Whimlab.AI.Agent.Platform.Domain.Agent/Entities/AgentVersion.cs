using Whimlab.AI.Agent.Platform.Domain.Core.Common;
using Whimlab.AI.Agent.Platform.Domain.Agent.ValueObjects;

namespace Whimlab.AI.Agent.Platform.Domain.Agent.Entities;

/// <summary>
/// 智能体版本实体
/// </summary>
public class AgentVersion : Entity
{
    /// <summary>
    /// 智能体ID
    /// </summary>
    public Guid AgentId { get; private set; }

    /// <summary>
    /// 版本号
    /// </summary>
    public string Version { get; private set; } = string.Empty;

    /// <summary>
    /// 配置
    /// </summary>
    public AgentConfiguration Configuration { get; private set; } = null!;

    /// <summary>
    /// 版本说明
    /// </summary>
    public string? Notes { get; private set; }

    /// <summary>
    /// 创建者ID
    /// </summary>
    public Guid CreatedBy { get; private set; }

    /// <summary>
    /// 是否为当前版本
    /// </summary>
    public bool IsCurrent { get; private set; }

    /// <summary>
    /// 智能体导航属性
    /// </summary>
    public Agent? Agent { get; private set; }

    /// <summary>
    /// 私有构造函数（用于EF Core）
    /// </summary>
    private AgentVersion() { }

    /// <summary>
    /// 创建智能体版本
    /// </summary>
    /// <param name="agentId">智能体ID</param>
    /// <param name="version">版本号</param>
    /// <param name="configuration">配置</param>
    /// <param name="notes">版本说明</param>
    /// <param name="createdBy">创建者ID</param>
    /// <returns>智能体版本实体</returns>
    public static AgentVersion Create(
        Guid agentId,
        string version,
        AgentConfiguration configuration,
        string? notes,
        Guid createdBy)
    {
        if (agentId == Guid.Empty)
            throw new ArgumentException("智能体ID不能为空", nameof(agentId));

        if (string.IsNullOrWhiteSpace(version))
            throw new ArgumentException("版本号不能为空", nameof(version));

        if (createdBy == Guid.Empty)
            throw new ArgumentException("创建者ID不能为空", nameof(createdBy));

        return new AgentVersion
        {
            AgentId = agentId,
            Version = version.Trim(),
            Configuration = configuration,
            Notes = notes?.Trim(),
            CreatedBy = createdBy,
            IsCurrent = false
        };
    }

    /// <summary>
    /// 设置为当前版本
    /// </summary>
    public void SetAsCurrent()
    {
        IsCurrent = true;
        MarkAsModified();
    }

    /// <summary>
    /// 取消当前版本状态
    /// </summary>
    public void UnsetAsCurrent()
    {
        IsCurrent = false;
        MarkAsModified();
    }
}

/// <summary>
/// 智能体标签实体
/// </summary>
public class AgentTag : Entity
{
    /// <summary>
    /// 智能体ID
    /// </summary>
    public Guid AgentId { get; private set; }

    /// <summary>
    /// 标签名称
    /// </summary>
    public string Name { get; private set; } = string.Empty;

    /// <summary>
    /// 智能体导航属性
    /// </summary>
    public Agent? Agent { get; private set; }

    /// <summary>
    /// 私有构造函数（用于EF Core）
    /// </summary>
    private AgentTag() { }

    /// <summary>
    /// 创建智能体标签
    /// </summary>
    /// <param name="agentId">智能体ID</param>
    /// <param name="name">标签名称</param>
    /// <returns>智能体标签实体</returns>
    public static AgentTag Create(Guid agentId, string name)
    {
        if (agentId == Guid.Empty)
            throw new ArgumentException("智能体ID不能为空", nameof(agentId));

        if (string.IsNullOrWhiteSpace(name))
            throw new ArgumentException("标签名称不能为空", nameof(name));

        return new AgentTag
        {
            AgentId = agentId,
            Name = name.Trim().ToLowerInvariant()
        };
    }
}
