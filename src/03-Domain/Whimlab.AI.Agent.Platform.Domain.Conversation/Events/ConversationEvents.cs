using MediatR;
using Whimlab.AI.Agent.Platform.Shared.Common.Enums;

namespace Whimlab.AI.Agent.Platform.Domain.Conversation.Events;

/// <summary>
/// 对话创建事件
/// </summary>
/// <param name="ConversationId">对话ID</param>
/// <param name="UserId">用户ID</param>
/// <param name="AgentId">智能体ID</param>
/// <param name="Title">对话标题</param>
public record ConversationCreatedEvent(Guid ConversationId, Guid UserId, Guid AgentId, string Title) : INotification;

/// <summary>
/// 对话标题更新事件
/// </summary>
/// <param name="ConversationId">对话ID</param>
/// <param name="Title">新标题</param>
public record ConversationTitleUpdatedEvent(Guid ConversationId, string Title) : INotification;

/// <summary>
/// 对话归档事件
/// </summary>
/// <param name="ConversationId">对话ID</param>
/// <param name="UserId">用户ID</param>
/// <param name="Status">归档状态</param>
public record ConversationArchivedEvent(Guid ConversationId, Guid UserId, ConversationStatus Status) : INotification;

/// <summary>
/// 对话恢复事件
/// </summary>
/// <param name="ConversationId">对话ID</param>
/// <param name="UserId">用户ID</param>
public record ConversationRestoredEvent(Guid ConversationId, Guid UserId) : INotification;

/// <summary>
/// 消息添加事件
/// </summary>
/// <param name="ConversationId">对话ID</param>
/// <param name="MessageId">消息ID</param>
/// <param name="SenderType">发送者类型</param>
/// <param name="ContentLength">内容长度</param>
public record MessageAddedEvent(Guid ConversationId, Guid MessageId, SenderType SenderType, int ContentLength) : INotification;

/// <summary>
/// 消息删除事件
/// </summary>
/// <param name="ConversationId">对话ID</param>
/// <param name="MessageId">消息ID</param>
/// <param name="SenderType">发送者类型</param>
public record MessageDeletedEvent(Guid ConversationId, Guid MessageId, SenderType SenderType) : INotification;

/// <summary>
/// Token消耗事件
/// </summary>
/// <param name="ConversationId">对话ID</param>
/// <param name="UserId">用户ID</param>
/// <param name="AgentId">智能体ID</param>
/// <param name="TokensUsed">使用的Token数量</param>
public record TokensConsumedEvent(Guid ConversationId, Guid UserId, Guid AgentId, int TokensUsed) : INotification;
