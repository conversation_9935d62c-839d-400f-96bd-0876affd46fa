using System.Diagnostics;
using System.Net;
using System.Text.Json;
using Whimlab.AI.Agent.Platform.Web.Api.Models.Responses;
using Whimlab.AI.Agent.Platform.Shared.Exceptions.Base;
using Whimlab.AI.Agent.Platform.Domain.Core.Exceptions;
using FluentValidationException = FluentValidation.ValidationException;

namespace Whimlab.AI.Agent.Platform.Web.Api.Middleware;

/// <summary>
/// 全局异常处理中间件
/// 统一处理应用程序中的所有未捕获异常
/// </summary>
public class GlobalExceptionMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ILogger<GlobalExceptionMiddleware> _logger;
    private readonly IWebHostEnvironment _environment;

    /// <summary>
    /// 初始化全局异常处理中间件
    /// </summary>
    /// <param name="next">下一个中间件</param>
    /// <param name="logger">日志记录器</param>
    /// <param name="environment">环境信息</param>
    public GlobalExceptionMiddleware(
        RequestDelegate next,
        ILogger<GlobalExceptionMiddleware> logger,
        IWebHostEnvironment environment)
    {
        _next = next;
        _logger = logger;
        _environment = environment;
    }

    /// <summary>
    /// 处理HTTP请求
    /// </summary>
    /// <param name="context">HTTP上下文</param>
    /// <returns>异步任务</returns>
    public async Task InvokeAsync(HttpContext context)
    {
        try
        {
            await _next(context);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "处理请求时发生未捕获的异常: {RequestPath}", context.Request.Path);
            await HandleExceptionAsync(context, ex);
        }
    }

    /// <summary>
    /// 处理异常并返回统一的错误响应
    /// </summary>
    /// <param name="context">HTTP上下文</param>
    /// <param name="exception">异常信息</param>
    /// <returns>异步任务</returns>
    private async Task HandleExceptionAsync(HttpContext context, Exception exception)
    {
        var response = context.Response;
        response.ContentType = "application/json";

        var apiResponse = new ApiResponse<object>
        {
            Success = false,
            TraceId = Activity.Current?.Id ?? context.TraceIdentifier,
            Timestamp = DateTime.UtcNow
        };

        // 根据异常类型设置响应状态码和错误信息
        switch (exception)
        {
            case ValidationException validationEx:
                response.StatusCode = (int)HttpStatusCode.BadRequest;
                apiResponse.Message = "请求数据验证失败";
                apiResponse.ErrorCode = "VALIDATION_ERROR";
                apiResponse.Errors.Add(validationEx.Message);
                break;

            case UnauthorizedAccessException:
                response.StatusCode = (int)HttpStatusCode.Unauthorized;
                apiResponse.Message = "未授权访问";
                apiResponse.ErrorCode = "UNAUTHORIZED";
                break;

            case BusinessException forbiddenEx when forbiddenEx.ErrorCode == "FORBIDDEN":
                response.StatusCode = (int)HttpStatusCode.Forbidden;
                apiResponse.Message = "访问被禁止";
                apiResponse.ErrorCode = "FORBIDDEN";
                break;

            case EntityNotFoundException notFoundEx:
                response.StatusCode = (int)HttpStatusCode.NotFound;
                apiResponse.Message = notFoundEx.Message;
                apiResponse.ErrorCode = "NOT_FOUND";
                break;

            case EntityAlreadyExistsException conflictEx:
                response.StatusCode = (int)HttpStatusCode.Conflict;
                apiResponse.Message = conflictEx.Message;
                apiResponse.ErrorCode = "CONFLICT";
                break;

            case FluentValidationException fluentValidationEx:
                response.StatusCode = (int)HttpStatusCode.BadRequest;
                apiResponse.Message = "请求数据验证失败";
                apiResponse.ErrorCode = "VALIDATION_ERROR";
                apiResponse.Errors = fluentValidationEx.Errors.Select(e => e.ErrorMessage).ToList();
                break;

            case BusinessException domainEx:
                response.StatusCode = (int)HttpStatusCode.BadRequest;
                apiResponse.Message = domainEx.Message;
                apiResponse.ErrorCode = "DOMAIN_ERROR";
                break;

            case TimeoutException:
                response.StatusCode = (int)HttpStatusCode.RequestTimeout;
                apiResponse.Message = "请求超时";
                apiResponse.ErrorCode = "TIMEOUT";
                break;

            case TaskCanceledException:
                response.StatusCode = (int)HttpStatusCode.RequestTimeout;
                apiResponse.Message = "请求被取消";
                apiResponse.ErrorCode = "CANCELLED";
                break;

            default:
                response.StatusCode = (int)HttpStatusCode.InternalServerError;
                apiResponse.Message = "服务器内部错误";
                apiResponse.ErrorCode = "INTERNAL_ERROR";

                // 在开发环境中包含详细的错误信息
                if (_environment.IsDevelopment())
                {
                    apiResponse.Errors.Add($"异常类型: {exception.GetType().Name}");
                    apiResponse.Errors.Add($"异常消息: {exception.Message}");
                    if (exception.StackTrace != null)
                    {
                        apiResponse.Errors.Add($"堆栈跟踪: {exception.StackTrace}");
                    }
                }
                break;
        }

        // 序列化响应并写入HTTP响应
        var jsonResponse = JsonSerializer.Serialize(apiResponse, new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            WriteIndented = _environment.IsDevelopment()
        });

        await response.WriteAsync(jsonResponse);
    }
}
