using System.Text;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Mvc;
using Microsoft.IdentityModel.Tokens;
using FluentValidation.AspNetCore;
using FluentValidation;
using Whimlab.AI.Agent.Platform.Web.Api.Configuration;
using Whimlab.AI.Agent.Platform.Web.Api.Services;
using Whimlab.AI.Agent.Platform.Application.Core.Behaviors;
using MediatR;
using System.Security.Claims;
using Asp.Versioning;

namespace Whimlab.AI.Agent.Platform.Web.Api.Extensions;

/// <summary>
/// 服务集合扩展方法
/// </summary>
public static class ServiceCollectionExtensions
{
    /// <summary>
    /// 添加Web API服务
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <param name="configuration">配置</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddWebApiServices(
        this IServiceCollection services, 
        IConfiguration configuration)
    {
        // 添加控制器服务
        services.AddControllers(options =>
        {
            // 配置模型验证
            options.ModelValidatorProviders.Clear();
        })
        .ConfigureApiBehaviorOptions(options =>
        {
            // 自定义模型验证错误响应
            options.InvalidModelStateResponseFactory = context =>
            {
                var errors = context.ModelState.Values
                    .SelectMany(v => v.Errors)
                    .Select(e => e.ErrorMessage)
                    .ToList();

                var response = new Models.Responses.ApiResponse
                {
                    Success = false,
                    Message = "请求数据验证失败",
                    ErrorCode = "VALIDATION_ERROR",
                    Errors = errors,
                    TraceId = context.HttpContext.TraceIdentifier
                };

                return new BadRequestObjectResult(response);
            };
        });

        // 添加API版本控制
        services.AddApiVersioning(options =>
        {
            options.DefaultApiVersion = new ApiVersion(1, 0);
            options.AssumeDefaultVersionWhenUnspecified = true;
            options.ApiVersionReader = ApiVersionReader.Combine(
                new UrlSegmentApiVersionReader(),
                new HeaderApiVersionReader("X-Version"),
                new QueryStringApiVersionReader("version")
            );
        });

        services.AddApiVersioning()
                .AddApiExplorer(options =>
                {
                    options.GroupNameFormat = "'v'VVV";
                    options.SubstituteApiVersionInUrl = true;
                });

        // 添加FluentValidation
        services.AddFluentValidationAutoValidation()
                .AddFluentValidationClientsideAdapters();

        // 注册验证器
        services.AddValidatorsFromAssemblyContaining<Program>();

        // 添加AutoMapper
        services.AddAutoMapper(AppDomain.CurrentDomain.GetAssemblies());

        // 添加MediatR
        services.AddMediatR(cfg =>
        {
            cfg.RegisterServicesFromAssemblies(AppDomain.CurrentDomain.GetAssemblies());
            
            // 添加管道行为
            cfg.AddBehavior(typeof(IPipelineBehavior<,>), typeof(ValidationBehavior<,>));
            cfg.AddBehavior(typeof(IPipelineBehavior<,>), typeof(LoggingBehavior<,>));
            cfg.AddBehavior(typeof(IPipelineBehavior<,>), typeof(PerformanceBehavior<,>));
            cfg.AddBehavior(typeof(IPipelineBehavior<,>), typeof(TransactionBehavior<,>));
        });

        // 添加CORS
        services.AddCors(options =>
        {
            options.AddPolicy("DefaultPolicy", builder =>
            {
                builder.AllowAnyOrigin()
                       .AllowAnyMethod()
                       .AllowAnyHeader();
            });
        });

        // 添加健康检查
        services.AddHealthChecks()
                .AddCheck("self", () => Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckResult.Healthy());

        return services;
    }

    /// <summary>
    /// 添加JWT认证服务
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <param name="configuration">配置</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddJwtAuthentication(
        this IServiceCollection services, 
        IConfiguration configuration)
    {
        // 配置JWT设置
        var jwtSettings = new JwtSettings();
        configuration.GetSection(JwtSettings.SectionName).Bind(jwtSettings);
        services.Configure<JwtSettings>(configuration.GetSection(JwtSettings.SectionName));

        // 注册JWT服务
        services.AddScoped<IJwtService, JwtService>();

        // 配置JWT认证
        services.AddAuthentication(options =>
        {
            options.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
            options.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
            options.DefaultScheme = JwtBearerDefaults.AuthenticationScheme;
        })
        .AddJwtBearer(options =>
        {
            var key = Encoding.UTF8.GetBytes(jwtSettings.SecretKey);
            
            options.TokenValidationParameters = new TokenValidationParameters
            {
                ValidateIssuer = jwtSettings.ValidateIssuer,
                ValidateAudience = jwtSettings.ValidateAudience,
                ValidateLifetime = jwtSettings.ValidateLifetime,
                ValidateIssuerSigningKey = jwtSettings.ValidateIssuerSigningKey,
                ValidIssuer = jwtSettings.Issuer,
                ValidAudience = jwtSettings.Audience,
                IssuerSigningKey = new SymmetricSecurityKey(key),
                ClockSkew = TimeSpan.FromSeconds(jwtSettings.ClockSkewSeconds),
                RequireExpirationTime = true
            };

            options.RequireHttpsMetadata = jwtSettings.RequireHttpsMetadata;
            options.SaveToken = jwtSettings.SaveToken;

            // 配置事件处理
            options.Events = new JwtBearerEvents
            {
                OnAuthenticationFailed = context =>
                {
                    var logger = context.HttpContext.RequestServices
                        .GetRequiredService<ILogger<Program>>();
                    logger.LogWarning("JWT认证失败: {Exception}", context.Exception.Message);
                    return Task.CompletedTask;
                },
                OnTokenValidated = context =>
                {
                    var logger = context.HttpContext.RequestServices
                        .GetRequiredService<ILogger<Program>>();
                    var userId = context.Principal?.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value;
                    logger.LogDebug("JWT令牌验证成功，用户ID: {UserId}", userId);
                    return Task.CompletedTask;
                },
                OnChallenge = context =>
                {
                    var logger = context.HttpContext.RequestServices
                        .GetRequiredService<ILogger<Program>>();
                    logger.LogWarning("JWT认证质询: {Error}", context.Error);
                    return Task.CompletedTask;
                }
            };
        });

        return services;
    }

    /// <summary>
    /// 添加授权策略
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddAuthorizationPolicies(this IServiceCollection services)
    {
        services.AddAuthorization(options =>
        {
            // 基于角色的策略
            options.AddPolicy("RequireAdminRole", policy => 
                policy.RequireRole("Admin", "SuperAdmin"));
            
            options.AddPolicy("RequireManagerRole", policy => 
                policy.RequireRole("Manager", "Admin", "SuperAdmin"));

            // 基于权限的策略
            options.AddPolicy("RequireUserManagement", policy => 
                policy.RequireClaim("permission", "user:manage"));
            
            options.AddPolicy("RequireAgentManagement", policy => 
                policy.RequireClaim("permission", "agent:manage"));
            
            options.AddPolicy("RequireConversationManagement", policy => 
                policy.RequireClaim("permission", "conversation:manage"));
            
            options.AddPolicy("RequireSubscriptionManagement", policy => 
                policy.RequireClaim("permission", "subscription:manage"));

            // 组合策略
            options.AddPolicy("RequireSystemAdmin", policy =>
                policy.RequireRole("SuperAdmin")
                      .RequireClaim("permission", "system:admin"));
        });

        return services;
    }

    /// <summary>
    /// 添加应用层服务
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddApplicationServices(this IServiceCollection services)
    {
        // 注册应用层服务
        // 这里可以添加其他应用层服务的注册
        
        return services;
    }

    /// <summary>
    /// 添加基础设施服务
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <param name="configuration">配置</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddInfrastructureServices(
        this IServiceCollection services, 
        IConfiguration configuration)
    {
        // 注册基础设施服务
        // 这里可以添加数据库、缓存、消息队列等基础设施服务的注册
        
        return services;
    }
}
