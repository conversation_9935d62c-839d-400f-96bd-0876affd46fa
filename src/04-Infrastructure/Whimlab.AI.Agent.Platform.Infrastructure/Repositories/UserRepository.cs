using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Whimlab.AI.Agent.Platform.Domain.Identity.Entities;
using Whimlab.AI.Agent.Platform.Domain.Identity.Repositories;
using Whimlab.AI.Agent.Platform.Infrastructure.Data;
using Whimlab.AI.Agent.Platform.Shared.Common.Enums;

namespace Whimlab.AI.Agent.Platform.Infrastructure.Repositories;

/// <summary>
/// 用户仓储实现
/// </summary>
public class UserRepository : BaseRepository<User, Guid>, IUserRepository
{
    /// <summary>
    /// 初始化用户仓储
    /// </summary>
    /// <param name="context">数据库上下文</param>
    /// <param name="logger">日志记录器</param>
    public UserRepository(
        AgentPlatformDbContext context,
        ILogger<UserRepository> logger) : base(context, logger)
    {
    }

    /// <summary>
    /// 根据用户名获取用户
    /// </summary>
    /// <param name="username">用户名</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>用户实体</returns>
    public async Task<User?> GetByUsernameAsync(string username, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("根据用户名获取用户: {Username}", username);

            return await _dbSet
                .FirstOrDefaultAsync(u => u.Username == username, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "根据用户名获取用户时发生错误: {Username}", username);
            throw;
        }
    }

    /// <summary>
    /// 根据邮箱获取用户
    /// </summary>
    /// <param name="email">邮箱</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>用户实体</returns>
    public async Task<User?> GetByEmailAsync(string email, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("根据邮箱获取用户: {Email}", email);

            return await _dbSet
                .FirstOrDefaultAsync(u => u.Email == email, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "根据邮箱获取用户时发生错误: {Email}", email);
            throw;
        }
    }

    /// <summary>
    /// 根据电话号码获取用户
    /// </summary>
    /// <param name="phoneNumber">电话号码</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>用户实体</returns>
    public async Task<User?> GetByPhoneNumberAsync(string phoneNumber, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("根据电话号码获取用户: {PhoneNumber}", phoneNumber);

            return await _dbSet
                .FirstOrDefaultAsync(u => u.PhoneNumber == phoneNumber, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "根据电话号码获取用户时发生错误: {PhoneNumber}", phoneNumber);
            throw;
        }
    }

    /// <summary>
    /// 检查用户名是否存在
    /// </summary>
    /// <param name="username">用户名</param>
    /// <param name="excludeUserId">排除的用户ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>是否存在</returns>
    public async Task<bool> IsUsernameExistsAsync(string username, Guid? excludeUserId = null, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("检查用户名是否存在: {Username}, 排除用户ID: {ExcludeUserId}", username, excludeUserId);

            var query = _dbSet.Where(u => u.Username == username);

            if (excludeUserId.HasValue)
            {
                query = query.Where(u => u.Id != excludeUserId.Value);
            }

            return await query.AnyAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "检查用户名是否存在时发生错误: {Username}", username);
            throw;
        }
    }

    /// <summary>
    /// 检查邮箱是否存在
    /// </summary>
    /// <param name="email">邮箱</param>
    /// <param name="excludeUserId">排除的用户ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>是否存在</returns>
    public async Task<bool> IsEmailExistsAsync(string email, Guid? excludeUserId = null, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("检查邮箱是否存在: {Email}, 排除用户ID: {ExcludeUserId}", email, excludeUserId);

            var query = _dbSet.Where(u => u.Email == email);

            if (excludeUserId.HasValue)
            {
                query = query.Where(u => u.Id != excludeUserId.Value);
            }

            return await query.AnyAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "检查邮箱是否存在时发生错误: {Email}", email);
            throw;
        }
    }

    /// <summary>
    /// 检查电话号码是否存在
    /// </summary>
    /// <param name="phoneNumber">电话号码</param>
    /// <param name="excludeUserId">排除的用户ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>是否存在</returns>
    public async Task<bool> IsPhoneNumberExistsAsync(string phoneNumber, Guid? excludeUserId = null, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("检查电话号码是否存在: {PhoneNumber}, 排除用户ID: {ExcludeUserId}", phoneNumber, excludeUserId);

            var query = _dbSet.Where(u => u.PhoneNumber == phoneNumber);

            if (excludeUserId.HasValue)
            {
                query = query.Where(u => u.Id != excludeUserId.Value);
            }

            return await query.AnyAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "检查电话号码是否存在时发生错误: {PhoneNumber}", phoneNumber);
            throw;
        }
    }

    /// <summary>
    /// 获取用户列表
    /// </summary>
    /// <param name="userType">用户类型</param>
    /// <param name="isActive">是否激活</param>
    /// <param name="searchTerm">搜索关键词</param>
    /// <param name="pageNumber">页码</param>
    /// <param name="pageSize">页大小</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>用户列表</returns>
    public async Task<(IEnumerable<User> Users, int TotalCount)> GetUsersAsync(
        UserType? userType = null,
        bool? isActive = null,
        string? searchTerm = null,
        int pageNumber = 1,
        int pageSize = 20,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("获取用户列表，用户类型: {UserType}, 是否激活: {IsActive}, 搜索词: {SearchTerm}, 页码: {PageNumber}, 页大小: {PageSize}",
                userType, isActive, searchTerm, pageNumber, pageSize);

            var query = _dbSet.AsQueryable();

            if (userType.HasValue)
            {
                query = query.Where(u => u.UserType == userType.Value);
            }

            if (isActive.HasValue)
            {
                query = query.Where(u => u.IsActive == isActive.Value);
            }

            if (!string.IsNullOrWhiteSpace(searchTerm))
            {
                var searchTermLower = searchTerm.ToLower();
                query = query.Where(u => 
                    u.Username.ToLower().Contains(searchTermLower) ||
                    u.Email.ToLower().Contains(searchTermLower) ||
                    (u.FirstName != null && u.FirstName.ToLower().Contains(searchTermLower)) ||
                    (u.LastName != null && u.LastName.ToLower().Contains(searchTermLower)));
            }

            var totalCount = await query.CountAsync(cancellationToken);

            var users = await query
                .OrderByDescending(u => u.CreatedAt)
                .Skip((pageNumber - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync(cancellationToken);

            return (users, totalCount);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取用户列表时发生错误");
            throw;
        }
    }

    /// <summary>
    /// 获取用户角色
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>用户角色列表</returns>
    public async Task<IEnumerable<Role>> GetUserRolesAsync(Guid userId, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("获取用户角色: {UserId}", userId);

            return await _context.UserRoles
                .Where(ur => ur.UserId == userId)
                .Include(ur => ur.Role)
                .Select(ur => ur.Role)
                .ToListAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取用户角色时发生错误: {UserId}", userId);
            throw;
        }
    }

    /// <summary>
    /// 获取用户权限
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>用户权限列表</returns>
    public async Task<IEnumerable<Permission>> GetUserPermissionsAsync(Guid userId, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("获取用户权限: {UserId}", userId);

            // 获取直接分配的权限
            var directPermissions = await _context.UserPermissions
                .Where(up => up.UserId == userId)
                .Include(up => up.Permission)
                .Select(up => up.Permission)
                .ToListAsync(cancellationToken);

            // 获取通过角色分配的权限
            var rolePermissions = await _context.UserRoles
                .Where(ur => ur.UserId == userId)
                .SelectMany(ur => ur.Role.RolePermissions)
                .Select(rp => rp.Permission)
                .ToListAsync(cancellationToken);

            // 合并并去重
            return directPermissions.Union(rolePermissions).Distinct();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取用户权限时发生错误: {UserId}", userId);
            throw;
        }
    }

    /// <summary>
    /// 获取最近登录的用户
    /// </summary>
    /// <param name="count">数量</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>最近登录的用户列表</returns>
    public async Task<IEnumerable<User>> GetRecentlyLoggedInUsersAsync(int count = 10, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("获取最近登录的用户，数量: {Count}", count);

            return await _dbSet
                .Where(u => u.LastLoginAt.HasValue)
                .OrderByDescending(u => u.LastLoginAt)
                .Take(count)
                .ToListAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取最近登录的用户时发生错误，数量: {Count}", count);
            throw;
        }
    }

    /// <summary>
    /// 获取新注册的用户
    /// </summary>
    /// <param name="days">天数</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>新注册的用户列表</returns>
    public async Task<IEnumerable<User>> GetNewlyRegisteredUsersAsync(int days = 7, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("获取新注册的用户，天数: {Days}", days);

            var cutoffDate = DateTime.UtcNow.AddDays(-days);

            return await _dbSet
                .Where(u => u.CreatedAt >= cutoffDate)
                .OrderByDescending(u => u.CreatedAt)
                .ToListAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取新注册的用户时发生错误，天数: {Days}", days);
            throw;
        }
    }
}
