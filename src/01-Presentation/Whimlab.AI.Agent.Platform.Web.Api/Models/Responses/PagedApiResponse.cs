using System.Text.Json.Serialization;

namespace Whimlab.AI.Agent.Platform.Web.Api.Models.Responses;

/// <summary>
/// 分页API响应格式
/// </summary>
/// <typeparam name="T">响应数据类型</typeparam>
public class PagedApiResponse<T> : ApiResponse<IEnumerable<T>>
{
    /// <summary>
    /// 分页元数据
    /// </summary>
    [JsonPropertyName("pagination")]
    public PaginationMetadata Pagination { get; set; } = new();

    /// <summary>
    /// 创建成功的分页响应
    /// </summary>
    /// <param name="data">响应数据</param>
    /// <param name="pagination">分页信息</param>
    /// <param name="message">成功消息</param>
    /// <returns>成功的分页响应</returns>
    public static new PagedApiResponse<T> Success(IEnumerable<T> data, PaginationMetadata pagination, string? message = null)
    {
        var response = new PagedApiResponse<T>
        {
            Data = data,
            Pagination = pagination,
            Message = message ?? "查询成功",
            Timestamp = DateTime.UtcNow
        };

        // 通过反射设置Success属性
        var successProperty = typeof(ApiResponse<IEnumerable<T>>).GetProperty("Success");
        successProperty?.SetValue(response, true);

        return response;
    }

    /// <summary>
    /// 创建失败的分页响应
    /// </summary>
    /// <param name="message">错误消息</param>
    /// <param name="errorCode">错误代码</param>
    /// <param name="errors">错误详情</param>
    /// <returns>失败的分页响应</returns>
    public static PagedApiResponse<T> Failure(string message, string? errorCode = null, List<string>? errors = null)
    {
        var response = new PagedApiResponse<T>
        {
            Message = message,
            ErrorCode = errorCode,
            Errors = errors ?? new List<string>(),
            Timestamp = DateTime.UtcNow
        };

        // 通过反射设置Success属性
        var successProperty = typeof(ApiResponse<IEnumerable<T>>).GetProperty("Success");
        successProperty?.SetValue(response, false);

        return response;
    }
}

/// <summary>
/// 分页元数据
/// </summary>
public class PaginationMetadata
{
    /// <summary>
    /// 当前页码
    /// </summary>
    [JsonPropertyName("currentPage")]
    public int CurrentPage { get; set; }

    /// <summary>
    /// 每页大小
    /// </summary>
    [JsonPropertyName("pageSize")]
    public int PageSize { get; set; }

    /// <summary>
    /// 总页数
    /// </summary>
    [JsonPropertyName("totalPages")]
    public int TotalPages { get; set; }

    /// <summary>
    /// 总记录数
    /// </summary>
    [JsonPropertyName("totalCount")]
    public long TotalCount { get; set; }

    /// <summary>
    /// 是否有下一页
    /// </summary>
    [JsonPropertyName("hasNext")]
    public bool HasNext { get; set; }

    /// <summary>
    /// 是否有上一页
    /// </summary>
    [JsonPropertyName("hasPrevious")]
    public bool HasPrevious { get; set; }

    /// <summary>
    /// 创建分页元数据
    /// </summary>
    /// <param name="currentPage">当前页码</param>
    /// <param name="pageSize">每页大小</param>
    /// <param name="totalCount">总记录数</param>
    /// <returns>分页元数据</returns>
    public static PaginationMetadata Create(int currentPage, int pageSize, long totalCount)
    {
        var totalPages = (int)Math.Ceiling((double)totalCount / pageSize);
        
        return new PaginationMetadata
        {
            CurrentPage = currentPage,
            PageSize = pageSize,
            TotalPages = totalPages,
            TotalCount = totalCount,
            HasNext = currentPage < totalPages,
            HasPrevious = currentPage > 1
        };
    }
}
