using Whimlab.AI.Agent.Platform.Domain.Core.Common;
using Whimlab.AI.Agent.Platform.Domain.Identity.Events;

namespace Whimlab.AI.Agent.Platform.Domain.Identity.Entities;

/// <summary>
/// 权限聚合根
/// </summary>
public class Permission : AggregateRoot
{
    /// <summary>
    /// 权限名称
    /// </summary>
    public string Name { get; private set; } = string.Empty;

    /// <summary>
    /// 权限显示名称
    /// </summary>
    public string DisplayName { get; private set; } = string.Empty;

    /// <summary>
    /// 权限描述
    /// </summary>
    public string? Description { get; private set; }

    /// <summary>
    /// 权限分组
    /// </summary>
    public string Group { get; private set; } = string.Empty;

    /// <summary>
    /// 是否为系统权限
    /// </summary>
    public bool IsSystemPermission { get; private set; }

    /// <summary>
    /// 是否激活
    /// </summary>
    public bool IsActive { get; private set; }

    /// <summary>
    /// 排序顺序
    /// </summary>
    public int SortOrder { get; private set; }

    /// <summary>
    /// 私有构造函数（用于EF Core）
    /// </summary>
    private Permission() { }

    /// <summary>
    /// 创建新权限
    /// </summary>
    /// <param name="name">权限名称</param>
    /// <param name="displayName">权限显示名称</param>
    /// <param name="group">权限分组</param>
    /// <param name="description">权限描述</param>
    /// <param name="isSystemPermission">是否为系统权限</param>
    /// <param name="sortOrder">排序顺序</param>
    /// <returns>权限实体</returns>
    public static Permission Create(
        string name, 
        string displayName, 
        string group, 
        string? description = null, 
        bool isSystemPermission = false,
        int sortOrder = 0)
    {
        var permission = new Permission
        {
            Name = name,
            DisplayName = displayName,
            Group = group,
            Description = description,
            IsSystemPermission = isSystemPermission,
            IsActive = true,
            SortOrder = sortOrder
        };

        permission.AddDomainEvent(new PermissionCreatedEvent(permission.Id, permission.Name));

        return permission;
    }

    /// <summary>
    /// 更新权限信息
    /// </summary>
    /// <param name="displayName">权限显示名称</param>
    /// <param name="description">权限描述</param>
    /// <param name="group">权限分组</param>
    /// <param name="sortOrder">排序顺序</param>
    /// <exception cref="InvalidOperationException">系统权限不能修改</exception>
    public void Update(string displayName, string? description = null, string? group = null, int? sortOrder = null)
    {
        if (IsSystemPermission)
            throw new InvalidOperationException("系统权限不能修改");

        DisplayName = displayName;
        Description = description;
        
        if (group != null)
            Group = group;
            
        if (sortOrder.HasValue)
            SortOrder = sortOrder.Value;
            
        MarkAsModified();

        AddDomainEvent(new PermissionUpdatedEvent(Id, Name));
    }

    /// <summary>
    /// 激活权限
    /// </summary>
    public void Activate()
    {
        if (IsActive) return;

        IsActive = true;
        MarkAsModified();
    }

    /// <summary>
    /// 停用权限
    /// </summary>
    /// <exception cref="InvalidOperationException">系统权限不能停用</exception>
    public void Deactivate()
    {
        if (IsSystemPermission)
            throw new InvalidOperationException("系统权限不能停用");

        if (!IsActive) return;

        IsActive = false;
        MarkAsModified();
    }

    /// <summary>
    /// 删除权限
    /// </summary>
    /// <exception cref="InvalidOperationException">系统权限不能删除</exception>
    public void Delete()
    {
        if (IsSystemPermission)
            throw new InvalidOperationException("系统权限不能删除");

        AddDomainEvent(new PermissionDeletedEvent(Id, Name));
    }
}
