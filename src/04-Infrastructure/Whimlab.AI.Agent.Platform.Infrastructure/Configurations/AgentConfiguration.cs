using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Whimlab.AI.Agent.Platform.Shared.Common.Enums;
using System.Text.Json;

namespace Whimlab.AI.Agent.Platform.Infrastructure.Configurations;

/// <summary>
/// 智能体实体配置
/// </summary>
public class AgentConfiguration : IEntityTypeConfiguration<Domain.Agent.Entities.Agent>
{
    /// <summary>
    /// 配置智能体实体
    /// </summary>
    /// <param name="builder">实体类型构建器</param>
    public void Configure(EntityTypeBuilder<Domain.Agent.Entities.Agent> builder)
    {
        // 表名
        builder.ToTable("Agents", "Agent");

        // 主键
        builder.HasKey(a => a.Id);

        // 属性配置
        builder.Property(a => a.Id)
            .IsRequired()
            .HasComment("智能体ID");

        builder.Property(a => a.Name)
            .IsRequired()
            .HasMaxLength(100)
            .HasComment("智能体名称");

        builder.Property(a => a.Description)
            .IsRequired()
            .HasMaxLength(1000)
            .HasComment("智能体描述");

        builder.Property(a => a.IconUrl)
            .HasMaxLength(500)
            .HasComment("图标URL");

        builder.Property(a => a.AgentType)
            .IsRequired()
            .HasConversion<string>()
            .HasMaxLength(50)
            .HasComment("智能体类型");

        builder.Property(a => a.Status)
            .IsRequired()
            .HasConversion<string>()
            .HasMaxLength(50)
            .HasComment("智能体状态");

        builder.Property(a => a.CreatedBy)
            .IsRequired()
            .HasComment("创建者ID");

        builder.Property(a => a.CurrentVersion)
            .IsRequired()
            .HasMaxLength(20)
            .HasDefaultValue("1.0.0")
            .HasComment("当前版本号");

        // 配置复杂对象为JSON
        builder.Property(a => a.Configuration)
            .IsRequired()
            .HasConversion(
                v => JsonSerializer.Serialize(v, (JsonSerializerOptions?)null),
                v => JsonSerializer.Deserialize<Domain.Agent.ValueObjects.AgentConfiguration>(v, (JsonSerializerOptions?)null)!)
            .HasColumnType("nvarchar(max)")
            .HasComment("智能体配置JSON");

        builder.Property(a => a.IsPublic)
            .IsRequired()
            .HasDefaultValue(false)
            .HasComment("是否公开");

        builder.Property(a => a.Category)
            .HasMaxLength(100)
            .HasComment("分类");

        builder.Property(a => a.UsageCount)
            .IsRequired()
            .HasDefaultValue(0L)
            .HasComment("使用次数");

        builder.Property(a => a.Rating)
            .IsRequired()
            .HasDefaultValue(0.0)
            .HasPrecision(3, 2)
            .HasComment("评分");

        builder.Property(a => a.RatingCount)
            .IsRequired()
            .HasDefaultValue(0)
            .HasComment("评分次数");

        builder.Property(a => a.LastUsedAt)
            .HasComment("最后使用时间");

        builder.Property(a => a.PublishedAt)
            .HasComment("发布时间");

        builder.Property(a => a.ArchivedAt)
            .HasComment("归档时间");

        builder.Property(a => a.CreatedAt)
            .IsRequired()
            .HasDefaultValueSql("GETUTCDATE()")
            .HasComment("创建时间");

        builder.Property(a => a.UpdatedAt)
            .HasComment("更新时间");

        // 索引
        builder.HasIndex(a => a.Name)
            .HasDatabaseName("IX_Agents_Name");

        builder.HasIndex(a => a.CreatedBy)
            .HasDatabaseName("IX_Agents_CreatedBy");

        builder.HasIndex(a => a.Status)
            .HasDatabaseName("IX_Agents_Status");

        builder.HasIndex(a => a.AgentType)
            .HasDatabaseName("IX_Agents_AgentType");

        builder.HasIndex(a => a.Category)
            .HasDatabaseName("IX_Agents_Category");

        builder.HasIndex(a => a.IsPublic)
            .HasDatabaseName("IX_Agents_IsPublic");

        builder.HasIndex(a => a.UsageCount)
            .HasDatabaseName("IX_Agents_UsageCount");

        builder.HasIndex(a => a.Rating)
            .HasDatabaseName("IX_Agents_Rating");

        builder.HasIndex(a => a.PublishedAt)
            .HasDatabaseName("IX_Agents_PublishedAt");

        builder.HasIndex(a => a.CreatedAt)
            .HasDatabaseName("IX_Agents_CreatedAt");

        // 复合索引
        builder.HasIndex(a => new { a.Status, a.IsPublic })
            .HasDatabaseName("IX_Agents_Status_IsPublic");

        builder.HasIndex(a => new { a.CreatedBy, a.Status })
            .HasDatabaseName("IX_Agents_CreatedBy_Status");
    }
}
