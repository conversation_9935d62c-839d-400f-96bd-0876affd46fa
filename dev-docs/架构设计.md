
# **系统指令：企业级AI智能体平台代码生成**

## **I. 项目总体目标与架构蓝图**

你的核心任务是生成一个完整的、企业级的AI智能体平台的C# .NET 9.0源代码。该平台将允许管理员创建和管理多种AI智能体，并供客户通过Web界面订阅和使用这些智能体。

**核心架构原则：**

1.  **模块化单体 (Modular Monolith):** 首选架构。设计目标是模块内高内聚、模块间低耦合。每个模块应为一个垂直切片。
2.  **领域驱动设计 (DDD):** 严格遵守DDD原则。清晰定义限界上下文（Bounded Contexts）、聚合（Aggregates）、实体（Entities）、值对象（Value Objects）、领域服务（Domain Services）和领域事件（Domain Events）。
3.  **命令查询职责分离 (CQRS):** 使用MediatR实现。命令用于修改状态，不返回数据（或仅返回ID/状态）；查询用于检索数据，不修改状态。
4.  **事件驱动架构 (EDA):**
    *   **领域事件:** 在同一事务边界内处理，用于聚合内部或紧密相关的聚合间的状态一致性。
    *   **集成事件:** 异步处理，用于模块间的通信，确保最终一致性。默认使用内存事件总线；对于需要后台处理和模块解耦的场景，明确指出时，需准备并可配置使用RabbitMQ。
5.  **分层架构 (应用于每个模块及整体):**
    *   **领域层:** 包含核心业务逻辑、聚合、实体、值对象、领域事件以及仓储接口。必须与持久化无关，除了可能依赖一个核心的共享领域内核外，不依赖其他层。
    *   **应用层:** 通过应用服务编排领域逻辑。处理命令和查询（CQRS）。包含请求/响应的DTO、特定于应用的验证逻辑，并定义其所需的基础设施服务接口。
    *   **基础设施层:** 实现应用层和领域层定义的接口。处理持久化（EF Core, Dapper）、外部服务集成（AI API、支付网关、消息队列、缓存）、日志记录等。
    *   **表现层 (API & UI):**
        *   **API (ASP.NET Core Web API):** 通过RESTful端点暴露功能。处理认证、授权、输入验证（FluentValidation）和DTO映射。
        *   **Web UI (Blazor Auto模式 & MudBlazor):** 两个独立的Blazor应用：一个供客户（Customer）使用，一个供管理员（Admin）使用。

**自我修正与完整性要求：** 对于下文描述的每一个业务模块或核心功能，你*必须*生成其在所有相关层（领域层、应用层、基础设施层、API层，以及适用的UI组件）的完整实现。在进入下一个模块/功能之前，请内部审查当前模块/功能的所有方面（实体、服务、命令、查询、仓储、控制器、UI、测试）是否已根据这些原则和详细需求完全实现。确保没有任何遗漏或简化，以免影响企业级质量。

## **II. 核心技术栈**

**后端 (.NET 9.0):**

*   **框架:** .NET 9.0, ASP.NET Core 9.0, Entity Framework Core 9.0
*   **架构模式:** DDD, CQRS (MediatR), 模块化单体, 事件驱动
*   **设计模式:** 仓储模式 (Repository), 工作单元模式 (Unit of Work), 规约模式 (Specification), 选项模式 (Options)
*   **数据存储 (主要):** PostgreSQL 16 (用于关系型/事务型数据)(开发环境:host:localhost,port:54321,user:luca, password:secret,database:whimlab-dev1)
*   **数据存储 (次要/专用):**
    *   MongoDB 7.0 (用于文档存储，例如：对话历史、复杂的智能体配置) (开发环境:host:localhost,port: 27017,user: admin, password: Passw0rd,database:whimlab-dev1)
    *   Redis 7.2 (用于分布式缓存、会话管理、速率限制、短期数据、实现分布式锁、消息队列的某些辅助功能)(开发环境:host:localhost,port: 26379,db:whimlab-dev1)
    *   Elasticsearch 8.11 (用于高级搜索、日志聚合、分析事件存储)
    *   MinIO (用于S3兼容的对象存储：智能体文件、附件、备份)
*   **消息队列:**
    *   内存事件总线 (MediatR notifications，用于领域事件、单体内部简单的集成事件)
    *   RabbitMQ 3.12 (用于复杂/重量级的后台任务、保证交付的集成事件，作为可选但需为此准备)(开发环境:host:localhost,port: 25672,user: guest, password: guest)
*   **AI集成SDK:**
    *   **Microsoft Semantic Kernel:** 用于构建“通用AI智能体”。(使用 `Microsoft.SemanticKernel` 相关包,开源地址:https://github.com/microsoft/semantic-kernel 、 https://learn.microsoft.com/en-us/semantic-kernel/overview/)。
    *   **Dify API客户端:** 用于与Dify平台交互。你需要为此实现一个HTTP客户端封装(dify文档和开源项目中获取需要信息: https://docs.dify.ai/en/introduction 、 https://github.com/langgenius/dify . API文档: https://docs.dify.ai/api_access/chatflow.zh 、 https://docs.dify.ai/api_access/agent.zh 、 https://docs.dify.ai/api_access/chat.zh 、 https://docs.dify.ai/api_access/completion.zh 、 https://docs.dify.ai/api_access/workflow.zh)。
    *   OpenAI SDK (如果Semantic Kernel对特定需求的抽象不足，或需要直接调用时使用)。
    *   LangChain.NET (如果Semantic Kernel在某些高级智能体场景下能力不足，则考虑使用)。
*   **认证与授权:**
    *   ASP.NET Core Identity (为Customer和Admin用户存储进行定制化分离)。
    *   JWT Bearer认证。
    *   基于策略的授权 (Policy-Based Authorization)。
*   **日志与监控:**
    *   Serilog (结构化日志)。
    *   OpenTelemetry (用于分布式追踪和指标)。
    *   ASP.NET Core 健康检查 (Health Checks)。
    *   Application Insights (用于云端监控，作为可选项但需为此设计)。
*   **API:** RESTful, 使用OpenAPI进行文档化。
*   **验证:** FluentValidation。
*   **对象映射:** AutoMapper。

**前端 (Blazor Auto 渲染模式 & MudBlazor,不使用js,使用blazor特性和机制):**

*   **框架:** Blazor (.NET 9.0) 采用 **Auto** 渲染模式 (Server 和 WebAssembly 混合)。
*   **UI库:** MudBlazor 6.11+。
*   **状态管理:** Fluxor (类Redux模式)。
*   **实时通信:** SignalR。
*   **本地存储:** Blazored.LocalStorage, Blazored.SessionStorage。对于较大数据量可考虑IndexedDB。
*   **PWA支持:** Service Worker, Web App Manifest, Push Notifications (为未来集成进行设计)。

**开发与DevOps:**

*   **IDE:** Visual Studio 2022 / VS Code。
*   **版本控制:** Git / GitHub。
*   **容器化:** Docker / Docker Compose。
*   **CI/CD:** GitHub Actions (配置基础的构建/测试工作流)。

## **III. 解决方案与项目结构**

严格按照以下企业级项目结构生成解决方案和项目：

```
Whimlab.AI/
├── src/
│   ├── 01-Presentation/                    # 表现层
│   │   ├── Whimlab.AI.Agent.Platform.Web.Api/     # 主API服务
│   │   │   ├── Controllers/               # API控制器 (按版本和业务模块组织, e.g., V1/IdentityController.cs, V1/AgentsController.cs)
│   │   │   ├── Middleware/               # 中间件 (e.g., GlobalExceptionMiddleware.cs, InputValidationMiddleware.cs)
│   │   │   ├── Filters/                  # 过滤器 (e.g., CustomAuthorizationFilter.cs)
│   │   │   ├── Validators/               # API层输入DTO的验证器 (FluentValidation)
│   │   │   ├── Mappers/                  # API层DTO与应用层DTO的映射配置 (AutoMapper Profiles)
│   │   │   └── Configuration/            # API相关配置 (e.g., SwaggerConfig.cs, CorsConfig.cs)
│   │   │
│   │   ├── Whimlab.AI.Agent.Platform.Web.GraphQL/ # GraphQL API (若将来需要)
│   │   │
│   │   ├── Whimlab.AI.Agent.Platform.Web.SignalR/ # 实时通信
│   │   │   ├── Hubs/                     # SignalR Hubs (e.g., ConversationHub.cs, NotificationHub.cs)
│   │   │   └── Services/                 # Hub相关的服务或上下文管理
│   │   │
│   │   ├── Whimlab.AI.Agent.Platform.Client.Customer/ # 客户端前端 (dotnet new mudblazor -n Whimlab.AI.Agent.Platform.Client.Customer -o src/01-Presentation/Whimlab.AI.Agent.Platform.Client.Customer --interactivity Auto)
│   │   │   ├── Pages/                    # 页面组件 (e.g., Index.razor, LoginPage.razor, ChatPage.razor)
│   │   │   ├── Components/               # 可复用组件 (e.g., Common/LoadingSpinner.razor, Agent/AgentCard.razor, Chat/MessageBubble.razor)
│   │   │   ├── Services/                 # 前端服务 (e.g., AuthenticationService.cs, AgentServiceHttpClient.cs)
│   │   │   ├── Store/                    # 状态管理 (Fluxor: States, Actions, Reducers, Effects for features like Auth, Conversation)
│   │   │   ├── Layout/                   # 布局组件 (e.g., MainLayout.razor, NavMenu.razor)
│   │   │   ├── wwwroot/                  # 静态资源
│   │   │   └── Whimlab.AI.Agent.Platform.Client.Customer.Client/ # WebAssembly特定部分
│   │   │       └── (Components or services specifically for WASM render mode)
│   │   │
│   │   └── Whimlab.AI.Agent.Platform.Client.Admin/    # 管理端前端 (dotnet new mudblazor -n Whimlab.AI.Agent.Platform.Client.Admin -o src/01-Presentation/Whimlab.AI.Agent.Platform.Client.Admin --interactivity Auto)
│   │       ├── Pages/                    # 管理页面 (e.g., DashboardPage.razor, UserManagementPage.razor, AgentListPage.razor)
│   │       ├── Components/               # 管理组件
│   │       ├── Services/                 # 管理服务
│   │       ├── Store/                    # 管理状态
│   │       ├── Layout/
│   │       ├── wwwroot/
│   │       └── Whimlab.AI.Agent.Platform.Client.Admin.Client/ # WebAssembly特定部分
│   │
│   ├── 02-Application/                     # 应用层
│   │   ├── Whimlab.AI.Agent.Platform.Application.Core/ # 核心应用服务
│   │   │   ├── Common/                   # 通用应用服务、接口、模型、异常
│   │   │   │   ├── Behaviors/            # MediatR管道行为 (ValidationBehavior, LoggingBehavior, PerformanceBehavior, TransactionBehavior, AuthorizationBehavior, CachingBehavior)
│   │   │   │   ├── Interfaces/           # 应用层接口 (e.g., IEmailService, IFileStorageService)
│   │   │   │   ├── Models/               # 应用层模型/DTOs (用于命令/查询的参数和返回结果)
│   │   │   │   └── Exceptions/           # 应用层特定异常 (e.g., ApplicationLogicException)
│   │   │   ├── Commands/                 # 命令定义 (按模块组织, e.g., Identity/RegisterCustomerCommand.cs)
│   │   │   │   └── Handlers/             # 命令处理器 (按模块组织, e.g., Identity/RegisterCustomerCommandHandler.cs)
│   │   │   ├── Queries/                  # 查询定义 (按模块组织)
│   │   │   │   └── Handlers/             # 查询处理器 (按模块组织)
│   │   │   └── EventHandlers/            # 应用层事件处理器 (处理领域事件或集成事件以触发应用层逻辑)
│   │   │
│   │   ├── Whimlab.AI.Agent.Platform.Application.Identity/ # 身份认证应用 (包含该模块的Commands, Queries, Services, EventHandlers, DTOs)
│   │   ├── Whimlab.AI.Agent.Platform.Application.Agent/    # 智能体应用 (...)
│   │   ├── Whimlab.AI.Agent.Platform.Application.Conversation/ # 对话应用 (...)
│   │   ├── Whimlab.AI.Agent.Platform.Application.Subscription/ # 订阅应用 (...)
│   │   ├── Whimlab.AI.Agent.Platform.Application.Payment/    # 支付应用 (...)
│   │   └── Whimlab.AI.Agent.Platform.Application.Analytics/  # 分析应用 (...)
│   │
│   ├── 03-Domain/                          # 领域层
│   │   ├── Whimlab.AI.Agent.Platform.Domain.Core/ # 核心领域
│   │   │   ├── Common/                   # 通用领域基类 (e.g., Entity.cs, ValueObject.cs, AggregateRoot.cs, DomainEvent.cs)
│   │   │   ├── Interfaces/               # 核心领域接口 (e.g., IRepository.cs, IDomainEventDispatcher.cs, IUnitOfWork.cs)
│   │   │   ├── Specifications/           # 规约模式实现基类
│   │   │   └── Exceptions/               # 领域异常基类 (e.g., DomainException.cs, BusinessRuleValidationException.cs)
│   │   │
│   │   ├── Whimlab.AI.Agent.Platform.Domain.Identity/ # 身份认证领域 (包含该模块的Aggregates, Entities, ValueObjects, DomainEvents, RepositoryInterfaces, DomainServices)
│   │   ├── Whimlab.AI.Agent.Platform.Domain.Agent/    # 智能体领域 (...)
│   │   ├── Whimlab.AI.Agent.Platform.Domain.Conversation/ # 对话领域 (...)
│   │   ├── Whimlab.AI.Agent.Platform.Domain.Subscription/ # 订阅领域 (...)
│   │   ├── Whimlab.AI.Agent.Platform.Domain.Payment/    # 支付领域 (...)
│   │   └── Whimlab.AI.Agent.Platform.Domain.Analytics/  # 分析领域 (...)
│   │
│   ├── 04-Infrastructure/                  # 基础设施层
│   │   ├── Whimlab.AI.Agent.Platform.Infrastructure.Data/ # 数据访问
│   │   │   ├── Context/                  # 数据库上下文 (e.g., AgentPlatformDbContext.cs, AgentReadDbContext.cs (IAgentReadDbContext), EventStoreDbContext.cs (若使用))
│   │   │   ├── Configurations/           # EF Core实体配置 (按模块组织, e.g., Identity/CustomerUserConfiguration.cs)
│   │   │   ├── Repositories/             # 仓储实现 (按模块组织, e.g., Identity/CustomerUserRepository.cs, Agent/AgentRepository.cs, Agent/AgentReadRepository.cs)
│   │   │   ├── Migrations/               # EF Core数据库迁移
│   │   │   ├── Seeders/                  # 数据播种 (e.g., DefaultRolesSeeder.cs)
│   │   │   └── Interceptors/             # EF Core数据拦截器 (e.g., EncryptionInterceptor.cs, AuditInterceptor.cs, DispatchDomainEventsInterceptor.cs)
│   │   │
│   │   ├── Whimlab.AI.Agent.Platform.Infrastructure.AI/ # AI服务实现
│   │   │   ├── SemanticKernel/           # Semantic Kernel集成 (e.g., EnterpriseSemanticKernelService.cs, IntelligentModelRouter.cs, IModelCatalog.cs)
│   │   │   ├── Dify/                     # DIFY集成 (e.g., EnterpriseDifyApiService.cs, IConversationStateManager_Dify.cs)
│   │   │   ├── Common/                   # AI通用组件 (e.g., TokenCounter.cs)
│   │   │   └── Monitoring/               # AI调用监控 (e.g., IModelPerformanceMonitor.cs, ITokenUsageTracker.cs)
│   │   │
│   │   ├── Whimlab.AI.Agent.Platform.Infrastructure.Caching/ # 缓存服务实现
│   │   │   ├── Redis/                    # Redis具体实现 (e.g., RedisDistributedLock.cs)
│   │   │   ├── Services/                 # 缓存服务 (e.g., EnterpriseDistributedCacheService.cs)
│   │   │   └── Configuration/            # 缓存配置
│   │   │
│   │   ├── Whimlab.AI.Agent.Platform.Infrastructure.Messaging/ # 消息服务实现
│   │   │   ├── RabbitMQ/                 # RabbitMQ实现 (e.g., RabbitMQConnectionManager.cs, EnterpriseMessageQueueManager.cs - RabbitMQ部分)
│   │   │   ├── Outbox/                   # Outbox模式实现 (e.g., OutboxMessage.cs, OutboxMessageProcessorService.cs)
│   │   │   ├── Deduplication/            # 消息去重 (e.g., MessageDeduplicationService.cs, IBloomFilter.cs)
│   │   │   └── EventBus/                 # 内存事件总线 (若有特定实现，通常MediatR的通知机制已足够)
│   │   │
│   │   ├── Whimlab.AI.Agent.Platform.Infrastructure.Payment/ # 支付服务实现
│   │   │   ├── Gateways/                 # 支付网关实现 (e.g., AlipayGateway.cs, WeChatPayGateway.cs)
│   │   │   ├── Services/                 # 支付服务 (e.g., PaymentService.cs, PaymentGatewayFactory.cs)
│   │   │   └── Webhooks/                 # (Webhook处理逻辑在API层控制器，但配置或辅助类可在此)
│   │   │
│   │   ├── Whimlab.AI.Agent.Platform.Infrastructure.Storage/ # 文件存储服务实现
│   │   │   ├── MinIO/                    # MinIO对象存储实现 (e.g., MinIOFileStorageService.cs)
│   │   │   └── Interfaces/               # (e.g., IFileStorageService.cs)
│   │   │
│   │   ├── Whimlab.AI.Agent.Platform.Infrastructure.Monitoring/ # 监控服务实现
│   │   │   ├── Logging/                  # 日志服务配置 (Serilog sinks for Console, File, Elasticsearch)
│   │   │   ├── Metrics/                  # 指标收集配置 (OpenTelemetry exporters for Prometheus, Application Insights)
│   │   │   ├── Tracing/                  # 链路追踪配置 (OpenTelemetry exporters for Jaeger, Application Insights)
│   │   │   └── HealthChecks/             # 健康检查实现 (e.g., PostgresHealthCheck.cs, RedisHealthCheck.cs)
│   │   │
│   │   ├── Whimlab.AI.Agent.Platform.Infrastructure.Identity/ # 身份认证基础设施实现 (Identity Stores, TokenService, EnterpriseAuthenticationService, EnterpriseAuthorizationService)
│   │   │
│   │   └── Whimlab.AI.Agent.Platform.Infrastructure.Notifications/ # 通知服务实现
│   │       ├── Email/                    # 邮件服务 (e.g., SmtpEmailService.cs or SendGridEmailService.cs)
│   │       └── InApp/                    # 应用内通知 (若使用SignalR, Hub在Presentation层, 逻辑可在此)
│   │
│   ├── 05-Shared/                         # 共享库
│   │   ├── Whimlab.AI.Agent.Platform.Shared.Common/ # 通用共享 (Constants, Enums, Extensions, Helpers, Utilities)
│   │   │
│   │   ├── Whimlab.AI.Agent.Platform.Shared.Contracts/ # 契约定义 (DTOs, Events, Commands, Queries - 按模块组织)
│   │   │
│   │   ├── Whimlab.AI.Agent.Platform.Shared.Exceptions/ # 自定义异常定义 (按类型组织: Business, Validation, Integration, Infrastructure)
│   │   │
│   │   └── Whimlab.AI.Agent.Platform.Shared.Configuration/ # 配置模型 (IOptions模型类)
│   │
│   └── 06-CrossCutting/                   # 横切关注点 (可选目录，通常其实现分散在其他层，如Middleware, Behaviors, Interceptors)
│
├── tests/                                 # 测试项目
│   ├── 01-UnitTests/                     # 单元测试 (按被测项目和模块组织, e.g., Whimlab.AI.Agent.Platform.Domain.Tests/Agent/AgentAggregateTests.cs)
│   ├── 02-IntegrationTests/              # 集成测试 (e.g., Whimlab.AI.Agent.Platform.API.IntegrationTests/AgentsControllerTests.cs, Whimlab.AI.Agent.Platform.Infrastructure.Tests/AgentRepositoryTests.cs)
│   ├── 03-E2ETests/                      # 端到端测试
│   ├── 04-PerformanceTests/              # 性能测试
│   └── 05-TestUtilities/                 # 测试工具 (Builders, Fixtures, Mocks, Helpers)
│
├── tools/                                # 工具和脚本
│   ├── build/                            # 构建脚本 (e.g., build.ps1, build.sh)
│   ├── deployment/                       # 部署脚本和配置
│   │   ├── docker/                       # Docker相关配置 (除了项目内的Dockerfile)
│   │   ├── kubernetes/                   # Kubernetes清单示例
│   │   └── scripts/                      # 其他部署脚本
│   ├── database/                         # 数据库工具 (迁移脚本由EF Core生成, 可在此放额外脚本)
│   └── monitoring/                       # 监控配置 (e.g., Prometheus config, Grafana dashboard JSONs)
│
├── docs/                                 # 文档 (架构文档, API文档, 用户指南等)
│
├── .github/                              # GitHub配置
│   ├── workflows/                        # GitHub Actions CI/CD (e.g., main.yml)
│   └── (Issue templates, PR templates)
│
├── .gitignore
├── .dockerignore
├── docker-compose.yml                    # 用于本地开发环境
├── docker-compose.override.yml
├── Directory.Build.props                 # 中央MSBuild属性
├── Directory.Packages.props              # 中央NuGet包版本管理
├── global.json                           # .NET SDK版本固定
├── nuget.config                          # NuGet源配置
├── README.md
└── Whimlab.AI.sln      # 解决方案文件
```

**Blazor Auto模式项目结构关键注意事项 (重申):**
`Whimlab.AI.Agent.Platform.Client.Customer` 和 `Whimlab.AI.Agent.Platform.Client.Admin` 项目将使用 `dotnet new mudblazor -n <ProjectName> -o <OutputPath> --interactivity Auto` 命令创建。这将创建一个主项目 (`<ProjectName>.csproj`) 和一个客户端项目 (`<ProjectName>.Client.csproj`)。确保组件根据其交互性需求及其依赖项所在的位置，被正确放置。

## **IV. 核心业务模块与功能实现 (详细指令)**

---

### **模块1: 身份与访问管理 (IAM) - 详细实现指令**

**用户类型:**

*   **CustomerUser (客户用户):** 通过 `Whimlab.AI.Agent.Platform.Client.Customer` 注册。存储于 `CustomerUsers` 表。
*   **AdminUser (管理员用户):** 由超级管理员通过 `Whimlab.AI.Agent.Platform.Client.Admin` 管理。存储于 `AdminUsers` 表。
*   两种用户类型完全分离，使用独立的ASP.NET Core Identity配置和独立的数据库表。

**领域层 (`Whimlab.AI.Agent.Platform.Domain.Identity`):**

*   **聚合 `CustomerUser` 和 `AdminUser`:**
    *   属性: `Id`, `UserName`, `NormalizedUserName`, `Email`, `NormalizedEmail`, `EmailConfirmed`, `PasswordHash`, `SecurityStamp`, `ConcurrencyStamp`, `PhoneNumber`, `PhoneNumberConfirmed`, `TwoFactorEnabled`, `LockoutEnd`, `LockoutEnabled`, `AccessFailedCount`, `FirstName`, `LastName`, `IsActive`, `CreatedAt`, `LastLoginAt`, `ProfilePictureUrl` (可选)。
    *   `CustomerUser` 特有属性: `TrialExpiryDate`, `CurrentSubscriptionId` (外键)。
    *   `AdminUser` 特有属性: (暂无特别)。
    *   方法: `Activate()`, `Deactivate()`, `UpdateLastLogin()`, `ChangePassword(newPasswordHash)`, `EnableMfa()`, `ConfirmEmail()`。
*   **实体 `Role`:** `Id`, `Name`, `NormalizedName`, `Description`, `ICollection<RolePermission> RolePermissions`。
*   **实体 `Permission`:** `Id`, `Name` (e.g., "Agents.Create", "Users.ViewCustomers"), `Description`, `GroupName` (用于UI分组)。
*   **关联实体 `UserRole`:** `UserId`, `RoleId`。
*   **关联实体 `RolePermission`:** `RoleId`, `PermissionId`。
*   **值对象:** `EmailAddress` (含验证), `HashedPassword` (封装密码哈希逻辑), `LoginAttempt` (用于记录登录尝试)。
*   **领域事件:** `CustomerRegisteredEvent(CustomerId, Email)`, `AdminUserCreatedEvent(AdminId, Email)`, `UserLoggedInEvent(UserId, UserType, IPAddress, Timestamp)`, `RoleAssignedToUserEvent(UserId, RoleId)`, `PermissionGrantedToRoleEvent(RoleId, PermissionId)`.
*   **仓储接口:** `ICustomerUserRepository : IRepository<CustomerUser, UserIdType>`, `IAdminUserRepository : IRepository<AdminUser, AdminIdType>`, `IRoleRepository : IRepository<Role, RoleIdType>`, `IPermissionRepository : IRepository<Permission, PermissionIdType>` (其中IdType是对应主键类型)。

**应用层 (`Whimlab.AI.Agent.Platform.Application.Identity`):**

*   **命令:**
    *   `RegisterCustomerCommand(FirstName, LastName, Email, Password)` -> `CustomerUserDto`
    *   `LoginCustomerCommand(Email, Password, IPAddress, UserAgent)` -> `AuthTokenDto`
    *   `CreateAdminUserCommand(FirstName, LastName, Email, Password, List<RoleIdType> RoleIds)` -> `AdminUserDto`
    *   `LoginAdminCommand(Email, Password, IPAddress, UserAgent)` -> `AuthTokenDto`
    *   `AssignRoleToUserCommand(UserIdType UserId, RoleIdType RoleId, string UserType)`
    *   `RevokeRoleFromUserCommand(UserIdType UserId, RoleIdType RoleId, string UserType)`
    *   `GrantPermissionToRoleCommand(RoleIdType RoleId, PermissionIdType PermissionId)`
    *   `RevokePermissionFromRoleCommand(RoleIdType RoleId, PermissionIdType PermissionId)`
    *   `RefreshTokenCommand(string ExpiredToken, string RefreshToken)` -> `AuthTokenDto`
    *   `ChangePasswordCommand(UserIdType UserId, string UserType, string OldPassword, string NewPassword)`
    *   `ForgotPasswordCommand(Email, string UserType)`
    *   `ResetPasswordCommand(Email, string UserType, string Token, string NewPassword)`
    *   `UpdateUserStatusCommand(UserIdType UserId, string UserType, bool IsActive)`
*   **查询:**
    *   `GetCustomerUserByIdQuery(UserIdType Id)` -> `CustomerUserDto`
    *   `GetAdminUserByIdQuery(AdminIdType Id)` -> `AdminUserDto`
    *   `ListAdminUsersQuery(PageNumber, PageSize, SearchTerm, SortBy, SortDirection)` -> `PagedResult<AdminUserDto>`
    *   `ListCustomerUsersQuery(PageNumber, PageSize, SearchTerm, SortBy, SortDirection)` -> `PagedResult<CustomerUserDto>`
    *   `GetRolesQuery()` -> `List<RoleDto>`
    *   `GetPermissionsQuery()` -> `List<PermissionDto>`
    *   `GetUserPermissionsQuery(UserIdType UserId, string UserType)` -> `List<string>` (返回权限名称列表)
*   **DTOs:** `CustomerUserDto`, `AdminUserDto`, `RoleDto` (含关联的PermissionDtos), `PermissionDto`, `AuthTokenDto` (AccessToken, RefreshToken, ExpiresIn, UserInfo {Id, Email, Name, Roles, Permissions}).
*   **命令处理器和查询处理器实现细节:**
    *   **`RegisterCustomerCommandHandler`:**
        1.  验证输入 (FluentValidation)。
        2.  检查Email是否已存在 (`ICustomerUserRepository`)。
        3.  创建`CustomerUser`聚合实例 (密码哈希)。
        4.  分配默认客户角色 (如果存在)。
        5.  设置试用期 (`TrialManagementService` - 实际逻辑可能在订阅模块，此处触发事件或直接调用)。
        6.  保存用户 (`ICustomerUserRepository`, UnitOfWork)。
        7.  发布`CustomerRegisteredEvent`。
        8.  记录审计日志 (`IAuditLogService`)。
        9.  返回`CustomerUserDto`。
    *   **`LoginCustomerCommandHandler` / `LoginAdminCommandHandler`:**
        1.  验证输入。
        2.  调用 `EnterpriseAuthenticationService.AuthenticateAsync` (传入用户名、密码、IP、UserAgent等)。
        3.  若成功，服务返回包含JWT的 `AuthenticationResult`。
        4.  （`EnterpriseAuthenticationService`内部会更新用户`LastLoginAt`, 记录登录尝试，检查锁定状态，处理MFA逻辑等）。
        5.  发布`UserLoggedInEvent`。
        6.  记录审计日志。
        7.  返回`AuthTokenDto`。
    *   **`RefreshTokenCommandHandler`:**
        1.  验证输入。
        2.  调用 `EnterpriseAuthenticationService.RefreshTokenAsync` (传入过期的AccessToken和RefreshToken)。
        3.  服务验证RefreshToken有效性、未吊销、与AccessToken匹配，并颁发新的`AuthTokenDto`。
        4.  记录审计日志。
    *   **其他命令/查询处理器:** 遵循类似的详细流程：验证、授权检查 (使用 `IAuthorizationService` 和自定义Requirement/Handler)、加载所需实体、执行业务逻辑 (通过聚合或领域服务)、持久化 (UnitOfWork)、发布事件、缓存操作、审计。

**基础设施层 (`Whimlab.AI.Agent.Platform.Infrastructure.Identity` & `Whimlab.AI.Agent.Platform.Infrastructure.Data`):**

*   **ASP.NET Core Identity Stores:**
    *   为`CustomerUser`实现`IUserStore<CustomerUser>`, `IUserPasswordStore<CustomerUser>`, `IUserEmailStore<CustomerUser>`, `IUserRoleStore<CustomerUser>`等。
    *   为`AdminUser`实现类似的Store。
    *   这些Store将使用EF Core与PostgreSQL交互。
*   **仓储实现:**
    *   `CustomerUserRepository`, `AdminUserRepository`, `RoleRepository`, `PermissionRepository`。它们将封装EF Core的查询逻辑，并可能使用规约模式。
*   **`EnterpriseAuthenticationService` 实现:**
    *   构造函数注入 `IIdentityProviderRegistry` (初期可简化为直接用户密码验证), `ICustomerUserRepository`, `IAdminUserRepository`, `ITokenService`, `IDistributedCache` (用于速率限制和MFA状态), `ILogger`, `IOptions<AuthenticationConfiguration>`, `IAuditLogService`.
    *   `AuthenticateAsync`:
        1.  根据请求中的UserType选择对应的UserRepository。
        2.  速率限制检查 (Redis计数器: `auth_attempts:<IP>:<email>` 或 `auth_attempts:<IP>`).
        3.  加载用户。验证用户是否存在及密码是否匹配 (使用 `PasswordHasher`)。
        4.  检查账户状态 (IsActive, LockoutEnd)。
        5.  (未来) 处理MFA逻辑: 若需要MFA且未提供MFA Token, 返回MFA质询；若提供了MFA Token, 验证它。
        6.  若认证成功，重置`AccessFailedCount`，更新`LastLoginAt`。
        7.  调用`ITokenService.GenerateTokenAsync`生成JWT和Refresh Token。Refresh Token需持久化并设置过期时间。
        8.  记录审计日志。
        9.  返回`AuthenticationResult`。
    *   `RefreshTokenAsync`:
        1.  验证Refresh Token格式和签名 (如果它也是JWT)。
        2.  从持久化存储中查找Refresh Token，确保它存在、未过期、未被吊销，并且与请求中的用户关联。
        3.  (可选) 吊销旧的Refresh Token，生成新的Refresh Token对。
        4.  调用`ITokenService.GenerateTokenAsync`生成新的Access Token。
        5.  记录审计日志。
*   **`ITokenService` 实现 (`JwtTokenService`):**
    *   从配置中读取JWT参数 (Secret, Issuer, Audience, AccessTokenExpirationMinutes, RefreshTokenExpirationDays)。
    *   `GenerateTokenAsync`: 创建`List<Claim>` (Id, Email, Name, Roles, Permissions, `jti`, `iat`, UserType)。生成JWT AccessToken。生成安全的随机字符串作为Refresh Token。持久化Refresh Token (与UserId关联, 设置过期时间)。
    *   `ValidateTokenAsync` (可选，用于验证过期的AccessToken以配合刷新)。
*   **`EnterpriseAuthorizationService` 实现:**
    *   构造函数注入 `IServiceProvider` (用于动态获取AuthorizationHandler), `ICustomerUserRepository`, `IAdminUserRepository`, `IRoleRepository`, `IPermissionRepository`, `IDistributedCache` (用于缓存用户权限/上下文).
    *   `AuthorizeAsync(userId, userType, requirement)`:
        1.  获取用户的`UserAuthorizationContext` (包含角色、直接权限、组织信息等)，优先从缓存读取。
        2.  根据`requirement`类型，动态获取所有注册的`IAuthorizationHandler<TRequirement>`。
        3.  遍历Handlers，调用其`HandleAsync`方法。
        4.  根据Handler的结果决定授权成功或失败。
        5.  记录审计日志。
*   **Authorization Handlers:** 为每个重要的操作创建`AuthorizationHandler<TRequirement>`和对应的`IAuthorizationRequirement`。例如：
    *   `ViewUserManagementPageRequirement` / `ViewUserManagementPageHandler` (检查用户是否是Admin且有特定权限)。
    *   `EditAgentRequirement(agentId)` / `EditAgentHandler` (检查用户是否是Agent所有者或拥有编辑权限的协作者)。
*   **`InputValidationMiddleware` 实现:**
    *   拦截HTTP请求。
    *   检查查询字符串、请求体、表单数据中的潜在SQL注入模式 (如 `'`, `;`, `--`, `xp_`, `exec`) 和XSS模式 (如 `<script>`, `onerror=`).
    *   若检测到，返回400 Bad Request并记录日志。
    *   注意：此中间件应作为补充，主要依赖参数化查询和HTML编码来防御。
*   **`EncryptionService` 和 `EncryptionInterceptor` 实现:**
    *   `EncryptionService(IConfiguration)`: 从配置读取对称加密密钥 (AES Key, IV)。实现`Encrypt(string)`和`Decrypt(string)`方法。
    *   `EncryptionInterceptor(IEncryptionService)`:
        *   重写`SavingChangesAsync`。
        *   遍历`ChangeTracker.Entries()`中状态为`Added`或`Modified`的实体。
        *   检查实体属性是否有`[Encrypted]`特性注解。
        *   若有，且属性值为字符串，则调用`_encryptionService.Encrypt()`更新属性值。
        *   (高级) 在读取时，可以考虑另一个拦截器或在仓储中解密，但这会更复杂。EF Core Value Converters 也是一个选项。目前仅关注写入时加密。
*   **`AuditInterceptor` 和 `IAuditLogService` 实现:**
    *   `AuditInterceptor(ICurrentUserService, IAuditLogService)`:
        *   `ICurrentUserService`: 提供当前请求的用户ID和类型。
        *   重写`SavingChangesAsync`。
        *   遍历`ChangeTracker.Entries()`，筛选出实现了`IAuditable`接口（或所有实体）且状态非`Unchanged`/`Detached`的实体。
        *   为每个实体创建`AuditEntry`对象：`UserId`, `UserType`, `EntityType`, `EntityId` (主键值), `Action` (Added, Modified, Deleted), `Timestamp`, `Changes` (字典，记录属性名、旧值、新值)。对于`Added`，旧值为空；对于`Deleted`，新值为空。
        *   调用`_auditLogService.LogAsync(auditEntries)`批量保存审计日志。
    *   `IAuditLogService` / `AuditLogService(AuditDbContext)`: 提供`LogAsync(IEnumerable<AuditEntry>)`方法，将`AuditEntry`保存到`AuditLogs`数据库表。`AuditDbContext`是独立的DbContext，确保审计日志保存不影响主业务事务（或者配置为在主事务成功后可靠地写入）。

**API层 (`Whimlab.AI.Agent.Platform.Web.Api/Controllers/V1/IdentityController`):**
*   `[HttpPost("customer/register")]`
*   `[HttpPost("customer/login")]`
*   `[HttpPost("admin/login")]`
*   `[HttpPost("token/refresh")]`
*   `[HttpPost("password/change")] [Authorize]`
*   `[HttpPost("password/forgot")]`
*   `[HttpPost("password/reset")]`
*   `AdminUsersController` (`[Authorize(Roles="SuperAdmin")]` 或基于权限):
    *   `[HttpGet]` (list), `[HttpGet("{id}")]`, `[HttpPost]`, `[HttpPut("{id}")]`, `[HttpDelete("{id}")]`, `[HttpPut("{id}/status")]`.
*   `CustomerUsersAdminController` (`[Authorize(Roles="Admin")]`):
    *   `[HttpGet]` (list), `[HttpGet("{id}")]`, `[HttpPut("{id}/status")]`.
*   `RolesAdminController` (`[Authorize(Roles="SuperAdmin")]`): CRUD, Assign/Revoke Permissions.
*   `PermissionsAdminController` (`[Authorize(Roles="SuperAdmin")]`): List.

**Customer UI (`Whimlab.AI.Agent.Platform.Client.Customer`):**
*   **Pages:**
    *   `Register.razor`: 表单提交到 `RegisterCustomerCommand`.
    *   `Login.razor`: 表单提交到 `LoginCustomerCommand`. 登录成功后，将`AuthTokenDto`存储到`Blazored.LocalStorage`并更新Fluxor `AuthState`. HTTP客户端拦截器应从此配置。
    *   `ForgotPassword.razor`, `ResetPassword.razor`.
    *   `Profile.razor`: 显示用户信息，提供修改密码表单。
*   **Fluxor Store for Auth:** `AuthState`, `LoginAction`, `LoginSuccessAction`, `LoginFailureAction`, `LogoutAction`, `AuthReducers`, `AuthEffects` (处理登录API调用，本地存储Token，导航)。

**Admin UI (`Whimlab.AI.Agent.Platform.Client.Admin`):**
*   **Pages:**
    *   `Login.razor`: 提交到 `LoginAdminCommand`.
    *   `UserManagementPage.razor`: `MudTabs`区分AdminUsers和CustomerUsers. 各自使用`MudDataGrid`显示用户列表，支持分页、搜索、排序。提供创建AdminUser的对话框 (`MudDialog`). 操作按钮用于编辑、修改状态、分配角色。
    *   `RoleManagementPage.razor`: `MudDataGrid`显示角色列表。提供创建/编辑角色的对话框，其中包含多选框 (`MudCheckBox`) 或 `MudTransfer` 用于分配权限给角色。
*   **Fluxor Store for Auth & Admin data if needed.**

---

### **模块2: AI智能体管理 - 详细实现指令**

**核心概念:** 管理员创建和配置两种主要类型的AI智能体：
1.  **通用AI智能体 (Universal AI Agent):** 基于Microsoft Semantic Kernel。配置系统提示词、模型ID (从管理员配置的模型列表中选择)及其他SK参数。
2.  **Dify AI智能体 (Dify AI Agent):** 基于调用Dify平台API。配置Dify API密钥、Dify App ID (聊天/补全类型)、Workflow ID (工作流类型) 或 Dify Agent ID (Agent类型)。

**领域层 (`Whimlab.AI.Agent.Platform.Domain.Agent`):**

*   **聚合 `AgentAggregate`:**
    *   **属性:** `AgentId Id { get; private set; }`, `AgentName Name { get; private set; }`, `AgentDescription Description { get; private set; }`, `AgentType Type { get; private set; }`, `AgentStatus Status { get; private set; }`, `AdminUserId OwnerId { get; private set; }`, `DateTime CreatedAt { get; private set; }`, `DateTime LastModifiedAt { get; private set; }`, `AgentVersion CurrentVersion { get; private set; }` (导航属性，实际配置在Version中), `AgentStatistics Statistics { get; private set; }`.
    *   **私有集合:** `private readonly List<AgentVersion> _versions = new();`, `private readonly List<AgentExecutionLog> _recentExecutions = new();` (用于短期追踪或统计基础，非完整执行记录), `private readonly List<AgentCollaborator> _collaborators = new();`
    *   **只读访问器:** `public IReadOnlyList<AgentVersion> Versions => _versions.AsReadOnly();` 等。
    *   **构造函数:** `public AgentAggregate(AgentId id, AgentName name, AgentDescription description, AgentType type, AdminUserId ownerId, AgentConfiguration initialConfiguration)`:
        *   执行领域验证 (名称非空，类型有效，所有者有效，初始配置有效)。
        *   设置初始状态为`Draft`，记录创建时间。
        *   创建第一个`AgentVersion`，版本号为1.0，包含`initialConfiguration`。
        *   `CurrentVersion`指向此初始版本。
        *   初始化`AgentStatistics`。
        *   添加`AgentCreatedDomainEvent`。
    *   **方法:**
        *   `public void UpdateConfiguration(AgentConfiguration newConfiguration, AdminUserId updatedBy, string changeDescription)`:
            *   验证操作权限 (所有者或有权限的协作者)。
            *   验证新配置的有效性 (`newConfiguration.Validate()`)。
            *   若新配置与`CurrentVersion.Configuration`有显著差异 (`CurrentConfiguration.HasSignificantChanges(newConfiguration)` - 此方法需在`AgentConfiguration`中实现，比较关键模型参数、提示等)，则创建新的`AgentVersion` (版本号递增)，将`newConfiguration`存入新版本，`updatedBy`和`changeDescription`记录到新版本。`CurrentVersion`指向新版本。发布`AgentVersionCreatedDomainEvent`。
            *   更新`LastModifiedAt`。
            *   发布`AgentConfigurationUpdatedDomainEvent`。
        *   `public void Publish(AdminUserId publishedBy)`:
            *   验证权限。
            *   若状态已经是`Published`，抛出领域异常。
            *   验证`CurrentVersion.Configuration`是否完整且有效，适合发布。
            *   设置`Status = AgentStatus.Published`。更新`LastModifiedAt`。
            *   发布`AgentPublishedDomainEvent(Id, CurrentVersion.Id, publishedBy)`。
        *   `public void Archive(AdminUserId archivedBy)`:
            *   验证权限。设置`Status = AgentStatus.Archived`。发布`AgentArchivedDomainEvent`。
        *   `public void AddCollaborator(AdminUserId collaboratorId, AgentPermissionLevel permission, AdminUserId addedBy)`: (未来考虑)
        *   `public void RecordExecutionStart(AgentExecutionId executionId, CustomerUserId customerId, DateTime startTime)`:
            *   创建`AgentExecutionLog`条目 (如果`_recentExecutions`用于此目的)。
            *   更新`AgentStatistics.IncrementExecutionCount()`。
            *   更新`LastModifiedAt`。
            *   发布`AgentExecutionStartedDomainEvent`。
        *   `public void RecordExecutionCompletion(AgentExecutionId executionId, int tokensUsed, decimal cost, TimeSpan duration, bool success, float? rating)`:
            *   更新对应`AgentExecutionLog`。
            *   更新`AgentStatistics.RecordSuccessfulExecution(tokensUsed, cost, duration)`或`RecordFailedExecution()`。
            *   若提供了评分，更新`AgentStatistics.AddRating(rating)`。
            *   发布`AgentExecutionCompletedDomainEvent`。
*   **实体 `AgentVersion`:** `AgentVersionId Id`, `AgentId AgentId`, `VersionNumber VersionNumber` (值对象 e.g., "1.0", "1.1"), `AgentConfiguration Configuration` (值对象), `string ChangeDescription`, `AdminUserId CreatedBy`, `DateTime CreatedAt`.
*   **实体 `AgentStatistics`:** `AgentId AgentId` (主键/外键), `long TotalExecutions`, `long TotalTokensUsed`, `decimal TotalCost`, `double AverageRating`, `int RatingCount`, `long SuccessfulExecutions`, `long FailedExecutions`, `TimeSpan AverageDuration`. 包含更新这些统计数据的方法。
*   **值对象 `AgentName`:** `string Value` (验证长度、允许字符等)。
*   **值对象 `AgentDescription`:** `string Value`。
*   **枚举/值对象 `AgentType`:** `SemanticKernel`, `DifyChat`, `DifyCompletion`, `DifyAgentApp`, `DifyWorkflow`.
*   **枚举/值对象 `AgentStatus`:** `Draft`, `Published`, `Archived`.
*   **值对象 `AgentConfiguration`:** (核心，需要非常详细)
    *   **公共属性:** `AgentType Type { get; }` (用于区分配置结构)。
    *   **条件属性 (根据Type):**
        *   **`SemanticKernelConfiguration SKConfig { get; private set; }`** (若Type为SemanticKernel)
            *   `ModelId` (string, e.g., "gpt-4", "claude-3-opus" - 从管理员配置的模型列表中选择).
            *   `SystemPrompt` (string, max length).
            *   `Temperature` (double, 0.0-2.0).
            *   `MaxTokens` (int, >0).
            *   `TopP` (double, 0.0-1.0).
            *   `PresencePenalty` (double, -2.0-2.0).
            *   `FrequencyPenalty` (double, -2.0-2.0).
            *   `StopSequences` (List<string>, 可选).
            *   `ChatSettings` (Dictionary<string, object> or specific class for provider-specific chat settings).
            *   `PluginReferences` (List<string>, 引用已注册的SK插件, 可选).
        *   **`DifyConfiguration DifyConfig { get; private set; }`** (若Type为Dify*)
            *   `DifyApiKey` (string, 敏感数据，应标记为加密存储 `[Encrypted]`).
            *   `DifyBaseUrl` (string, e.g., "https://api.dify.ai/v1").
            *   `DifyAppId` (string, 若Type为 `DifyChat` or `DifyCompletion`).
            *   `DifyAgentId` (string, 若Type为 `DifyAgentApp`).
            *   `DifyWorkflowId` (string, 若Type为 `DifyWorkflow`).
            *   `ResponseMode` (string, e.g., "blocking", "streaming" - Dify特定).
            *   `ConversationContextMode` (string, e.g., "auto", "none" - Dify特定, 指示如何处理Dify端的对话ID).
            *   `IncludeSensitiveDataInResponse` (bool, Dify特定).
    *   **方法:**
        *   `ValidationResult Validate()`: 根据`Type`验证对应的配置是否完整且有效。例如，SKConfig的ModelId不能为空，DifyConfig的ApiKey不能为空，且对应类型的ID（AppId/AgentId/WorkflowId）也必须存在。
        *   `bool HasSignificantChanges(AgentConfiguration other)`: 比较此配置与另一个配置，判断变更是否足以创建新版本。
*   **领域事件:** `AgentCreatedEvent(AgentId, Name, Type, OwnerId)`, `AgentConfigurationUpdatedEvent(AgentId, VersionId, UpdatedBy)`, `AgentVersionCreatedEvent(AgentId, NewVersionId, VersionNumber, CreatedBy)`, `AgentPublishedEvent(AgentId, VersionId, PublishedBy)`, `AgentArchivedEvent(AgentId, ArchivedBy)`.
*   **仓储接口 `IAgentRepository`:** (除了CRUD) `Task<AgentAggregate> GetByNameAndOwnerAsync(AgentName name, AdminUserId ownerId)`, `Task<PagedResult<AgentAggregate>> ListAsync(ListAgentsSpecification spec)`.
    *   **`IAgentReadRepository`:** (用于查询) `Task<AgentDto> GetPublishedAgentDetailsAsync(AgentId id)`, `Task<IEnumerable<AgentSummaryDto>> ListPublishedAgentsAsync(FilterAndSortOptions options)`.

**应用层 (`Whimlab.AI.Agent.Platform.Application.Agent`):**

*   **命令:**
    *   `CreateAgentCommand(Name, Description, AgentType, SemanticKernelConfigDto skConfig, DifyConfigDto difyConfig, AdminUserId createdBy)` -> `AgentDto`. (skConfig和difyConfig是互斥的DTO，根据AgentType传递一个)。
    *   `UpdateAgentConfigurationCommand(AgentId, SemanticKernelConfigDto skConfig, DifyConfigDto difyConfig, AdminUserId updatedBy, string changeDescription)` -> `AgentVersionDto`.
    *   `PublishAgentCommand(AgentId, AdminUserId publishedBy)`.
    *   `ArchiveAgentCommand(AgentId, AdminUserId archivedBy)`.
    *   `ExecuteAgentCommand(AgentId, CustomerUserId customerId, ConversationInputDto input, ConversationId conversationId, Dictionary<string, object> executionParameters)` -> (void, 结果通过事件/SignalR流式传输).
*   **查询:**
    *   `GetAgentDetailsForEditingQuery(AgentId id)` -> `AgentDetailsEditDto` (包含所有版本和当前配置)。
    *   `ListAgentsForAdminQuery(PageNumber, PageSize, SearchTerm, AgentTypeFilter, AgentStatusFilter, SortBy, SortDirection)` -> `PagedResult<AgentSummaryAdminDto>`.
    *   `GetPublishedAgentByIdQuery(AgentId id)` -> `AgentPublicDto` (客户可见的信息)。
    *   `ListPublishedAgentsForCustomerQuery(PageNumber, PageSize, SearchTerm, CategoryFilter, SortBy)` -> `PagedResult<AgentPublicSummaryDto>`.
*   **DTOs:**
    *   `SemanticKernelConfigDto`, `DifyConfigDto` (与领域层值对象对应，用于API传输)。
    *   `AgentDto` (聚合的完整表示)。
    *   `AgentVersionDto`.
    *   `AgentSummaryAdminDto` (列表显示给管理员)。
    *   `AgentPublicDto` (客户看到的智能体详情)。
    *   `AgentPublicSummaryDto` (客户看到的智能体列表项)。
    *   `ConversationInputDto` (UserId, Content, FileAttachments [可选], ExistingConversationId [可选]).
*   **命令处理器实现 (`CreateAgentCommandHandler` 作为示例):**
    1.  验证输入 (FluentValidation on Command DTO).
    2.  授权检查 (`IAuthorizationService`: 只有特定角色的Admin可以创建Agent).
    3.  将DTO转换为`AgentConfiguration`值对象。调用`agentConfiguration.Validate()`。
    4.  创建`AgentAggregate`实例。
    5.  调用`_agentRepository.AddAsync(agent)`.
    6.  调用`_unitOfWork.CommitAsync()`.
    7.  分发`agent.DomainEvents` (`IDomainEventDispatcher`).
    8.  记录审计日志.
    9.  返回映射后的`AgentDto`.
*   **`ExecuteAgentCommandHandler` 实现:**
    1.  验证输入。
    2.  授权检查 (客户是否有权使用此Agent，订阅是否有效，`QuotaManagementService.CheckQuotaAsync`).
    3.  加载`AgentAggregate`及其`CurrentVersion.Configuration`.
    4.  调用`agent.RecordExecutionStart()`.
    5.  调用`_agentRepository.UpdateAsync(agent)` 和 `_unitOfWork.CommitAsync()`. (记录开始)
    6.  创建`AgentExecutionContext` (包含ConversationId, CustomerId, AgentConfig, Input).
    7.  **异步调用 `IAgentExecutionService.ExecuteAsync(executionContext)`**.
        *   `IAgentExecutionService`内部：
            *   根据AgentType和Config，选择`EnterpriseSemanticKernelService`或`EnterpriseDifyApiService`.
            *   调用相应的AI服务执行方法 (可能是流式的).
            *   对于流式响应: 持续接收AI服务返回的chunks. 对每个chunk:
                *   发布`AgentStreamChunkReceivedEvent(conversationId, chunkContent, isFinalChunk, tokenInfo)`. (此事件由对话模块的SignalR处理器监听并推送到客户端).
            *   执行完成后:
                *   获取总Token用量，成本 (若AI服务提供)。
                *   调用`QuotaManagementService.RecordUsageAsync()`.
                *   发布`AgentExecutionCompletedEvent(conversationId, agentId, customerId, finalResponse, tokensUsed, cost, duration, success, ratingInput)`.
                *   (或者，`ExecuteAgentCommandHandler`可以直接处理来自`IAgentExecutionService`的流，并发布事件)
    8.  CommandHandler本身可能不直接等待AI执行完成，而是触发异步流程。

**基础设施层 (`Whimlab.AI.Agent.Platform.Infrastructure.AI` & `Whimlab.AI.Agent.Platform.Infrastructure.Data`):**

*   **`IAgentRepository` 实现:**
    *   使用`AgentPlatformDbContext` (写) 和 `IAgentReadDbContext` (读)。
    *   `AgentConfiguration` 存储为JSONB列在`AgentVersions`表中，并在加载时反序列化为强类型的`AgentConfiguration`值对象。
    *   实现缓存逻辑 (L1内存, L2 Redis) 如 `AgentRepository` 示例 (优化文档第五部分第10.1节)，包括获取、添加、更新、删除时的缓存读取和失效。
    *   使用`AsSplitQuery()`优化包含集合的查询。
*   **`IAgentReadRepository` 实现:**
    *   用于优化查询，返回`AgentPublicDto`等只读模型。
    *   可能使用Dapper进行复杂的列表查询或聚合。
*   **`EnterpriseSemanticKernelService` 实现:**
    *   构造函数注入`IConfiguration` (用于模型配置), `IModelRouter`, `ITokenUsageTracker`, `IModelPerformanceMonitor`, `ICircuitBreakerFactory` (Polly), `ILogger`, `IHttpClientFactory`.
    *   `InitializeKernels`: 从`appsettings.json`的`AI:Models:SemanticKernel`部分读取多个模型配置 (ProviderType [OpenAI, AzureOpenAI, Anthropic], ModelId, ApiKey, Endpoint, OrgId等)。为每个配置创建并缓存一个`IKernel`实例。
        *   每个`HttpClient`通过`IHttpClientFactory`创建，并配置Polly重试策略和超时。
    *   `ExecuteAsync(prompt, variables, modelSelectionCriteria)`:
        1.  调用`_modelRouter.SelectOptimalModelAsync(prompt, modelSelectionCriteria)`选择最佳Kernel实例。
        2.  使用Polly断路器执行Kernel调用 (`kernel.CreateSemanticFunction(prompt, promptTemplateConfig).InvokeAsync(context)`).
        3.  从`FunctionResult`或`ModelResult`中提取Token用量和响应。
        4.  调用`_tokenTracker.TrackUsageAsync()`.
        5.  调用`_performanceMonitor.RecordPerformanceMetricsAsync()`.
    *   `ExecuteStreamingAsync(...)`: 类似，但使用`kernel.RunStreamingAsync(...)`并返回`IAsyncEnumerable<StreamingContent>`。
*   **`EnterpriseDifyApiService` 实现:**
    *   构造函数注入`IHttpClientFactory`, `IOptions<DifyGlobalConfiguration>` (包含全局Dify Base URL, 默认ApiKey等), `IConversationStateManager_Dify` (若Dify多轮对话需要外部管理其`conversation_id`), `IWorkflowExecutionTracker`, `IRetryPolicyProvider` (Polly), `ILogger`.
    *   为Dify的Chat, Completion, Agent, Workflow API分别实现方法。
    *   所有HTTP调用通过`IHttpClientFactory`创建的客户端进行，并应用Polly重试和断路器策略。
    *   请求和响应使用强类型的C# DTO。
    *   `StreamChatAsync`需正确处理SSE (Server-Sent Events)，解析`data:`行，处理`[DONE]`事件，并`yield return`每个消息块。
    *   安全处理Dify API Key (从`AgentConfiguration.DifyConfig.DifyApiKey`获取，该字段应标记为`[Encrypted]`)。
*   **`IModelCatalog` 实现:** (供`IntelligentModelRouter`使用)
    *   从配置或数据库中加载管理员定义的可用Semantic Kernel模型及其能力 (上下文长度, 支持函数调用, 成本/token等)。
*   **缓存:** Agent定义、已编译的Prompt/Function (如果Semantic Kernel支持) 可以被缓存。

**API层 (`Whimlab.AI.Agent.Platform.Web.Api`):**
*   **`AdminAgentsController` (`[Authorize(Policy="ManageAgents")]`):**
    *   `[HttpGet]` -> `ListAgentsForAdminQuery`
    *   `[HttpGet("{id}")]` -> `GetAgentDetailsForEditingQuery`
    *   `[HttpPost]` -> `CreateAgentCommand`
    *   `[HttpPut("{id}/configuration")]` -> `UpdateAgentConfigurationCommand`
    *   `[HttpPost("{id}/publish")]` -> `PublishAgentCommand`
    *   `[HttpPost("{id}/archive")]` -> `ArchiveAgentCommand`
*   **`CustomerAgentsController` (`[Authorize]`):**
    *   `[HttpGet]` -> `ListPublishedAgentsForCustomerQuery`
    *   `[HttpGet("{id}")]` -> `GetPublishedAgentByIdQuery`
    *   `[HttpPost("{id}/execute")]` -> `ExecuteAgentCommand` (此端点是核心交互点，可能需要特殊处理流式响应或将其转换为SignalR推送)

**管理员UI (`Whimlab.AI.Agent.Platform.Client.Admin`):**
*   **Agent List Page:** `MudDataGrid`显示`AgentSummaryAdminDto`。列：Name, Type, Status, Last Modified。操作：Edit, Publish/Unpublish, Archive, View Stats。支持服务器端过滤、排序、分页。
*   **Create/Edit Agent Page (`MudForm`):**
    *   `MudTextField` for Name, Description.
    *   `MudSelect<AgentTypeEnum>` for Agent Type.
    *   **动态配置区域 (基于选定的AgentType显示/隐藏):**
        *   **Semantic Kernel:**
            *   `MudTextField` (multiline) for System Prompt.
            *   `MudSelect<string>` for Model ID (选项从后端API获取，即管理员配置的模型列表).
            *   `MudSlider` for Temperature (0.0-2.0).
            *   `MudNumericField<int>` for MaxTokens.
            *   (可选) `MudTextField` for Stop Sequences (逗号分隔).
            *   (可选) `MudChipSet` / `MudMultiSelection` for PluginReferences.
        *   **Dify (通用):**
            *   `MudTextField` for Dify API Key (type="password").
            *   `MudTextField` for Dify Base URL (可有默认值).
        *   **Dify Chat/Completion:** `MudTextField` for Dify App ID.
        *   **Dify Agent App:** `MudTextField` for Dify Agent ID.
        *   **Dify Workflow:** `MudTextField` for Dify Workflow ID.
            *   (可选) `MudSelect<string>` for Dify ResponseMode ("blocking", "streaming").
    *   `MudButton` "Save Draft", "Save and Publish".
    *   版本历史查看区域 (只读列表显示`AgentVersionDto`).

**客户UI (`Whimlab.AI.Agent.Platform.Client.Customer`):**
*   **Agent Marketplace Page:** `MudGrid` + `MudCard`显示`AgentPublicSummaryDto`。卡片包含Name, Description, Type, Tags, AverageRating。点击卡片导航到对话页面，并传递AgentId。

---

### **模块3: 对话管理 - 详细实现指令**

**领域层 (`Whimlab.AI.Agent.Platform.Domain.Conversation`):**

*   **聚合 `ConversationAggregate`:**
    *   **属性:** `ConversationId Id`, `CustomerUserId CustomerId`, `AgentId AgentId`, `ConversationTitle Title` (值对象), `DateTime CreatedAt`, `DateTime LastActivityAt`, `ConversationStatus Status` (枚举: `Active`, `ArchivedByUser`, `AutoArchivedSystem`), `ICollection<ConversationMessage> Messages` (只在加载聚合时加载有限数量的最新消息，或仅加载元数据), `ConversationContext CurrentContext` (值对象，包含如Dify的`conversation_id`或SK的内存状态).
    *   **构造函数:** `public ConversationAggregate(ConversationId id, CustomerUserId customerId, AgentId agentId, ConversationTitle title, MessageContent initialUserMessage)`:
        *   创建对话，添加初始用户消息，设置`Active`状态，记录时间。
        *   发布`ConversationStartedEvent`.
    *   **方法:**
        *   `public ConversationMessage AddUserMessage(MessageContent content, CustomerUserId userId)`:
            *   验证用户是否为参与者，对话是否`Active`。
            *   创建`ConversationMessage` (SenderType.User)。添加到`Messages`集合 (若在内存中维护)。
            *   更新`LastActivityAt`。
            *   更新`CurrentContext`。
            *   发布`UserMessageAddedEvent`.
        *   `public ConversationMessage AddAgentResponse(MessageContent content, int tokensUsed, Dictionary<string, object> aiMetadata)`:
            *   创建`ConversationMessage` (SenderType.Agent)。添加到`Messages`集合。
            *   更新`LastActivityAt`。
            *   更新`CurrentContext`。
            *   发布`AgentResponseAddedEvent`.
        *   `public void Archive(CustomerUserId userId)`: (用户归档)
        *   `public void SystemArchive()`: (系统自动归档，如长时间不活跃)
*   **实体 `ConversationMessage`:** `MessageId Id`, `ConversationId ConversationId`, `SenderType Sender` (枚举: `User`, `Agent`), `MessageContent Content` (值对象，可包含文本、附件引用), `DateTime Timestamp`, `int TokenCount` (若适用), `Dictionary<string, object> AIMetadata` (JSONB, 存储AI提供商返回的特定元数据，如Dify的`retriever_resources`).
*   **仓储接口:** `IConversationRepository`, `IMessageRepository` (若消息单独存储)。

**应用层 (`Whimlab.AI.Agent.Platform.Application.Conversation`):**

*   **命令:**
    *   `StartConversationCommand(CustomerUserId customerId, AgentId agentId, string initialMessageContent)` -> `ConversationDto` (包含初始消息和对话ID).
    *   `PostUserMessageCommand(ConversationId conversationId, CustomerUserId customerId, string messageContent, List<FileUploadDto> attachments)` -> `MessageAckDto` (仅确认消息已接收并开始处理).
    *   `ArchiveConversationCommand(ConversationId conversationId, CustomerUserId customerId)`.
*   **查询:**
    *   `GetConversationDetailsQuery(ConversationId conversationId, CustomerUserId customerId)` -> `ConversationWithMessagesDto`.
    *   `ListUserConversationsQuery(CustomerUserId customerId, PageNumber, PageSize, SortBy)` -> `PagedResult<ConversationSummaryDto>`.
    *   `GetConversationMessagesQuery(ConversationId conversationId, CustomerUserId customerId, PageNumber, PageSize, BeforeMessageId)` -> `PagedResult<MessageDto>`.
*   **DTOs:** `ConversationDto`, `MessageDto` (含SenderType, Content, Timestamp, AIMetadata), `ConversationSummaryDto`, `ConversationWithMessagesDto`, `StreamChunkDto` (MessageId, ChunkContent, IsFinal, TokenInfo), `MessageAckDto`.
*   **`PostUserMessageCommandHandler` 实现:**
    1.  验证。授权 (客户只能发到自己的对话)。
    2.  加载`ConversationAggregate`。
    3.  加载`CustomerSubscription`，调用`QuotaManagementService.CheckQuotaAsync()`. 若不足，抛异常。
    4.  调用`conversation.AddUserMessage(...)`.
    5.  保存`ConversationAggregate` (`_conversationRepository.UpdateAsync`, UoW).
    6.  发布`UserMessageAddedEvent` (此事件由SignalR Hub处理器监听，将用户消息推给客户端)。
    7.  **异步分发 `ExecuteAgentCommand` (来自Agent模块)**，传递`conversationId`, `agentId`, `customerId`, `messageContent`, 和从`conversation.CurrentContext`获取的上下文信息。
    8.  返回`MessageAckDto`.
*   **应用层事件处理器 (监听来自Agent模块的事件):**
    *   **`HandleAgentStreamChunkEvent(AgentStreamChunkReceivedEvent domainEvent)`:**
        1.  (可选) 如果需要累积或部分持久化流式块，则执行。
        2.  获取与`domainEvent.ConversationId`关联的SignalR Hub上下文。
        3.  调用该Hub的客户端方法 `ReceiveAgentStreamChunk(new StreamChunkDto { ... })`。
    *   **`HandleAgentExecutionCompletedEvent(AgentExecutionCompletedEvent domainEvent)`:**
        1.  加载`ConversationAggregate`。
        2.  调用`conversation.AddAgentResponse(domainEvent.FinalResponse, domainEvent.TokensUsed, domainEvent.AIMetadata)`.
        3.  保存`ConversationAggregate`.
        4.  调用`QuotaManagementService.RecordUsageAsync(domainEvent.CustomerId, domainEvent.TokensUsed)` (确保此操作的幂等性和可靠性，若AI执行成功但配额记录失败，需要补偿机制或重试)。
        5.  获取SignalR Hub上下文。
        6.  调用Hub的客户端方法 `ReceiveAgentResponseComplete(new MessageDto { ... mapped from domainEvent ... })`。
*   **`ConversationContextService` 实现:**
    *   `BuildContextForAgentAsync(ConversationId conversationId, AgentConfiguration agentConfig)`:
        1.  从缓存或`IMessageRepository`获取最近的N条消息 (N由`agentConfig`或全局设置决定)。
        2.  如果对话很长且`agentConfig`需要摘要，则对更早的消息生成摘要 (可能调用另一个AI服务进行摘要)。
        3.  格式化消息历史以符合特定AI（SK或Dify）的输入要求。
        4.  如果使用Dify且需要Dify的`conversation_id`，则从`ConversationAggregate.CurrentContext`中获取或向Dify发起一个获取/创建会话的请求。
        5.  返回包含格式化历史和任何其他上下文参数的对象。
    *   缓存构建的上下文短时间以避免重复计算。

**基础设施层 (`Whimlab.AI.Agent.Platform.Infrastructure.Data`, `Whimlab.AI.Agent.Platform.Infrastructure.Messaging`):**

*   **`IConversationRepository` 实现 (PostgreSQL):** 存储`Conversations`聚合元数据。
*   **`IMessageRepository` 实现 (MongoDB 首选):**
    *   `AddAsync(ConversationMessage message)`
    *   `GetMessagesByConversationAsync(ConversationId conversationId, int skip, int take, DateTime? beforeTimestamp)`: 按时间倒序分页获取消息。
    *   在`ConversationId`和`Timestamp`上创建索引。
*   **SignalR Hub (`ConversationHub` in `Whimlab.AI.Agent.Platform.Web.SignalR`):**
    *   **连接管理:**
        *   `OnConnectedAsync()`: 用户连接时，可以记录连接ID与UserID的映射 (例如在Redis中)，用于定向推送。
        *   `OnDisconnectedAsync()`: 清理映射。
    *   **组管理:**
        *   `Task JoinConversationGroup(string conversationId)`: 客户端在进入聊天页时调用。将连接加入到名为`conversationId`的组。验证用户是否有权访问此对话。
        *   `Task LeaveConversationGroup(string conversationId)`: 客户端离开聊天页时调用。
    *   **客户端调用的方法 (Hub Methods):**
        *   `Task SendUserMessageToHub(string conversationId, string messageContent, List<FileUploadDto> attachments)`: 客户端发送消息时调用此方法。此方法内部会构造并分发`PostUserMessageCommand`。
    *   **服务器推送给客户端的方法 (Client Proxies):**
        *   `Task ReceiveUserMessage(MessageDto userMessage)`: 当用户消息被成功处理并保存后，推送给组内所有客户端。
        *   `Task ReceiveAgentStreamChunk(StreamChunkDto chunk)`: 推送AI的流式响应块。
        *   `Task ReceiveAgentResponseComplete(MessageDto agentMessage)`: 推送AI的最终完整响应。
        *   `Task ReceiveConversationError(string conversationId, string errorMessage)`: 若处理出错，通知客户端。
        *   `Task TypingIndicatorUpdate(string conversationId, bool isTyping, string agentName)`: AI正在输入的状态。
    *   **认证:** Hub方法应使用`[Authorize]`特性。SignalR的`AccessTokenProvider`在客户端配置，用于在连接时传递JWT。

**API层 (`Whimlab.AI.Agent.Platform.Web.Api/Controllers/V1/ConversationsController`):**
*   `[HttpPost]` -> `StartConversationCommand`
*   `[HttpGet]` -> `ListUserConversationsQuery`
*   `[HttpGet("{conversationId}")]` -> `GetConversationDetailsQuery`
*   `[HttpGet("{conversationId}/messages")]` -> `GetConversationMessagesQuery`
*   `[HttpPost("{conversationId}/archive")]` -> `ArchiveConversationCommand`
*   **注意:** 实际聊天消息不通过REST API发送，而是通过SignalR Hub的`SendUserMessageToHub`方法。

**客户UI (`Whimlab.AI.Agent.Platform.Client.Customer`):**
*   **`ChatPage.razor` (`@page "/chat/{ConversationId:guid}")`:**
    *   参数 `[Parameter] public Guid ConversationId { get; set; }`.
    *   `OnInitializedAsync`:
        *   加载对话详情 (`GetConversationDetailsQuery`) 和初始消息 (`GetConversationMessagesQuery`).
        *   初始化SignalR连接 (`HubConnectionBuilder`)，配置 `AccessTokenProvider` 从本地存储获取JWT。
        *   注册SignalR客户端事件处理器 (`hubConnection.On<MessageDto>("ReceiveUserMessage", ...)`等)。
        *   启动连接并调用 `hubConnection.InvokeAsync("JoinConversationGroup", ConversationId)`.
    *   **消息显示:** `MudList`或自定义滚动区域显示`MessageDto`列表。使用`MessageBubble.razor`组件，根据`SenderType`区分用户和AI消息样式。AI消息在流式传输时动态更新其内容。
    *   **输入区域:** `MudTextField @bind-Value="currentMessageInput" @onkeydown="HandleInputKeyDown"`, `MudIconButton Icon="@Icons.Material.Filled.Send" OnClick="SendChatMessageAsync" Disabled="@string.IsNullOrWhiteSpace(currentMessageInput)"`.
    *   `SendChatMessageAsync()`: 调用 `hubConnection.InvokeAsync("SendUserMessageToHub", ConversationId, currentMessageInput)`. 清空输入框。
    *   **流式处理:** `ReceiveAgentStreamChunk`处理器追加内容到对应的AI消息气泡。`ReceiveAgentResponseComplete`处理器标记流式结束并最终化AI消息。
    *   **状态管理 (Fluxor):** `ConversationState` (当前`ConversationId`, `List<MessageDto> Messages`, `IsAgentTyping`). Actions: `LoadConversationAction`, `LoadMessagesSuccessAction`, `UserMessageSentAction`, `AgentChunkReceivedAction`, `AgentResponseFinalizedAction`. Effects处理API调用和SignalR事件。
    *   `IAsyncDisposable`: 在`DisposeAsync`中调用 `hubConnection.InvokeAsync("LeaveConversationGroup", ConversationId)` 并 `DisposeAsync` Hub连接。

---

### **模块4: 订阅与配额管理 - 详细实现指令**

**核心概念:** 新客户有试用期。试用期后需订阅付费套餐。管理员管理套餐。

**试用逻辑:** 新客户注册后自动获得3天或10,000 tokens的试用额度 (以先到者为准)。

**领域层 (`Whimlab.AI.Agent.Platform.Domain.Subscription`):**

*   **聚合 `SubscriptionPlan`:** `PlanId Id`, `string Name`, `string Description`, `Price Price` (值对象, e.g., `decimal Amount, string Currency`), `BillingCycle Cycle` (值对象/枚举: `Monthly`, `Yearly`), `TokenAmount TokenQuotaPerCycle` (值对象), `IReadOnlyList<string> Features`, `bool IsActive`, `bool IsPubliclyVisible`.
*   **聚合 `CustomerSubscription`:** `SubscriptionId Id`, `CustomerUserId CustomerId`, `PlanId PlanId`, `SubscriptionStatus Status` (枚举: `Trialing`, `Active`, `PaymentFailed`, `Canceled`, `Expired`), `DateTime StartDate`, `DateTime EndDate`, `DateTime? TrialEndsAt` (基于注册时间+3天), `long CurrentTokensUsedInCycle`, `bool AutoRenews`.
    *   **方法:**
        *   `StartTrial(CustomerUserId customerId, DateTime trialEndDate)`: 创建试用订阅。
        *   `Activate(PlanId planId, DateTime startDate, DateTime endDate, TokenAmount tokenQuota)`: 激活付费订阅。重置`CurrentTokensUsedInCycle`.
        *   `Renew(DateTime newEndDate, TokenAmount tokenQuota)`: 续订。重置`CurrentTokensUsedInCycle`.
        *   `Cancel(DateTime cancelAt)`: 标记为在周期结束时取消。
        *   `Expire()`: 标记为已过期。
        *   `RecordTokenUsage(long tokens)`: 增加`CurrentTokensUsedInCycle`. 返回是否超出此周期配额。
        *   `bool IsTrialActive(DateTime currentDate)`: 检查`TrialEndsAt`和`CurrentTokensUsedInCycle` (对照试用期10000 token限额)。
        *   `bool IsSubscriptionActive(DateTime currentDate)`: 检查`Status`和`EndDate`.
        *   `bool HasQuotaRemaining(long requestedTokens)`: 检查当前周期内是否有足够配额。
*   **仓储接口:** `ISubscriptionPlanRepository`, `ICustomerSubscriptionRepository`.
*   **领域服务 `ISubscriptionLifecycleService`:** (可选，部分逻辑可在应用服务中) `ExpireOverdueTrials()`, `ProcessRenewals()`.
*   **领域服务 `IQuotaValidationService`:** (更简单，由应用服务调用) `CheckAndUpdateQuota(CustomerSubscription subscription, long tokensToConsume)`.

**应用层 (`Whimlab.AI.Agent.Platform.Application.Subscription`):**

*   **命令 (Admin):** `CreatePlanCommand`, `UpdatePlanCommand`, `TogglePlanVisibilityCommand`.
*   **命令 (Customer):** `ChoosePlanAndInitiatePaymentCommand(CustomerId, PlanId)` -> `PaymentInitiationDto`.
*   **命令 (System/Webhook):** `FinalizeSubscriptionAfterPaymentCommand(PaymentId, CustomerId, PlanId, PaymentStatus)`.
*   **命令 (System/Scheduler):** `ProcessDailySubscriptionTasksCommand` (触发试用期检查、续订检查).
*   **查询 (Admin):** `ListAllPlansQuery`, `GetPlanByIdQuery`, `ListCustomerSubscriptionsAdminQuery`.
*   **查询 (Customer):** `GetMyCurrentSubscriptionQuery(CustomerId)`, `ListPublicPlansQuery`.
*   **DTOs:** `SubscriptionPlanDto`, `CustomerSubscriptionDto` (含当前token用量和周期结束时间).
*   **`TrialManagementService` (应用服务):**
    *   `HandleCustomerRegisteredEvent(CustomerRegisteredEvent domainEvent)`: 创建一个`Trialing`状态的`CustomerSubscription`，设置`TrialEndsAt` (注册时间+3天)，初始`TokenQuotaPerCycle`为10000。保存。
*   **`QuotaManagementService` (应用服务):**
    *   `Task<bool> CheckAndDeductTokensAsync(CustomerUserId customerId, AgentId agentId, long tokensToDeduct)`:
        1.  加载`CustomerSubscription`。
        2.  调用`customerSubscription.IsTrialActive()` 或 `customerSubscription.IsSubscriptionActive()`. 若均否，返回false。
        3.  调用`customerSubscription.HasQuotaRemaining(tokensToDeduct)`. 若否，返回false。
        4.  **重要: 使用Redis进行高性能的原子性配额扣减和检查。**
            *   键: `quota:<customerId>:<subscriptionCycleIdentifier>`. 值: 当前周期剩余tokens.
            *   使用`DECRBY`或Lua脚本原子地扣减并检查是否<0。如果<0，则`INCRBY`回滚，并返回配额不足。
            *   如果Redis操作成功，则异步地将`tokensToDeduct`记录到`CustomerSubscription.CurrentTokensUsedInCycle` (PostgreSQL) 以持久化。这里可能存在最终一致性。
        5.  若Redis检查通过，则调用`customerSubscription.RecordTokenUsage(tokensToDeduct)`更新聚合状态。
        6.  保存`CustomerSubscription`.
        7.  若配额即将用尽 (e.g., <10%)，发布`QuotaThresholdReachedEvent`.
        8.  返回true.
*   **`SubscriptionService` (应用服务):**
    *   `ChoosePlanAndInitiatePaymentAsync(ChoosePlanAndInitiatePaymentCommand command)`:
        1.  加载Plan。
        2.  创建`CreatePaymentCommand` (来自支付模块)，金额为Plan价格。
        3.  调用支付模块的`CreatePaymentCommandHandler`，获取`PaymentInitiationDto`并返回。
    *   `FinalizeSubscriptionAfterPaymentAsync(FinalizeSubscriptionAfterPaymentCommand command)`: (由支付成功Webhook触发)
        1.  加载`CustomerSubscription` (若存在) 和 `SubscriptionPlan`.
        2.  若支付成功:
            *   若当前是试用或无有效订阅，则调用`customerSubscription.Activate(...)`或创建新订阅并激活。
            *   若当前是付费订阅且是续订，则调用`customerSubscription.Renew(...)`。
            *   保存`CustomerSubscription`.
            *   发布`SubscriptionActivatedEvent`或`SubscriptionRenewedEvent`.
        3.  若支付失败: 发布`SubscriptionPaymentFailedEvent`.
*   **`SubscriptionStateMachine` (应用服务或领域服务内部使用):**
    *   定义各状态 (`Trialing`, `Active`, `PaymentFailed`, `Canceled`, `Expired`) 间的有效转换规则。
    *   提供`CanTransition(fromStatus, toStatus)`和`Transition(subscription, newStatus)`方法。
    *   `Transition`方法应更新订阅状态并发布相应的领域事件 (e.g., `SubscriptionStatusChangedEvent`).

**基础设施层 (`Whimlab.AI.Agent.Platform.Infrastructure.Data` & `Whimlab.AI.Agent.Platform.Infrastructure.Caching`):**

*   **`ISubscriptionPlanRepository`, `ICustomerSubscriptionRepository` 实现 (PostgreSQL).**
*   **Redis:** 用于`QuotaManagementService`的实时token计数。键例如：`tokens_used:<subscription_id>:<cycle_start_date>`. 使用`INCRBY`原子增加。
*   **`IHostedService` (`DailySubscriptionTasksService`):**
    *   每日运行:
        *   查询即将到期/已到期的试用订阅。对于已到期的，调用`customerSubscription.Expire()`并保存，发布`TrialExpiredEvent`。
        *   查询即将需要续订的`Active`且`AutoRenews=true`的订阅。为每个此类订阅发布一个`ProcessSubscriptionRenewalCommand` (该命令会触发支付流程)。

**API层 (`Whimlab.AI.Agent.Platform.Web.Api/Controllers/V1`):**
*   `AdminSubscriptionPlansController`: `[HttpGet]`, `[HttpPost]`, `[HttpPut]`, `[HttpDelete]`.
*   `CustomerSubscriptionsController`:
    *   `[HttpGet("plans")]` -> `ListPublicPlansQuery`.
    *   `[HttpGet("mine")] [Authorize]` -> `GetMyCurrentSubscriptionQuery`.
    *   `[HttpPost("subscribe")] [Authorize]` -> `ChoosePlanAndInitiatePaymentCommand`.
    *   `[HttpPost("cancel")] [Authorize]` -> `CancelSubscriptionCommand`.

**管理员UI (`Whimlab.AI.Agent.Platform.Client.Admin`):**
*   **Subscription Plans Page:** `MudDataGrid`显示套餐，操作：Create, Edit, Toggle Visibility, Delete.
*   **Create/Edit Plan Form:** `MudTextField` (Name, Description), `MudNumericField` (Price, TokenQuota), `MudSelect` (BillingCycle), `MudSwitch` (IsActive, IsPubliclyVisible), `MudTextField` (Features - 简单文本输入或更复杂的组件)。
*   **Customer Subscriptions Page:** `MudDataGrid`显示客户订阅，过滤：Status, Plan.

**客户UI (`Whimlab.AI.Agent.Platform.Client.Customer`):**
*   **Pricing Page:** `MudGrid` + `MudCard`显示公开的`SubscriptionPlanDto`。包含“选择此套餐”按钮。
*   **My Subscription Page:** 显示`CustomerSubscriptionDto`信息。若`Status`为`Trialing`，显示试用期剩余天数和token。若为`Active`，显示当前周期token用量和结束日期。提供“取消订阅”按钮 (若`AutoRenews`为true)。
*   **Checkout Flow:** 点击“选择此套餐”后，导航到支付页面，选择支付方式，然后调用API发起支付，并处理支付网关的响应 (如显示二维码或重定向)。

---

### **模块5: 支付管理 - 详细实现指令**

**领域层 (`Whimlab.AI.Agent.Platform.Domain.Payment`):**

*   **聚合 `Payment`:** `PaymentId Id`, `CustomerUserId CustomerId`, `SubscriptionId? AssociatedSubscriptionId`, `PlanId? AssociatedPlanId`, `decimal Amount`, `string Currency`, `PaymentGatewayType Gateway` (枚举: `Alipay`, `WeChatPay`), `PaymentStatus Status` (枚举: `PendingInitiation`, `PendingGatewayResponse`, `Succeeded`, `Failed`, `RequiresAction`, `Canceled`, `Refunded`, `PartiallyRefunded`), `string GatewayTransactionId` (可选), `string PaymentIntentId` (可选, e.g. Stripe), `Dictionary<string, string> GatewayMetadata` (JSONB), `DateTime CreatedAt`, `DateTime LastUpdatedAt`.
*   **实体 `Refund`:** `RefundId Id`, `PaymentId OriginalPaymentId`, `decimal Amount`, `string Currency`, `string Reason`, `RefundStatus Status` (枚举: `Pending`, `Succeeded`, `Failed`), `string GatewayRefundId` (可选), `DateTime CreatedAt`.
*   **仓储接口:** `IPaymentRepository`.
*   **领域事件:** `PaymentInitiatedEvent(PaymentId, CustomerId, Amount, Gateway)`, `PaymentSucceededEvent(PaymentId, GatewayTransactionId, Amount)`, `PaymentFailedEvent(PaymentId, FailureReason)`, `RefundInitiatedEvent`, `RefundCompletedEvent`.

**应用层 (`Whimlab.AI.Agent.Platform.Application.Payment`):**

*   **命令:**
    *   `InitiateSubscriptionPaymentCommand(CustomerUserId customerId, SubscriptionId subscriptionId, PlanId planId, decimal amount, string currency, PaymentGatewayType gatewayType)` -> `PaymentInitiationResponseDto` (包含如支付URL、二维码数据、或客户端SDK所需参数).
    *   `HandlePaymentWebhookCommand(string gatewayName, string rawWebhookPayload, Dictionary<string, string> webhookHeaders)` -> (void, 内部处理支付状态更新和事件发布).
    *   `RequestRefundCommand(PaymentId paymentId, decimal? amountToRefund, string reason, AdminUserId requestedBy)` -> `RefundDto`.
*   **查询:** `GetPaymentDetailsQuery(PaymentId id)` -> `PaymentDto`, `ListCustomerPaymentsQuery(CustomerId, Page, PageSize)` -> `PagedResult<PaymentDto>`.
*   **DTOs:** `PaymentDto`, `RefundDto`, `PaymentInitiationResponseDto`.
*   **`IPaymentGateway` 接口:**
    *   `Task<PaymentInitiationResponseDto> CreatePaymentIntentAsync(decimal amount, string currency, string description, Dictionary<string, string> metadata)`
    *   `PaymentWebhookValidationResult ValidateWebhook(string rawPayload, Dictionary<string, string> headers)` (返回包含如EventType, PaymentId, Status的解析结果或错误).
    *   `Task<RefundGatewayResponseDto> ProcessRefundAsync(string gatewayPaymentId, decimal amount, string currency, string reason)`
*   **`PaymentService` (应用服务):**
    *   `InitiateSubscriptionPaymentAsync(InitiateSubscriptionPaymentCommand cmd)`:
        1.  验证。加载Plan和Customer。
        2.  创建`Payment`聚合，状态`PendingInitiation`。保存。
        3.  获取`IPaymentGateway`实例 (通过`PaymentGatewayFactory`).
        4.  调用`gateway.CreatePaymentIntentAsync()`.
        5.  更新`Payment`聚合状态为`PendingGatewayResponse`，保存`PaymentIntentId`等。保存。
        6.  发布`PaymentInitiatedEvent`.
        7.  返回`PaymentInitiationResponseDto`.
    *   `HandlePaymentWebhookAsync(HandlePaymentWebhookCommand cmd)`:
        1.  获取`IPaymentGateway`实例。
        2.  调用`gateway.ValidateWebhook()`验证签名和解析payload。若无效，记录错误并返回。
        3.  根据Webhook中的`PaymentId`或`GatewayTransactionId`加载`Payment`聚合。
        4.  若支付成功: 更新`Payment.Status`为`Succeeded`, 保存`GatewayTransactionId`. 发布`PaymentSucceededEvent` (此事件会触发订阅激活)。
        5.  若支付失败: 更新`Payment.Status`为`Failed`. 发布`PaymentFailedEvent`.
        6.  保存`Payment`聚合。记录审计。
        7.  **幂等性处理**: 检查Webhook事件ID是否已处理过 (存储已处理的Webhook事件ID到Redis或DB)。
*   **`PaymentGatewayFactory(IServiceProvider serviceProvider)`:** 根据`PaymentGatewayType`从DI容器解析对应的`IPaymentGateway`实现。

**基础设施层 (`Whimlab.AI.Agent.Platform.Infrastructure.Payment`):**

*   **`AlipayGateway : IPaymentGateway` 实现:**
    *   使用支付宝SDK或直接HTTP调用。
    *   `CreatePaymentIntentAsync`: 调用支付宝统一下单接口 (PC支付、扫码支付、App支付等)，返回支付URL或二维码内容。
    *   `ValidateWebhook`: 验证支付宝异步通知的签名 (`AlipaySignature.RSACheckV1/V2`)。解析通知参数 (`trade_status`, `out_trade_no`, `trade_no`).
    *   `ProcessRefundAsync`: 调用支付宝退款接口。
*   **`WeChatPayGateway : IPaymentGateway` 实现:**
    *   使用微信支付SDK或直接HTTP调用。
    *   `CreatePaymentIntentAsync`: 调用微信支付统一下单接口 (Native, JSAPI, H5等)，返回CodeURL或JSAPI参数。
    *   `ValidateWebhook`: 验证微信支付回调的签名。解密通知内容 (若有加密)。解析XML/JSON参数 (`result_code`, `out_trade_no`, `transaction_id`).
    *   `ProcessRefundAsync`: 调用微信支付退款接口。
*   **`IPaymentRepository` 实现 (PostgreSQL).**
*   **安全:** API密钥等敏感配置通过`IOptionsMonitor`从安全配置源加载。

**API层 (`Whimlab.AI.Agent.Platform.Web.Api/Controllers/V1`):**
*   `PaymentsController`:
    *   `[HttpPost("initiate-subscription-payment")] [Authorize]` -> `InitiateSubscriptionPaymentCommand`.
*   `WebhooksController`:
    *   `[HttpPost("alipay/notify")]`: 接收支付宝异步通知。调用`HandlePaymentWebhookCommand("Alipay", payload, headers)`.
    *   `[HttpPost("wechatpay/notify")]`: 接收微信支付异步通知。调用`HandlePaymentWebhookCommand("WeChatPay", payload, headers)`.
    *   这些端点*不能*有JWT授权，但必须严格执行网关签名验证。

**客户UI (`Whimlab.AI.Agent.Platform.Client.Customer`):**
*   **Checkout Page:**
    *   当用户选择支付方式并确认后，调用后端的`initiate-subscription-payment` API。
    *   根据API返回的`PaymentInitiationResponseDto`:
        *   若为支付宝PC/H5，则重定向到支付URL。
        *   若为支付宝/微信扫码，则使用QR Code库 (如QRCoder) 生成二维码并显示在`MudDialog`或页面上。
        *   若为微信JSAPI，则调用微信JSBridge。
    *   支付页面应轮询后端检查支付状态，或通过SignalR从服务器接收支付成功/失败的实时通知，然后导航到成功或失败页面。

**管理员UI (`Whimlab.AI.Agent.Platform.Client.Admin`):**
*   **Payments List Page:** `MudDataGrid`显示支付记录，支持按客户、状态、网关、日期范围过滤。
*   **Payment Details Page:** 显示支付详情，提供退款操作按钮 (若支付状态为Succeeded)。退款时弹出`MudDialog`输入退款金额和原因。

---

### **模块6: 分析与报告 (基础版) - 详细实现指令**

**领域层 (`Whimlab.AI.Agent.Platform.Domain.Analytics`):**
*   （基础版）定义只读模型/DTO，不创建新聚合。
*   `PlatformUsageDailySnapshot` (ReadModel): `Date`, `ActiveCustomers`, `NewCustomers`, `TotalAgentsCreated`, `ConversationsStarted`, `TokensConsumed`.
*   `AgentUsageDailySummary` (ReadModel): `Date`, `AgentId`, `AgentName`, `Executions`, `TokensConsumed`, `AverageRating`.

**应用层 (`Whimlab.AI.Agent.Platform.Application.Analytics`):**
*   **查询 (Admin):**
    *   `GetDashboardStatsQuery(DateRange range)` -> `DashboardStatsDto` (包含关键指标，如当前活跃用户数、本月新增用户、总智能体数、今日对话数、今日Token消耗)。
    *   `GetAgentPerformanceReportQuery(AgentId? agentId, DateRange range, ReportGranularity granularity)` -> `List<AgentPerformanceDataPointDto>` (granularity: Daily, Hourly).
    *   `GetCustomerEngagementReportQuery(CustomerId? customerId, DateRange range)` -> `CustomerEngagementDto`.
*   **DTOs:** `DashboardStatsDto`, `AgentPerformanceDataPointDto` (Timestamp, Executions, Tokens, AvgRating), `CustomerEngagementDto`.
*   **`ReportingService` (应用服务):**
    *   此类包含上述查询的处理器逻辑。
    *   直接使用只读的DbContext (`IAgentReadDbContext`) 或注入多个仓储接口来获取数据。
    *   使用Dapper或EF Core的原始SQL/FromSql执行复杂的聚合查询以生成报告数据。
    *   例如，`GetDashboardStatsQueryHandler`会查询Users, Subscriptions, Agents, Conversations, TokenUsageLog (若有) 表。

**基础设施层 (`Whimlab.AI.Agent.Platform.Infrastructure.Data`):**
*   **`IAnalyticsReadRepository` (或直接在`ReportingService`中实现查询逻辑):**
    *   实现针对PostgreSQL的优化只读查询。
    *   例如，`GetTotalTokensConsumedAsync(DateRange range)`: `SELECT SUM(TokenCount) FROM ConversationMessages WHERE Timestamp BETWEEN @Start AND @End`.
    *   为常用的报告查询创建数据库视图 (Views) 可能有助于性能和简化查询。
*   **(未来/高级) `RealTimeAnalyticsService` 实现:**
    *   使用Redis计数器 (`INCRBY`, `HINCRBY`) 记录实时事件 (如智能体执行、用户登录)。
    *   SignalR Hub (`AnalyticsHub`) 将这些计数器数据推送到管理员仪表盘。
    *   后台任务定期将Redis中的计数器快照持久化到PostgreSQL或Elasticsearch。
*   **(未来/高级) `ReportGenerationService` 实现:**
    *   支持从多个数据源 (PostgreSQL, MongoDB, Elasticsearch) 获取数据。
    *   使用模板引擎 (如RazorEngine, Scriban) 渲染报表 (HTML, CSV, PDF - PDF需额外库如QuestPDF)。
    *   将生成的报表存储到MinIO。
    *   支持计划报表 (通过Hangfire或Quartz.NET)。

**API层 (`Whimlab.AI.Agent.Platform.Web.Api/Controllers/V1/AdminAnalyticsController`):**
*   `[HttpGet("dashboard")]` -> `GetDashboardStatsQuery`
*   `[HttpGet("agent-performance")]` -> `GetAgentPerformanceReportQuery`
*   `[HttpGet("customer-engagement")]` -> `GetCustomerEngagementReportQuery`

**管理员UI (`Whimlab.AI.Agent.Platform.Client.Admin`):**
*   **Dashboard Page:**
    *   使用 `MudCard` 显示关键指标 (`DashboardStatsDto`).
    *   使用 `MudChart` (Bar, Line, Donut) 可视化数据，例如：
        *   过去30天每日新增用户 (Line chart).
        *   各类型智能体数量分布 (Donut chart).
        *   热门智能体按执行次数 (Bar chart).
    *   日期范围选择器 (`MudDateRangePicker`).
*   **Reports Page:** 提供不同报告的入口，允许选择参数（如Agent, Customer, DateRange）并显示结果 (可能是`MudTable`或图表)。

---

### **模块7: 通知 (基础版) - 详细实现指令**

**领域层 (`Whimlab.AI.Agent.Platform.Domain.Notification`):**
*   **实体 `Notification`:** `NotificationId Id`, `UserId RecipientUserId` (可以是CustomerUserId或AdminUserId), `UserType RecipientUserType` (枚举), `string Title`, `string MessageBody`, `NotificationChannelType Channel` (枚举: `Email`, `InApp`), `NotificationSeverity Severity` (枚举: `Info`, `Warning`, `Error`), `NotificationStatus Status` (枚举: `PendingDispatch`, `Sent`, `Delivered_InApp` (若有确认), `Read_InApp`, `DispatchFailed`), `DateTime CreatedAt`, `DateTime? SentAt`, `DateTime? ReadAt`, `string? FailureReason`.
*   **仓储接口:** `INotificationRepository`.
*   **领域事件:** `NotificationPreparedEvent(NotificationId, RecipientUserId, Channel, Title, Message)`.

**应用层 (`Whimlab.AI.Agent.Platform.Application.Notification`):**
*   **命令:**
    *   `CreateNotificationCommand(UserId recipientId, UserType recipientType, string title, string message, NotificationChannelType channel, NotificationSeverity severity)` -> `NotificationDto`. (此命令仅创建通知记录，实际发送由事件处理器或后台任务处理)。
    *   `MarkInAppNotificationAsReadCommand(NotificationId notificationId, UserId userId)`.
    *   `MarkAllInAppNotificationsAsReadCommand(UserId userId)`.
*   **查询:**
    *   `GetUserInAppNotificationsQuery(UserId userId, UserType userType, bool unreadOnly, PageNumber, PageSize)` -> `PagedResult<NotificationDto>`.
*   **DTOs:** `NotificationDto` (包含所有`Notification`实体字段).
*   **应用层事件处理器:**
    *   **`HandleCustomerRegisteredEvent`**: `dispatch CreateNotificationCommand` (Welcome Email).
    *   **`HandleTrialExpiringSoonEvent`** (自定义事件，由后台任务发布): `dispatch CreateNotificationCommand` (Trial expiring Email & InApp).
    *   **`HandleSubscriptionPaymentSucceededEvent`**: `dispatch CreateNotificationCommand` (Subscription active Email & InApp).
    *   **`HandleSubscriptionPaymentFailedEvent`**: `dispatch CreateNotificationCommand` (Payment failed Email & InApp).
    *   **`HandleQuotaThresholdReachedEvent`**: `dispatch CreateNotificationCommand` (Quota warning Email & InApp).
*   **`NotificationDispatchService` (应用服务，可由`NotificationPreparedEvent`的处理器调用或由后台任务轮询`PendingDispatch`的通知):**
    *   `Task DispatchNotificationAsync(NotificationId notificationId)`:
        1.  加载`Notification`.
        2.  若Channel为Email, 调用`IEmailService.SendEmailAsync()`.
        3.  若Channel为InApp且需要实时推送, 调用`IInAppNotificationRelay.PushNotificationAsync()` (此服务内部使用SignalR).
        4.  更新`Notification.Status`为`Sent`或`DispatchFailed`. 保存。

**基础设施层 (`Whimlab.AI.Agent.Platform.Infrastructure.Notifications` & `Whimlab.AI.Agent.Platform.Infrastructure.Data`):**
*   **`INotificationRepository` 实现 (PostgreSQL).**
*   **`IEmailService` 实现:**
    *   使用MailKit库通过SMTP发送邮件。配置SMTP服务器、端口、用户名、密码从`appsettings.json`.
    *   邮件模板: 使用简单的字符串替换或更高级的模板引擎 (如Scriban) 加载HTML邮件模板。
*   **`IInAppNotificationRelay` 实现 (若需要实时推送):**
    *   注入`IHubContext<NotificationHub, INotificationClient>`.
    *   `PushNotificationAsync(UserId recipientId, NotificationDto notification)`: 调用`_hubContext.Clients.User(recipientId.ToString()).ReceiveInAppNotification(notification)`.

**API层 (`Whimlab.AI.Agent.Platform.Web.Api/Controllers/V1/NotificationsController`):**
*   `[HttpGet("in-app")] [Authorize]` -> `GetUserInAppNotificationsQuery`.
*   `[HttpPost("in-app/{notificationId}/mark-as-read")] [Authorize]` -> `MarkInAppNotificationAsReadCommand`.
*   `[HttpPost("in-app/mark-all-as-read")] [Authorize]` -> `MarkAllInAppNotificationsAsReadCommand`.

**客户UI & 管理员UI (`Whimlab.AI.Agent.Platform.Client.Customer` & `Whimlab.AI.Agent.Platform.Client.Admin`):**
*   **`NotificationBell.razor` 组件 (放在`MainLayout`的`MudAppBar`中):**
    *   `MudMenu`包裹`MudIconButton` (bell icon) 和 `MudBadge Content="@unreadCount"`.
    *   `OnInitializedAsync`: 调用API获取未读通知数和最近5条未读通知 (`GetUserInAppNotificationsQuery`).
    *   Fluxor State `NotificationState` (UnreadCount, RecentNotifications). Action/Effect轮询或SignalR更新。
    *   菜单项显示最近通知，点击导航到完整通知页面。
*   **`NotificationsPage.razor`:**
    *   `MudTabs`区分“全部”和“未读”。
    *   `MudList`或`MudTable`显示`NotificationDto`列表，支持分页。
    *   提供“全部标记为已读”按钮。
    *   点击单个通知可标记为已读。
*   **SignalR Client Logic (若实现实时推送):**
    *   在`MainLayout`或全局服务中初始化`NotificationHub`连接。
    *   `hubConnection.On<NotificationDto>("ReceiveInAppNotification", (notification) => { ... update Fluxor state, show snackbar ... });`

---

## **X. 跨领域关注点实现细节 (最终整合)**

1.  **MediatR管道行为 (`Whimlab.AI.Agent.Platform.Application.Core.Common.Behaviors`):**
    *   **`ValidationBehavior<TRequest, TResponse>(IEnumerable<IValidator<TRequest>> validators)`:**
        *   若`validators`不为空，则对`request`执行所有验证器的`ValidateAsync`。
        *   若有验证失败，收集所有`ValidationFailure`并抛出`FluentValidation.ValidationException`.
        *   否则调用`next()`.
    *   **`LoggingBehavior<TRequest, TResponse>(ILogger<TRequest> logger, ICurrentUserService currentUserService)`:**
        *   记录请求开始：类型、(可选)序列化后的请求内容、当前用户ID。
        *   调用`next()`.
        *   记录请求结束：执行时间、(可选)成功/失败状态。
    *   **`PerformanceBehavior<TRequest, TResponse>(ILogger<TRequest> logger, IPerformanceMonitor performanceMonitor)`:** (若`IPerformanceMonitor`已实现)
        *   `Stopwatch sw = Stopwatch.StartNew();`
        *   `var response = await next();`
        *   `sw.Stop();`
        *   `_performanceMonitor.RecordRequestPerformance(typeof(TRequest).Name, sw.ElapsedMilliseconds, ...);` (或类似方法)
        *   若执行时间超阈值，`logger.LogWarning(...)`.
    *   **`TransactionBehavior<TRequest, TResponse>(IUnitOfWork unitOfWork, ILogger<TRequest> logger)`:** (仅用于修改数据的Command)
        *   检查`TRequest`是否标记了`[TransactionalCommand]`特性或实现了`ITransactionalRequest`接口。
        *   若需要事务：`await unitOfWork.BeginTransactionAsync(); try { var response = await next(); await unitOfWork.CommitTransactionAsync(); return response; } catch { await unitOfWork.RollbackTransactionAsync(); throw; }`.
        *   若不需要事务：`await next()`.
    *   **`AuthorizationBehavior<TRequest, TResponse>(IAuthorizationService authorizationService, ICurrentUserService currentUserService, IHttpContextAccessor httpContextAccessor)`:**
        *   从`TRequest`或其处理程序上查找`[Authorize]`相关的特性或`IAuthorizationRequirementProvider`接口。
        *   若需要授权，构建`AuthorizationPolicy`或获取`IAuthorizationRequirement`。
        *   调用`_authorizationService.AuthorizeAsync(httpContextAccessor.HttpContext.User, resource, requirementOrPolicy)`.
        *   若授权失败，抛出`UnauthorizedAccessException`.
    *   **`CachingBehavior<TRequest, TResponse>(ICacheService cacheService, ILogger<TRequest> logger)`:** (仅用于Query)
        *   检查`TRequest`是否标记了`[CacheableQuery(DurationMinutes=N)]`特性或实现了`ICacheableQuery<TResponse>`.
        *   若可缓存：生成缓存键 (基于`TRequest`类型和其属性值)。
        *   尝试从`cacheService.GetAsync<TResponse>(cacheKey)`. 若命中，返回缓存结果。
        *   若未命中：`var response = await next(); cacheService.SetAsync(cacheKey, response, TimeSpan.FromMinutes(N)); return response;`

2.  **`EnterpriseDistributedCacheService` (`Whimlab.AI.Agent.Platform.Infrastructure.Caching.Services`):**
    *   构造函数注入`IMemoryCache l1Cache`, `IDistributedCache l2Cache` (Redis的`IDistributedCache`实现), `IRedisDatabase` (StackExchange.Redis的`IDatabase`), `ICachePartitionManager` (可选高级), `ICacheConsistencyManager` (可选高级), `IMetricsCollector`, `ILogger`, `IOptions<CacheConfiguration>`.
    *   `GetAsync<T>(key)`: 尝试L1 -> 尝试L2 -> 若L2命中则填充L1 -> 返回。记录命中/未命中指标。
    *   `GetOrSetAsync<T>(key, factory, expiration)`: 先`GetAsync`. 若未命中，使用`RedisDistributedLock`防止缓存雪崩，双重检查锁，调用`factory`，结果存入L2和L1。
    *   `SetAsync<T>(key, value, expiration)`: 存L2 -> 存L1。发布L1失效通知给其他实例 (若`ICacheConsistencyManager`实现)。
    *   `RemoveAsync(key)`: 删L1 -> 删L2。发布L1失效通知。
    *   `InvalidateByPatternAsync(pattern)`: 使用Redis `SCAN`命令获取匹配键，然后批量`RemoveAsync`.
    *   `InvalidateByTagsAsync(tags)`: (高级) 若缓存项带标签，从标签索引中获取键，然后批量`RemoveAsync`.
    *   **`RedisDistributedLock` (`Whimlab.AI.Agent.Platform.Infrastructure.Caching.Redis`):** 使用StackExchange.Redis的`StringSetAsync(key, value, expiry, When.NotExists)`获取锁，`ScriptEvaluateAsync` (Lua脚本) 安全释放锁。

3.  **`EnterpriseMessageQueueManager` (RabbitMQ部分 - `Whimlab.AI.Agent.Platform.Infrastructure.Messaging.RabbitMQ`):**
    *   `RabbitMQConnectionManager`: 管理到RabbitMQ的连接和通道，支持自动恢复、心跳、连接池。
    *   `PublishAsync<T>(message, options)`:
        1.  获取路由信息 (`MessageRoutingInfo`: Exchange, RoutingKey, IsPersistent, Priority, Expiration).
        2.  创建`MessageEnvelope<T>` (包含MessageId, CorrelationId, Payload, Timestamp, Headers).
        3.  序列化为JSON (UTF-8 bytes).
        4.  获取RabbitMQ `IModel` (channel).
        5.  创建`IBasicProperties` (Persistent, MessageId, Timestamp, ContentType, Headers, Priority, Expiration).
        6.  调用`channel.BasicPublish()`.
        7.  记录发布指标。
    *   `CreateConsumerAsync<T>(queueName, handler, options)`:
        1.  获取RabbitMQ `IModel`.
        2.  配置QoS (`channel.BasicQos(prefetchCount)`).
        3.  创建`AsyncEventingBasicConsumer`.
        4.  在`consumer.Received`事件中:
            *   反序列化`MessageEnvelope<T>`.
            *   创建`MessageContext` (含DeliveryTag, RetryCount).
            *   **调用 `IdempotentMessageProcessor.ProcessAsync(message.Payload, context)`**.
            *   根据`MessageProcessingResult`:
                *   Success: `channel.BasicAck()`.
                *   Retry (且未达最大次数): 调用`_retryService.ScheduleRetryAsync()` (可能将消息重新发布到延迟队列或使用RabbitMQ延迟插件)，然后`channel.BasicAck()`.
                *   Reject/Retry (已达最大次数): 调用`_deadLetterService.SendToDeadLetterAsync()` (发布到DLQ)，然后`channel.BasicAck()`.
            *   记录处理指标。
            *   处理未捕获异常：Nack并考虑移入DLQ。
        5.  调用`channel.BasicConsume()`.
        6.  返回`IMessageConsumer` (用于控制消费者生命周期).
    *   **`MessageDeduplicationService` (`Whimlab.AI.Agent.Platform.Infrastructure.Messaging.Deduplication`):**
        *   `IsDuplicateAsync(messageId, messageType)`: 先查Bloom Filter，再查Redis缓存键 (`msg_dedup:<messageType>:<messageId>`).
        *   `MarkAsProcessedAsync(messageId, messageType, result)`: 添加到Bloom Filter，并写入Redis缓存键（带TTL，值为处理结果）。
        *   `GetPreviousResultAsync(messageId, messageType)`: 从Redis读取上次处理结果。
    *   **`IdempotentMessageProcessor<T>` (装饰器):** 包装实际的`IMessageProcessor<T>`. 在`ProcessAsync`中，先调用`_deduplicationService.IsDuplicateAsync`. 若是，则返回`_deduplicationService.GetPreviousResultAsync`. 否则，调用内部处理器，然后调用`_deduplicationService.MarkAsProcessedAsync`.
    *   **Outbox Pattern (`Whimlab.AI.Agent.Platform.Infrastructure.Messaging.Outbox`):**
        *   `OutboxMessage` 实体 (Id, Type, PayloadJson, Status[Pending, Processing, Processed, Failed], RetryCount, CreatedAt, NextAttemptAt).
        *   `IOutboxMessageRepository`.
        *   `UnitOfWork`修改：在`CommitAsync` *之前*，将业务聚合产生的集成事件序列化并暂存。在主DB事务`_dbContext.SaveChangesAsync()`成功 *之后*，将这些暂存的事件保存到`OutboxMessages`表 (如果使用同一DbContext，此保存也应在同一事务；若不同DbContext，则需确保可靠性)。
        *   `OutboxMessageProcessorService` (`IHostedService`): 定期轮询`OutboxMessages`表中`Status=Pending`或`Status=Processing AND NextAttemptAt <= Now`的记录。为每条记录调用`EnterpriseMessageQueueManager.PublishAsync()`。发布成功则更新记录为`Processed`。失败则增加`RetryCount`，更新`NextAttemptAt` (指数退避)，或达到最大重试次数后标记为`Failed` (并可能移到专用死信表或发告警)。

4.  **监控与可观测性 (`Whimlab.AI.Agent.Platform.Infrastructure.Monitoring`):**
    *   **Serilog:** 在`Program.cs`中配置。
        *   `MinimumLevel.Information()`, Override for Microsoft/System to Warning.
        *   Enrichers: `FromLogContext`, `WithMachineName`, `WithThreadId`, `WithEnvironmentUserName`, `WithProcessId`, `WithProperty("ApplicationName", "Whimlab.AI.Agent.Platform")`. 自定义 enricher for `TraceId`, `SpanId`, `UserId`.
        *   Sinks: `Console` (structured), `File` (rolling, JSON or structured text), `Elasticsearch` (using `Serilog.Sinks.Elasticsearch`,配置
            Elasticsearch URL, index format `ai-platform-logs-{0:yyyy.MM.dd}`).
    *   **OpenTelemetry:** 在`Program.cs`中配置。
        *   `AddOpenTelemetry()`
            *   `.WithTracing(builder => builder.AddAspNetCoreInstrumentation().AddHttpClientInstrumentation().AddEntityFrameworkCoreInstrumentation().AddSource(AgentPlatformActivitySource.Name) /* 自定义ActivitySource */ .SetResourceBuilder(ResourceBuilder.CreateDefault().AddService("Whimlab.AI.Agent.Platform")).AddJaegerExporter() /* 或OtlpExporter */)`
            *   `.WithMetrics(builder => builder.AddAspNetCoreInstrumentation().AddHttpClientInstrumentation().AddRuntimeInstrumentation().AddMeter(AgentPlatformMetrics.Name) /* 自定义Meter */ .SetResourceBuilder(ResourceBuilder.CreateDefault().AddService("Whimlab.AI.Agent.Platform")).AddPrometheusExporter() /* 或OtlpExporter */)`
        *   `AgentPlatformActivitySource` (static class with `ActivitySource Instance`) 用于自定义追踪。
        *   `AgentPlatformMetrics` (static class with `Meter Instance` and specific `Counter<T>`, `Histogram<T>`) 用于自定义指标。
    *   **HealthChecks:** 在`Program.cs`中配置 `AddHealthChecks()`.
        *   `.AddNpgSql(Configuration.GetConnectionString("DefaultConnection"), name: "postgresql-write")`
        *   `.AddNpgSql(Configuration.GetConnectionString("ReadOnlyConnection"), name: "postgresql-read", tags: new[] { "readonly" })` (若使用只读副本)
        *   `.AddRedis(Configuration.GetConnectionString("Redis"), name: "redis")`
        *   `.AddRabbitMQ(Configuration.GetConnectionString("RabbitMQ"), name: "rabbitmq", tags: new[] { "messaging" })` (若使用)
        *   `.AddMongoDb(Configuration.GetConnectionString("MongoDB"), name: "mongodb", tags: new[] { "documentdb" })` (若使用)
        *   自定义健康检查 (implement `IHealthCheck`):
            *   `DifyApiHealthCheck(IDifyApiService difyApi, IOptions<DifyConfiguration> difyOptions)`: 调用Dify的某个简单状态接口。
            *   `SemanticKernelModelHealthCheck(ISemanticKernelService skService, IOptions<SemanticKernelModelsConfiguration> skModelsConfig)`: 检查配置的主要模型是否可访问。
        *   配置UI: `MapHealthChecks("/health", new HealthCheckOptions { ResponseWriter = UIResponseWriter.WriteHealthCheckUIResponse })` 和 `MapHealthChecksUI()`.

## **XI. UI 实现细节 (Blazor Auto Mode & MudBlazor - 最终整合)**

1.  **`MainLayout.razor` (Customer & Admin):**
    *   `<MudThemeProvider @ref="@_mudThemeProvider" @bind-IsDarkMode="@_isDarkMode" />`
    *   `<MudDialogProvider /> <MudSnackbarProvider />`
    *   `<MudLayout>`
        *   `<MudAppBar Elevation="1"> ... <MudToggleDarkMode @bind-IsDarkMode="_isDarkMode" /> <NotificationBell /> </MudAppBar>`
        *   `<MudDrawer @bind-Open="@_drawerOpen" Elevation="1"> <NavMenu /> </MudDrawer>`
        *   `<MudMainContent> @Body </MudMainContent>`
    *   `@code { private bool _isDarkMode; private MudThemeProvider _mudThemeProvider; ... OnAfterRenderAsync to set default theme ...}`
    *   `NotificationBell.razor` 组件放在AppBar中。
2.  **`BaseComponent.cs` (共享或在各自UI项目):**
    ```csharp
    public abstract class BaseComponent : ComponentBase, IDisposable
    {
        [Inject] protected ILogger<BaseComponent> Logger { get; set; } = null!;
        [Inject] protected NavigationManager NavigationManager { get; set; } = null!;
        [Inject] protected ISnackbar Snackbar { get; set; } = null!;
        [Inject] protected IDialogService DialogService { get; set; } = null!;
        [Inject] protected IDispatcher Dispatcher { get; set; } = null!; // For Fluxor

        protected bool IsLoading { get; set; }
        protected string? ErrorMessage { get; set; }
        protected CancellationTokenSource Cts { get; } = new CancellationTokenSource();

        protected override async Task OnInitializedAsync()
        {
            await LoadDataAsyncWrapper();
        }

        protected async Task LoadDataAsyncWrapper(bool showLoadingIndicator = true)
        {
            if (showLoadingIndicator) IsLoading = true;
            ErrorMessage = null;
            try
            {
                await LoadDataAsync(Cts.Token);
            }
            catch (Exception ex) when (ex is not OperationCanceledException)
            {
                Logger.LogError(ex, "Error loading data in {ComponentName}", GetType().Name);
                ErrorMessage = "加载数据时发生错误：" + ex.Message; // 可根据环境显示不同详细程度
                Snackbar.Add(ErrorMessage, Severity.Error);
            }
            finally
            {
                if (showLoadingIndicator) IsLoading = false;
                if (IsLoading && !showLoadingIndicator) IsLoading = false; // Ensure it's turned off
                StateHasChanged();
            }
        }

        protected virtual Task LoadDataAsync(CancellationToken cancellationToken) => Task.CompletedTask;

        protected void ShowSuccess(string message) => Snackbar.Add(message, Severity.Success);
        protected void ShowError(string message) => Snackbar.Add(message, Severity.Error);
        protected void ShowWarning(string message) => Snackbar.Add(message, Severity.Warning);
        protected void ShowInfo(string message) => Snackbar.Add(message, Severity.Info);

        protected async Task<bool> ShowConfirmationDialog(string title, string message, string confirmButtonText = "确认")
        {
            var parameters = new DialogParameters { ["MessageContent"] = message };
            var options = new DialogOptions { CloseButton = true, MaxWidth = MaxWidth.ExtraSmall };
            var dialog = DialogService.Show<ConfirmationDialog>(title, parameters, options);
            var result = await dialog.Result;
            return !result.Canceled;
        }

        public virtual void Dispose()
        {
            Cts.Cancel();
            Cts.Dispose();
            GC.SuppressFinalize(this);
        }
    }
    ```
    *   `ConfirmationDialog.razor`:
    ```razor
    <MudDialog>
        <DialogContent>
            <MudText>@MessageContent</MudText>
        </DialogContent>
        <DialogActions>
            <MudButton OnClick="Cancel">取消</MudButton>
            <MudButton Color="Color.Primary" OnClick="Confirm">@ConfirmButtonText</MudButton>
        </DialogActions>
    </MudDialog>
    @code {
        [CascadingParameter] MudDialogInstance MudDialog { get; set; } = null!;
        [Parameter] public string MessageContent { get; set; } = "";
        [Parameter] public string ConfirmButtonText { get; set; } = "确认";
        void Confirm() => MudDialog.Close(DialogResult.Ok(true));
        void Cancel() => MudDialog.Cancel();
    }
    ```
3.  **Fluxor Store Setup (e.g., in `Program.cs` of Blazor projects):**
    *   `services.AddFluxor(options => options.ScanAssemblies(typeof(Program).Assembly).UseReduxDevTools());`
    *   **Example `AuthState.cs`:**
    ```csharp
    [FeatureState]
    public record AuthState
    {
        public bool IsAuthenticated { get; init; } = false;
        public bool IsAuthenticating { get; init; } = false;
        public string? AccessToken { get; init; }
        public DateTime? AccessTokenExpiration { get; init; }
        public string? RefreshToken { get; init; }
        public UserInfoDto? CurrentUser { get; init; }
        public string? ErrorMessage { get; init; }

        private AuthState() { } // Required for Fluxor
        public AuthState(bool isAuthenticated, bool isAuthenticating, string? accessToken, DateTime? accessTokenExpiration, string? refreshToken, UserInfoDto? currentUser, string? errorMessage)
        {
            IsAuthenticated = isAuthenticated; IsAuthenticating = isAuthenticating; AccessToken = accessToken; AccessTokenExpiration = accessTokenExpiration; RefreshToken = refreshToken; CurrentUser = currentUser; ErrorMessage = errorMessage;
        }
    }
    // UserInfoDto record
    public record UserInfoDto(string Id, string Email, string Name, List<string> Roles, List<string> Permissions);
    // Actions (LoginAction, LoginSuccessAction, LoginFailureAction, LogoutAction, RefreshTokenAction etc.)
    // Reducers (pure functions updating AuthState)
    // Effects (handling API calls for login, logout, refresh; interacting with LocalStorage for tokens)
    ```
4.  **HTTP Client Services (e.g., `AuthServiceHttpClient.cs` in Customer UI):**
    ```csharp
    public class AuthServiceHttpClient
    {
        private readonly HttpClient _httpClient;
        public AuthServiceHttpClient(HttpClient httpClient) => _httpClient = httpClient;

        public async Task<AuthTokenDto?> LoginAsync(LoginCustomerCommand command)
        {
            var response = await _httpClient.PostAsJsonAsync("api/v1/identity/customer/login", command);
            if (response.IsSuccessStatusCode)
                return await response.Content.ReadFromJsonAsync<AuthTokenDto>();
            // Handle error response, throw custom exception or return null/error DTO
            return null; 
        }
        // Other methods for register, refresh token etc.
    }
    // In Program.cs:
    // builder.Services.AddHttpClient<AuthServiceHttpClient>(client => client.BaseAddress = new Uri(builder.HostEnvironment.BaseAddress))
    //    .AddHttpMessageHandler<BaseAddressAuthorizationMessageHandler>(); // If using WASM hosted
    // Or for server-side, just configure BaseAddress and add a JWT handler
    ```
    *   **JWT Interceptor (`JwtHttpInterceptor.cs` - DelegatingHandler):**
        *   Inject `IState<AuthState>` (Fluxor) and `ILocalStorageService`.
        *   Override `SendAsync`. Before sending, check `AuthState` or `LocalStorage` for token. If present and not expired, add `Authorization: Bearer <token>` header.
        *   Handle 401 responses to trigger refresh token logic or logout.

**具体页面的UI实现指令已在各模块的UI部分详细描述，请严格遵循那些使用MudBlazor组件的指导。**

## **XII. DevOps、部署与数据库策略 (最终整合)**

1.  **`Dockerfile` (for `Whimlab.AI.Agent.Platform.Web.Api` - 主API服务，包含Blazor Server部分):**
    ```dockerfile
    # Stage 1: Build
    FROM mcr.microsoft.com/dotnet/sdk:9.0 AS build
    WORKDIR /src
    COPY ["src/01-Presentation/Whimlab.AI.Agent.Platform.Web.Api/Whimlab.AI.Agent.Platform.Web.Api.csproj", "src/01-Presentation/Whimlab.AI.Agent.Platform.Web.Api/"]
    # Copy other csproj files for solution build context
    COPY ["Whimlab.AI.sln", "."]
    COPY ["Directory.Build.props", "."]
    COPY ["Directory.Packages.props", "."]
    COPY ["src/", "src/"] # Copy all src to build all projects
    RUN dotnet restore "Whimlab.AI.sln"
    WORKDIR "/src/src/01-Presentation/Whimlab.AI.Agent.Platform.Web.Api"
    RUN dotnet build "Whimlab.AI.Agent.Platform.Web.Api.csproj" -c Release -o /app/build

    # Stage 2: Publish Blazor WebAssembly assets (if Customer/Admin UIs are separate Blazor WASM or Auto with significant WASM parts)
    # Example for Customer UI if it has substantial WASM assets to be served by the API
    # WORKDIR /src/src/01-Presentation/Whimlab.AI.Agent.Platform.Client.Customer
    # RUN dotnet publish "Whimlab.AI.Agent.Platform.Client.Customer.csproj" -c Release -o /app/build/wwwroot/customer_ui_wasm # Adjust path as needed

    # Stage 3: Publish API
    FROM build AS publish
    WORKDIR "/src/src/01-Presentation/Whimlab.AI.Agent.Platform.Web.Api"
    RUN dotnet publish "Whimlab.AI.Agent.Platform.Web.Api.csproj" -c Release -o /app/publish /p:UseAppHost=false

    # Stage 4: Final runtime image
    FROM mcr.microsoft.com/dotnet/aspnet:9.0 AS final
    WORKDIR /app
    COPY --from=publish /app/publish .
    # If Blazor WASM assets were published separately and need to be served by this API:
    # COPY --from=publish /app/build/wwwroot/customer_ui_wasm ./wwwroot/customer_ui_wasm 
    ENTRYPOINT ["dotnet", "Whimlab.AI.Agent.Platform.Web.Api.dll"]
    ```
    *   **注意:** Blazor Auto模式下，WASM资源通常由服务器端项目提供。如果UI项目是独立部署的（例如，静态站点部署WASM，或有独立的ASP.NET Core宿主进程），则它们需要自己的Dockerfile。当前假设API服务也承载了Blazor Server的部分。

2.  **`docker-compose.yml` (本地开发):**
    ```yaml
    version: '3.8'
    services:
      postgres:
        image: postgres:16
        ports:
          - "5432:5432"
        environment:
          POSTGRES_USER: user
          POSTGRES_PASSWORD: password
          POSTGRES_DB: ai_agent_platform_db
        volumes:
          - postgres_data:/var/lib/postgresql/data
      redis:
        image: redis:7.2
        ports:
          - "6379:6379"
        volumes:
          - redis_data:/data
      rabbitmq: # Optional
        image: rabbitmq:3.12-management
        ports:
          - "5672:5672" # AMQP
          - "15672:15672" # Management UI
        environment:
          RABBITMQ_DEFAULT_USER: user
          RABBITMQ_DEFAULT_PASS: password
        volumes:
          - rabbitmq_data:/var/lib/rabbitmq
      # minio: # Optional for local dev
      #   image: minio/minio
      #   ports:
      #     - "9000:9000" # API
      #     - "9001:9001" # Console
      #   environment:
      #     MINIO_ROOT_USER: minioadmin
      #     MINIO_ROOT_PASSWORD: minioadmin
      #   command: server /data --console-address ":9001"
      #   volumes:
      #     - minio_data:/data
      # elasticsearch: # Optional for local dev
      #   image: docker.elastic.co/elasticsearch/elasticsearch:8.11.0
      #   ports:
      #     - "9200:9200"
      #     - "9300:9300"
      #   environment:
      #     - discovery.type=single-node
      #     - xpack.security.enabled=false # For easy local dev
      #   volumes:
      #     - es_data:/usr/share/elasticsearch/data

      # API Service (assuming it hosts Blazor Server parts for both UIs, or UIs are separate)
      api_service:
        build:
          context: . # Root of your solution where Dockerfile for API is
          dockerfile: src/01-Presentation/Whimlab.AI.Agent.Platform.Web.Api/Dockerfile # Adjust path to API's Dockerfile
        ports:
          - "8080:8080" # Or your app's configured port
          - "8081:8081" # If https is also mapped
        environment:
          ASPNETCORE_ENVIRONMENT: Development
          ASPNETCORE_URLS: http://+:8080;http://+:8081 # Example
          ConnectionStrings__DefaultConnection: "Host=postgres;Port=5432;Database=ai_agent_platform_db;Username=user;Password=password;"
          ConnectionStrings__Redis: "redis:6379"
          ConnectionStrings__RabbitMQ: "amqp://user:password@rabbitmq:5672/"
          # Add other necessary environment variables for API keys, JWT secrets etc.
        depends_on:
          - postgres
          - redis
          - rabbitmq # if used
        volumes:
          - ~/.aspnet/https/aspnetapp.pfx:/https/aspnetapp.pfx:ro # For HTTPS in dev, if needed
          - ./src:/src:ro # Mount source for faster dev iteration (optional, be careful with paths)

    volumes:
      postgres_data:
      redis_data:
      rabbitmq_data:
      # minio_data:
      # es_data:
    ```
3.  **Kubernetes Manifest Stubs (示例于 `tools/deployment/kubernetes/`):**
    *   `api-deployment.yaml`, `api-service.yaml`, `api-ingress.yaml`, `api-hpa.yaml` (严格遵循优化文档第五部分第12.1节的示例结构和配置细节，包括探针、资源限制、HPA指标)。
    *   `configmap.yaml` (for non-sensitive config), `secret.yaml` (for sensitive data, to be populated from a secrets manager).

4.  **Database Schema Management (EF Core Migrations):**
    *   在`Whimlab.AI.Agent.Platform.Infrastructure.Data`项目中启用迁移 (`dotnet ef migrations add InitialCreate -c AgentPlatformDbContext`).
    *   **Data Seeding:** 在`AgentPlatformDbContext.OnModelCreating`中或使用自定义`IDataSeeder`接口和实现类 (在`Program.cs`中调用) 来播种初始数据：
        *   默认权限 (`Permissions`表)。
        *   默认角色 (SuperAdmin, Admin, Customer) 及其关联权限 (`Roles`, `RolePermissions`表)。
        *   一个初始的SuperAdmin用户 (`AdminUsers`表)。
        *   几个示例订阅套餐 (`SubscriptionPlans`表)。

5.  **CI/CD - GitHub Actions (`.github/workflows/main.yml`):**
    ```yaml
    name: .NET CI/CD

    on:
      push:
        branches: [ "main", "develop" ]
      pull_request:
        branches: [ "main", "develop" ]

    jobs:
      build_and_test:
        runs-on: ubuntu-latest
        services: # For integration tests
          postgres:
            image: postgres:16
            env:
              POSTGRES_USER: testuser
              POSTGRES_PASSWORD: testpassword
              POSTGRES_DB: test_db
            ports:
              - 5432:5432
            options: >-
              --health-cmd pg_isready
              --health-interval 10s
              --health-timeout 5s
              --health-retries 5
          redis:
            image: redis:7.2
            ports:
              - 6379:6379
            options: >-
              --health-cmd "redis-cli ping"
              --health-interval 10s
              --health-timeout 5s
              --health-retries 5

        steps:
        - uses: actions/checkout@v4
        - name: Setup .NET
          uses: actions/setup-dotnet@v4
          with:
            dotnet-version: '9.0.x' # Match your global.json
            source-url: https://api.nuget.org/v3/index.json # If you have private feeds, configure nuget.config
          env:
            NUGET_AUTH_TOKEN: ${{secrets.GITHUB_TOKEN}}


        - name: Restore dependencies
          run: dotnet restore Whimlab.AI.sln

        - name: Build
          run: dotnet build Whimlab.AI.sln --configuration Release --no-restore

        - name: Run Unit Tests
          run: dotnet test Whimlab.AI.sln --configuration Release --no-build --filter "Category=Unit" --logger "trx;LogFileName=unit_test_results.trx"
          # Assuming test projects are categorized, e.g. [TestCategory("Unit")]

        - name: Run Integration Tests
          run: dotnet test Whimlab.AI.sln --configuration Release --no-build --filter "Category=Integration" --logger "trx;LogFileName=integration_test_results.trx"
          env: # Provide connection strings for integration tests
            ConnectionStrings__DefaultConnection: "Host=localhost;Port=${{ job.services.postgres.ports }};Database=test_db;Username=testuser;Password=testpassword;"
            ConnectionStrings__Redis: "localhost:${{ job.services.redis.ports }}"
            # Other env vars for tests

        # - name: Publish Test Results (Optional)
        #   uses: actions/upload-artifact@v3
        #   if: always() # always() ensures that this step runs even if prior steps fail
        #   with:
        #     name: test-results
        #     path: '**/*.trx'

        # (Future steps for Docker build, push, deploy)
    ```

## **XIII. 对AI的最终指示 (再次重申和总结)**

1.  **模块化、分层实现:** 严格按照定义的模块和层级生成代码。每个模块的功能必须在所有相关层中完整实现。
2.  **遵循优化文档细节:** 当提示中提到参考“优化文档”中的某个类、模式或实现流程时（例如 `EnterpriseSemanticKernelService`的详细结构, `CreateAgentCommandHandler`的步骤, `AgentRepository`的缓存和查询逻辑, MediatR管道行为, `EnterpriseDistributedCacheService`的L1/L2实现, `EnterpriseMessageQueueManager`的RabbitMQ和Outbox逻辑, 监控组件的配置），**你必须直接实现这些文档中描述的详细功能、属性、方法签名和内部逻辑，而不是仅仅创建一个骨架或简化版本。** 假定这些优化文档中的代码片段和设计思想是硬性要求。
3.  **领域模型驱动:** 聚合、实体、值对象的行为和验证逻辑必须在领域层实现。应用层编排领域逻辑，基础设施层提供实现。
4.  **CQRS 和 MediatR:** 所有应用层操作通过Command/Query和对应的Handler实现。广泛使用MediatR管道行为处理横切关注点。
5.  **错误处理和验证:** 所有API端点和应用服务方法必须有健壮的输入验证 (FluentValidation) 和全面的异常处理。返回标准化的错误响应。
6.  **异步编程:** 所有I/O密集型操作（数据库、网络调用、文件系统）必须使用`async/await`。
7.  **依赖注入:** 严格使用依赖注入。
8.  **安全性:** 认证、授权、输入清理、数据加密、防范常见Web漏洞。
9.  **可测试性:** 生成的代码应易于单元测试和集成测试。
10. **代码质量:** 清洁、可读、可维护，遵循C#最佳实践和命名约定。充分使用XML文档注释。
11. **配置驱动:** 所有环境相关的设置、API密钥、特性开关等都应通过`appsettings.json`和`IOptions`模式进行配置，并支持环境变量覆盖。

**请从解决方案设置、项目结构开始，然后是模块1：身份与访问管理 (IAM)，完全整合本提示词中所有模块的详细实现指令和引用的优化文档中的具体设计。在IAM及其相关测试完成后，再继续模块2：AI智能体管理，并同样包含所有详细实现指令。请一步一步，模块化地进行。**

