using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Whimlab.AI.Agent.Platform.Domain.Identity.Entities;

namespace Whimlab.AI.Agent.Platform.Infrastructure.Configurations;

/// <summary>
/// 用户实体配置
/// </summary>
public class UserConfiguration : IEntityTypeConfiguration<User>
{
    /// <summary>
    /// 配置用户实体
    /// </summary>
    /// <param name="builder">实体类型构建器</param>
    public void Configure(EntityTypeBuilder<User> builder)
    {
        // 表名
        builder.ToTable("Users", "Identity");

        // 主键
        builder.HasKey(u => u.Id);

        // 属性配置
        builder.Property(u => u.Id)
            .IsRequired()
            .HasComment("用户ID");

        builder.Property(u => u.Username)
            .IsRequired()
            .HasMaxLength(50)
            .HasComment("用户名");

        builder.Property(u => u.Email)
            .IsRequired()
            .HasMaxLength(255)
            .HasComment("邮箱地址");

        builder.Property(u => u.PasswordHash)
            .IsRequired()
            .HasMaxLength(255)
            .HasComment("密码哈希");

        builder.Property(u => u.FirstName)
            .HasMaxLength(50)
            .HasComment("名字");

        builder.Property(u => u.LastName)
            .HasMaxLength(50)
            .HasComment("姓氏");

        builder.Property(u => u.PhoneNumber)
            .HasMaxLength(20)
            .HasComment("电话号码");

        builder.Property(u => u.Avatar)
            .HasMaxLength(500)
            .HasComment("头像URL");

        builder.Property(u => u.IsActive)
            .IsRequired()
            .HasDefaultValue(true)
            .HasComment("是否激活");

        builder.Property(u => u.IsEmailConfirmed)
            .IsRequired()
            .HasDefaultValue(false)
            .HasComment("邮箱是否已确认");

        builder.Property(u => u.IsPhoneConfirmed)
            .IsRequired()
            .HasDefaultValue(false)
            .HasComment("电话是否已确认");

        builder.Property(u => u.LastLoginAt)
            .HasComment("最后登录时间");

        builder.Property(u => u.CreatedAt)
            .IsRequired()
            .HasDefaultValueSql("GETUTCDATE()")
            .HasComment("创建时间");

        builder.Property(u => u.UpdatedAt)
            .HasComment("更新时间");

        // 索引
        builder.HasIndex(u => u.Username)
            .IsUnique()
            .HasDatabaseName("IX_Users_Username");

        builder.HasIndex(u => u.Email)
            .IsUnique()
            .HasDatabaseName("IX_Users_Email");

        builder.HasIndex(u => u.PhoneNumber)
            .HasDatabaseName("IX_Users_PhoneNumber");

        builder.HasIndex(u => u.IsActive)
            .HasDatabaseName("IX_Users_IsActive");

        builder.HasIndex(u => u.CreatedAt)
            .HasDatabaseName("IX_Users_CreatedAt");

        // 关系配置
        builder.HasMany(u => u.UserRoles)
            .WithOne(ur => ur.User)
            .HasForeignKey(ur => ur.UserId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasMany(u => u.UserPermissions)
            .WithOne(up => up.User)
            .HasForeignKey(up => up.UserId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasMany(u => u.ResourcePermissions)
            .WithOne(rp => rp.User)
            .HasForeignKey(rp => rp.UserId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}
