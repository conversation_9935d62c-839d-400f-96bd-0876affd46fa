using Whimlab.AI.Agent.Platform.Domain.Core.Common;
using Whimlab.AI.Agent.Platform.Domain.Subscription.ValueObjects;
using Whimlab.AI.Agent.Platform.Domain.Subscription.Events;
using Whimlab.AI.Agent.Platform.Shared.Common.Enums;

namespace Whimlab.AI.Agent.Platform.Domain.Subscription.Entities;

/// <summary>
/// 订阅计划聚合根
/// </summary>
public class SubscriptionPlan : AggregateRoot
{
    private readonly List<PlanFeature> _features = new();
    private readonly List<PlanQuota> _quotas = new();

    /// <summary>
    /// 计划名称
    /// </summary>
    public string Name { get; private set; } = string.Empty;

    /// <summary>
    /// 计划描述
    /// </summary>
    public string Description { get; private set; } = string.Empty;

    /// <summary>
    /// 计划代码
    /// </summary>
    public string PlanCode { get; private set; } = string.Empty;

    /// <summary>
    /// 月度价格
    /// </summary>
    public Money MonthlyPrice { get; private set; } = null!;

    /// <summary>
    /// 年度价格
    /// </summary>
    public Money YearlyPrice { get; private set; } = null!;

    /// <summary>
    /// 试用天数
    /// </summary>
    public int TrialDays { get; private set; }

    /// <summary>
    /// 是否流行
    /// </summary>
    public bool IsPopular { get; private set; }

    /// <summary>
    /// 是否激活
    /// </summary>
    public bool IsActive { get; private set; }

    /// <summary>
    /// 排序顺序
    /// </summary>
    public int SortOrder { get; private set; }

    /// <summary>
    /// 最大用户数
    /// </summary>
    public int? MaxUsers { get; private set; }

    /// <summary>
    /// 功能列表（只读）
    /// </summary>
    public IReadOnlyList<PlanFeature> Features => _features.AsReadOnly();

    /// <summary>
    /// 配额列表（只读）
    /// </summary>
    public IReadOnlyList<PlanQuota> Quotas => _quotas.AsReadOnly();

    /// <summary>
    /// 私有构造函数（用于EF Core）
    /// </summary>
    private SubscriptionPlan() { }

    /// <summary>
    /// 创建订阅计划
    /// </summary>
    /// <param name="name">计划名称</param>
    /// <param name="description">计划描述</param>
    /// <param name="planCode">计划代码</param>
    /// <param name="monthlyPrice">月度价格</param>
    /// <param name="yearlyPrice">年度价格</param>
    /// <param name="trialDays">试用天数</param>
    /// <param name="maxUsers">最大用户数</param>
    /// <param name="sortOrder">排序顺序</param>
    /// <returns>订阅计划实体</returns>
    public static SubscriptionPlan Create(
        string name,
        string description,
        string planCode,
        Money monthlyPrice,
        Money yearlyPrice,
        int trialDays = 0,
        int? maxUsers = null,
        int sortOrder = 0)
    {
        if (string.IsNullOrWhiteSpace(name))
            throw new ArgumentException("计划名称不能为空", nameof(name));

        if (string.IsNullOrWhiteSpace(description))
            throw new ArgumentException("计划描述不能为空", nameof(description));

        if (string.IsNullOrWhiteSpace(planCode))
            throw new ArgumentException("计划代码不能为空", nameof(planCode));

        if (trialDays < 0)
            throw new ArgumentException("试用天数不能为负数", nameof(trialDays));

        if (maxUsers.HasValue && maxUsers.Value <= 0)
            throw new ArgumentException("最大用户数必须大于0", nameof(maxUsers));

        var plan = new SubscriptionPlan
        {
            Name = name.Trim(),
            Description = description.Trim(),
            PlanCode = planCode.Trim().ToUpperInvariant(),
            MonthlyPrice = monthlyPrice,
            YearlyPrice = yearlyPrice,
            TrialDays = trialDays,
            MaxUsers = maxUsers,
            SortOrder = sortOrder,
            IsActive = true,
            IsPopular = false
        };

        plan.AddDomainEvent(new SubscriptionPlanCreatedEvent(plan.Id, plan.Name, plan.PlanCode));

        return plan;
    }

    /// <summary>
    /// 更新计划信息
    /// </summary>
    /// <param name="name">计划名称</param>
    /// <param name="description">计划描述</param>
    /// <param name="trialDays">试用天数</param>
    /// <param name="maxUsers">最大用户数</param>
    /// <param name="sortOrder">排序顺序</param>
    public void UpdateInfo(string name, string description, int? trialDays = null, int? maxUsers = null, int? sortOrder = null)
    {
        Name = name.Trim();
        Description = description.Trim();
        
        if (trialDays.HasValue)
        {
            if (trialDays.Value < 0)
                throw new ArgumentException("试用天数不能为负数", nameof(trialDays));
            TrialDays = trialDays.Value;
        }

        if (maxUsers.HasValue)
        {
            if (maxUsers.Value <= 0)
                throw new ArgumentException("最大用户数必须大于0", nameof(maxUsers));
            MaxUsers = maxUsers.Value;
        }

        if (sortOrder.HasValue)
            SortOrder = sortOrder.Value;

        MarkAsModified();

        AddDomainEvent(new SubscriptionPlanUpdatedEvent(Id, Name));
    }

    /// <summary>
    /// 更新价格
    /// </summary>
    /// <param name="monthlyPrice">月度价格</param>
    /// <param name="yearlyPrice">年度价格</param>
    public void UpdatePricing(Money monthlyPrice, Money yearlyPrice)
    {
        MonthlyPrice = monthlyPrice;
        YearlyPrice = yearlyPrice;
        MarkAsModified();

        AddDomainEvent(new SubscriptionPlanPriceUpdatedEvent(Id, monthlyPrice, yearlyPrice));
    }

    /// <summary>
    /// 设置为流行计划
    /// </summary>
    public void SetAsPopular()
    {
        IsPopular = true;
        MarkAsModified();
    }

    /// <summary>
    /// 取消流行计划
    /// </summary>
    public void UnsetAsPopular()
    {
        IsPopular = false;
        MarkAsModified();
    }

    /// <summary>
    /// 激活计划
    /// </summary>
    public void Activate()
    {
        if (IsActive) return;

        IsActive = true;
        MarkAsModified();

        AddDomainEvent(new SubscriptionPlanActivatedEvent(Id, Name));
    }

    /// <summary>
    /// 停用计划
    /// </summary>
    public void Deactivate()
    {
        if (!IsActive) return;

        IsActive = false;
        MarkAsModified();

        AddDomainEvent(new SubscriptionPlanDeactivatedEvent(Id, Name));
    }

    /// <summary>
    /// 添加功能
    /// </summary>
    /// <param name="featureName">功能名称</param>
    /// <param name="featureValue">功能值</param>
    /// <param name="description">功能描述</param>
    public void AddFeature(string featureName, string? featureValue = null, string? description = null)
    {
        if (string.IsNullOrWhiteSpace(featureName))
            throw new ArgumentException("功能名称不能为空", nameof(featureName));

        if (_features.Any(f => f.Name.Equals(featureName, StringComparison.OrdinalIgnoreCase)))
            throw new InvalidOperationException($"功能 {featureName} 已存在");

        var feature = PlanFeature.Create(Id, featureName, featureValue, description);
        _features.Add(feature);
        MarkAsModified();
    }

    /// <summary>
    /// 移除功能
    /// </summary>
    /// <param name="featureName">功能名称</param>
    public void RemoveFeature(string featureName)
    {
        var feature = _features.FirstOrDefault(f => f.Name.Equals(featureName, StringComparison.OrdinalIgnoreCase));
        if (feature != null)
        {
            _features.Remove(feature);
            MarkAsModified();
        }
    }

    /// <summary>
    /// 添加配额
    /// </summary>
    /// <param name="quotaType">配额类型</param>
    /// <param name="limit">配额限制</param>
    /// <param name="period">配额周期</param>
    /// <param name="description">配额描述</param>
    public void AddQuota(string quotaType, long limit, string period = "monthly", string? description = null)
    {
        if (string.IsNullOrWhiteSpace(quotaType))
            throw new ArgumentException("配额类型不能为空", nameof(quotaType));

        if (limit < 0)
            throw new ArgumentException("配额限制不能为负数", nameof(limit));

        if (_quotas.Any(q => q.QuotaType.Equals(quotaType, StringComparison.OrdinalIgnoreCase)))
            throw new InvalidOperationException($"配额类型 {quotaType} 已存在");

        var quota = PlanQuota.Create(Id, quotaType, limit, period, description);
        _quotas.Add(quota);
        MarkAsModified();
    }

    /// <summary>
    /// 更新配额
    /// </summary>
    /// <param name="quotaType">配额类型</param>
    /// <param name="limit">新的配额限制</param>
    public void UpdateQuota(string quotaType, long limit)
    {
        var quota = _quotas.FirstOrDefault(q => q.QuotaType.Equals(quotaType, StringComparison.OrdinalIgnoreCase));
        if (quota == null)
            throw new InvalidOperationException($"配额类型 {quotaType} 不存在");

        quota.UpdateLimit(limit);
        MarkAsModified();
    }

    /// <summary>
    /// 移除配额
    /// </summary>
    /// <param name="quotaType">配额类型</param>
    public void RemoveQuota(string quotaType)
    {
        var quota = _quotas.FirstOrDefault(q => q.QuotaType.Equals(quotaType, StringComparison.OrdinalIgnoreCase));
        if (quota != null)
        {
            _quotas.Remove(quota);
            MarkAsModified();
        }
    }

    /// <summary>
    /// 获取指定周期的价格
    /// </summary>
    /// <param name="billingCycle">计费周期</param>
    /// <returns>价格</returns>
    public Money GetPrice(BillingCycle billingCycle)
    {
        return billingCycle switch
        {
            BillingCycle.Monthly => MonthlyPrice,
            BillingCycle.Yearly => YearlyPrice,
            _ => throw new ArgumentException($"不支持的计费周期: {billingCycle}")
        };
    }

    /// <summary>
    /// 获取年度折扣百分比
    /// </summary>
    /// <returns>折扣百分比</returns>
    public decimal GetYearlyDiscountPercentage()
    {
        if (MonthlyPrice.IsZero() || YearlyPrice.IsZero())
            return 0;

        var yearlyEquivalent = MonthlyPrice.Multiply(12);
        var savings = yearlyEquivalent.Subtract(YearlyPrice);
        return (savings.Amount / yearlyEquivalent.Amount) * 100;
    }

    /// <summary>
    /// 检查是否有指定功能
    /// </summary>
    /// <param name="featureName">功能名称</param>
    /// <returns>是否有该功能</returns>
    public bool HasFeature(string featureName)
    {
        return _features.Any(f => f.Name.Equals(featureName, StringComparison.OrdinalIgnoreCase));
    }

    /// <summary>
    /// 获取配额限制
    /// </summary>
    /// <param name="quotaType">配额类型</param>
    /// <returns>配额限制</returns>
    public long? GetQuotaLimit(string quotaType)
    {
        return _quotas.FirstOrDefault(q => q.QuotaType.Equals(quotaType, StringComparison.OrdinalIgnoreCase))?.Limit;
    }
}
