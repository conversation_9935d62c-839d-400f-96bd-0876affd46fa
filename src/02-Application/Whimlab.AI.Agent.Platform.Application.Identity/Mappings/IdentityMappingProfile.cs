using AutoMapper;
using Whimlab.AI.Agent.Platform.Application.Identity.DTOs;
using Whimlab.AI.Agent.Platform.Domain.Identity.Entities;

namespace Whimlab.AI.Agent.Platform.Application.Identity.Mappings;

/// <summary>
/// 身份认证模块映射配置
/// </summary>
public class IdentityMappingProfile : Profile
{
    /// <summary>
    /// 初始化映射配置
    /// </summary>
    public IdentityMappingProfile()
    {
        CreateUserMappings();
        CreateRoleMappings();
        CreatePermissionMappings();
    }

    /// <summary>
    /// 创建用户映射
    /// </summary>
    private void CreateUserMappings()
    {
        // User -> UserDto
        CreateMap<User, UserDto>()
            .ForMember(dest => dest.Email, opt => opt.MapFrom(src => src.Email.Value))
            .ForMember(dest => dest.PhoneNumber, opt => opt.MapFrom(src => src.PhoneNumber != null ? src.PhoneNumber.Value : null))
            .ForMember(dest => dest.Roles, opt => opt.MapFrom(src => new List<RoleDto>())) // 默认空列表，需要在查询时单独处理
            .ForMember(dest => dest.IsEmailVerified, opt => opt.MapFrom(src => src.IsEmailVerified))
            .ForMember(dest => dest.IsPhoneVerified, opt => opt.MapFrom(src => src.IsPhoneVerified))
            .ForMember(dest => dest.IsActive, opt => opt.MapFrom(src => src.IsActive));

        // User -> UserSummaryDto
        CreateMap<User, UserSummaryDto>()
            .ForMember(dest => dest.Email, opt => opt.MapFrom(src => src.Email.Value))
            .ForMember(dest => dest.IsActive, opt => opt.MapFrom(src => src.IsActive));

        // CreateUserDto -> User (需要在命令处理器中手动处理，因为涉及值对象创建)
        
        // UpdateUserDto -> User (需要在命令处理器中手动处理)
    }

    /// <summary>
    /// 创建角色映射
    /// </summary>
    private void CreateRoleMappings()
    {
        // Role -> RoleDto
        CreateMap<Role, RoleDto>()
            .ForMember(dest => dest.Permissions, opt => opt.MapFrom(src => new List<PermissionDto>())) // 默认空列表，需要在查询时单独处理
            .ForMember(dest => dest.IsActive, opt => opt.MapFrom(src => src.IsActive));
    }

    /// <summary>
    /// 创建权限映射
    /// </summary>
    private void CreatePermissionMappings()
    {
        // Permission -> PermissionDto
        CreateMap<Permission, PermissionDto>();
    }
}
