namespace Whimlab.AI.Agent.Platform.Application.Core.Services;

/// <summary>
/// 业务规则验证服务接口
/// 统一的业务规则验证框架，集成配额检查和权限验证
/// </summary>
public interface IBusinessRuleValidationService
{
    /// <summary>
    /// 验证用户操作权限和配额
    /// </summary>
    /// <param name="request">验证请求</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>验证结果</returns>
    Task<BusinessRuleValidationResult> ValidateAsync(BusinessRuleValidationRequest request, CancellationToken cancellationToken = default);

    /// <summary>
    /// 验证权限
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="permissionName">权限名称</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>验证结果</returns>
    Task<BusinessRuleValidationResult> ValidatePermissionAsync(Guid userId, string permissionName, CancellationToken cancellationToken = default);

    /// <summary>
    /// 验证资源访问权限
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="resourceType">资源类型</param>
    /// <param name="resourceId">资源ID</param>
    /// <param name="operation">操作类型</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>验证结果</returns>
    Task<BusinessRuleValidationResult> ValidateResourceAccessAsync(Guid userId, string resourceType, Guid resourceId, string operation, CancellationToken cancellationToken = default);

    /// <summary>
    /// 验证配额
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="quotaType">配额类型</param>
    /// <param name="requestedAmount">请求使用量</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>验证结果</returns>
    Task<BusinessRuleValidationResult> ValidateQuotaAsync(Guid userId, string quotaType, long requestedAmount, CancellationToken cancellationToken = default);

    /// <summary>
    /// 验证并使用配额
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="quotaType">配额类型</param>
    /// <param name="amount">使用量</param>
    /// <param name="description">使用描述</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>验证和使用结果</returns>
    Task<BusinessRuleValidationResult> ValidateAndUseQuotaAsync(Guid userId, string quotaType, long amount, string? description = null, CancellationToken cancellationToken = default);
}

/// <summary>
/// 业务规则验证请求
/// </summary>
public class BusinessRuleValidationRequest
{
    /// <summary>
    /// 用户ID
    /// </summary>
    public Guid UserId { get; set; }

    /// <summary>
    /// 操作类型
    /// </summary>
    public string Operation { get; set; } = string.Empty;

    /// <summary>
    /// 资源类型
    /// </summary>
    public string? ResourceType { get; set; }

    /// <summary>
    /// 资源ID
    /// </summary>
    public Guid? ResourceId { get; set; }

    /// <summary>
    /// 权限名称列表
    /// </summary>
    public List<string> RequiredPermissions { get; set; } = new();

    /// <summary>
    /// 配额验证列表
    /// </summary>
    public List<QuotaValidationRequest> QuotaValidations { get; set; } = new();

    /// <summary>
    /// 是否需要所有权限（true）还是任一权限（false）
    /// </summary>
    public bool RequireAllPermissions { get; set; } = true;

    /// <summary>
    /// 验证上下文数据
    /// </summary>
    public Dictionary<string, object> Context { get; set; } = new();
}

/// <summary>
/// 配额验证请求
/// </summary>
public class QuotaValidationRequest
{
    /// <summary>
    /// 配额类型
    /// </summary>
    public string QuotaType { get; set; } = string.Empty;

    /// <summary>
    /// 请求使用量
    /// </summary>
    public long RequestedAmount { get; set; }

    /// <summary>
    /// 是否实际使用配额
    /// </summary>
    public bool ConsumeQuota { get; set; } = false;

    /// <summary>
    /// 使用描述
    /// </summary>
    public string? Description { get; set; }
}

/// <summary>
/// 业务规则验证结果
/// </summary>
public class BusinessRuleValidationResult
{
    /// <summary>
    /// 是否验证通过
    /// </summary>
    public bool IsValid { get; set; }

    /// <summary>
    /// 错误消息
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// 错误代码
    /// </summary>
    public string? ErrorCode { get; set; }

    /// <summary>
    /// 失败的权限列表
    /// </summary>
    public List<string> FailedPermissions { get; set; } = new();

    /// <summary>
    /// 失败的配额验证列表
    /// </summary>
    public List<QuotaValidationFailure> FailedQuotaValidations { get; set; } = new();

    /// <summary>
    /// 警告消息列表
    /// </summary>
    public List<string> Warnings { get; set; } = new();

    /// <summary>
    /// 建议操作列表
    /// </summary>
    public List<string> SuggestedActions { get; set; } = new();

    /// <summary>
    /// 验证详情
    /// </summary>
    public Dictionary<string, object> Details { get; set; } = new();

    /// <summary>
    /// 创建成功结果
    /// </summary>
    /// <returns>成功的验证结果</returns>
    public static BusinessRuleValidationResult Success()
    {
        return new BusinessRuleValidationResult { IsValid = true };
    }

    /// <summary>
    /// 创建失败结果
    /// </summary>
    /// <param name="errorMessage">错误消息</param>
    /// <param name="errorCode">错误代码</param>
    /// <returns>失败的验证结果</returns>
    public static BusinessRuleValidationResult Failure(string errorMessage, string? errorCode = null)
    {
        return new BusinessRuleValidationResult
        {
            IsValid = false,
            ErrorMessage = errorMessage,
            ErrorCode = errorCode
        };
    }

    /// <summary>
    /// 创建权限失败结果
    /// </summary>
    /// <param name="failedPermissions">失败的权限列表</param>
    /// <returns>权限失败的验证结果</returns>
    public static BusinessRuleValidationResult PermissionFailure(IEnumerable<string> failedPermissions)
    {
        var permissions = failedPermissions.ToList();
        return new BusinessRuleValidationResult
        {
            IsValid = false,
            ErrorMessage = $"缺少必要权限: {string.Join(", ", permissions)}",
            ErrorCode = "PERMISSION_DENIED",
            FailedPermissions = permissions
        };
    }

    /// <summary>
    /// 创建配额失败结果
    /// </summary>
    /// <param name="failedQuotaValidations">失败的配额验证列表</param>
    /// <returns>配额失败的验证结果</returns>
    public static BusinessRuleValidationResult QuotaFailure(IEnumerable<QuotaValidationFailure> failedQuotaValidations)
    {
        var failures = failedQuotaValidations.ToList();
        var quotaTypes = failures.Select(f => f.QuotaType);
        return new BusinessRuleValidationResult
        {
            IsValid = false,
            ErrorMessage = $"配额不足: {string.Join(", ", quotaTypes)}",
            ErrorCode = "QUOTA_EXCEEDED",
            FailedQuotaValidations = failures
        };
    }
}

/// <summary>
/// 配额验证失败详情
/// </summary>
public class QuotaValidationFailure
{
    /// <summary>
    /// 配额类型
    /// </summary>
    public string QuotaType { get; set; } = string.Empty;

    /// <summary>
    /// 请求使用量
    /// </summary>
    public long RequestedAmount { get; set; }

    /// <summary>
    /// 当前使用量
    /// </summary>
    public long CurrentUsage { get; set; }

    /// <summary>
    /// 配额限制
    /// </summary>
    public long QuotaLimit { get; set; }

    /// <summary>
    /// 剩余配额
    /// </summary>
    public long RemainingQuota { get; set; }

    /// <summary>
    /// 失败原因
    /// </summary>
    public string Reason { get; set; } = string.Empty;

    /// <summary>
    /// 建议操作
    /// </summary>
    public List<string> SuggestedActions { get; set; } = new();
}

/// <summary>
/// 业务规则验证错误代码常量
/// </summary>
public static class BusinessRuleErrorCodes
{
    public const string PermissionDenied = "PERMISSION_DENIED";
    public const string QuotaExceeded = "QUOTA_EXCEEDED";
    public const string ResourceNotFound = "RESOURCE_NOT_FOUND";
    public const string ResourceAccessDenied = "RESOURCE_ACCESS_DENIED";
    public const string UserNotFound = "USER_NOT_FOUND";
    public const string InvalidOperation = "INVALID_OPERATION";
    public const string SystemError = "SYSTEM_ERROR";
}

/// <summary>
/// 配额类型常量
/// </summary>
public static class QuotaTypes
{
    /// <summary>
    /// 智能体相关配额
    /// </summary>
    public static class Agent
    {
        public const string CreateAgent = "agent.create";
        public const string PublishAgent = "agent.publish";
        public const string AgentExecution = "agent.execution";
        public const string AgentStorage = "agent.storage";
    }

    /// <summary>
    /// 对话相关配额
    /// </summary>
    public static class Conversation
    {
        public const string CreateConversation = "conversation.create";
        public const string MessageCount = "conversation.message_count";
        public const string ConversationStorage = "conversation.storage";
        public const string TokenUsage = "conversation.token_usage";
    }

    /// <summary>
    /// API相关配额
    /// </summary>
    public static class Api
    {
        public const string ApiCalls = "api.calls";
        public const string DataTransfer = "api.data_transfer";
        public const string RequestRate = "api.request_rate";
    }

    /// <summary>
    /// 存储相关配额
    /// </summary>
    public static class Storage
    {
        public const string FileStorage = "storage.file_storage";
        public const string DatabaseStorage = "storage.database_storage";
        public const string BackupStorage = "storage.backup_storage";
    }
}
