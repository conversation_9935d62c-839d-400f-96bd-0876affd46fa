using AutoMapper;
using Whimlab.AI.Agent.Platform.Application.Conversation.DTOs;
using Whimlab.AI.Agent.Platform.Domain.Conversation.Entities;

namespace Whimlab.AI.Agent.Platform.Application.Conversation.Mappings;

/// <summary>
/// 对话映射扩展方法
/// </summary>
public static class ConversationMappingExtensions
{
    /// <summary>
    /// 映射对话实体到DTO并填充智能体名称
    /// </summary>
    /// <param name="mapper">AutoMapper实例</param>
    /// <param name="conversation">对话实体</param>
    /// <param name="agentName">智能体名称</param>
    /// <returns>对话DTO</returns>
    public static ConversationDto MapToDto(this IMapper mapper, Domain.Conversation.Entities.Conversation conversation, string? agentName = null)
    {
        var dto = mapper.Map<ConversationDto>(conversation);
        
        // 填充智能体名称
        dto.AgentName = agentName ?? "未知智能体";
        
        return dto;
    }

    /// <summary>
    /// 映射对话实体到摘要DTO并填充智能体名称
    /// </summary>
    /// <param name="mapper">AutoMapper实例</param>
    /// <param name="conversation">对话实体</param>
    /// <param name="agentName">智能体名称</param>
    /// <returns>对话摘要DTO</returns>
    public static ConversationSummaryDto MapToSummaryDto(this IMapper mapper, Domain.Conversation.Entities.Conversation conversation, string? agentName = null)
    {
        var dto = mapper.Map<ConversationSummaryDto>(conversation);
        
        // 填充智能体名称
        dto.AgentName = agentName ?? "未知智能体";
        
        return dto;
    }

    /// <summary>
    /// 批量映射对话实体列表到DTO列表
    /// </summary>
    /// <param name="mapper">AutoMapper实例</param>
    /// <param name="conversations">对话实体列表</param>
    /// <param name="agentNames">智能体名称字典（AgentId -> AgentName）</param>
    /// <returns>对话DTO列表</returns>
    public static List<ConversationSummaryDto> MapToSummaryDtos(this IMapper mapper, 
        IEnumerable<Domain.Conversation.Entities.Conversation> conversations, 
        Dictionary<Guid, string>? agentNames = null)
    {
        return conversations.Select(conversation =>
        {
            var dto = mapper.Map<ConversationSummaryDto>(conversation);
            
            // 填充智能体名称
            if (agentNames?.TryGetValue(conversation.AgentId, out var agentName) == true)
            {
                dto.AgentName = agentName;
            }
            else
            {
                dto.AgentName = "未知智能体";
            }
            
            return dto;
        }).ToList();
    }

    /// <summary>
    /// 创建消息实体的辅助方法
    /// </summary>
    /// <param name="createMessageDto">创建消息DTO</param>
    /// <param name="conversationId">对话ID</param>
    /// <returns>消息实体</returns>
    public static Message CreateMessageFromDto(CreateMessageDto createMessageDto, Guid conversationId)
    {
        // 这里需要根据实际的Message构造函数来实现
        // 由于Message可能有复杂的构造逻辑，这个方法应该在具体的处理器中实现
        throw new NotImplementedException("此方法应该在具体的命令处理器中实现");
    }

    /// <summary>
    /// 创建附件实体的辅助方法
    /// </summary>
    /// <param name="attachmentDto">附件DTO</param>
    /// <param name="messageId">消息ID</param>
    /// <returns>附件实体</returns>
    public static MessageAttachment CreateAttachmentFromDto(CreateMessageAttachmentDto attachmentDto, Guid messageId)
    {
        // 这里需要根据实际的MessageAttachment构造函数来实现
        // 由于MessageAttachment可能有复杂的构造逻辑，这个方法应该在具体的处理器中实现
        throw new NotImplementedException("此方法应该在具体的命令处理器中实现");
    }
}
