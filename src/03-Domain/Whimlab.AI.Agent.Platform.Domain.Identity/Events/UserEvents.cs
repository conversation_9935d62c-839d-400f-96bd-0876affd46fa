using MediatR;
using Whimlab.AI.Agent.Platform.Shared.Common.Enums;

namespace Whimlab.AI.Agent.Platform.Domain.Identity.Events;

/// <summary>
/// 用户创建事件
/// </summary>
/// <param name="UserId">用户ID</param>
/// <param name="Email">邮箱地址</param>
/// <param name="UserType">用户类型</param>
public record UserCreatedEvent(Guid UserId, string Email, UserType UserType) : INotification;

/// <summary>
/// 用户资料更新事件
/// </summary>
/// <param name="UserId">用户ID</param>
/// <param name="DisplayName">显示名称</param>
/// <param name="AvatarUrl">头像URL</param>
public record UserProfileUpdatedEvent(Guid UserId, string DisplayName, string? AvatarUrl) : INotification;

/// <summary>
/// 用户密码修改事件
/// </summary>
/// <param name="UserId">用户ID</param>
public record UserPasswordChangedEvent(Guid UserId) : INotification;

/// <summary>
/// 用户密码重置事件
/// </summary>
/// <param name="UserId">用户ID</param>
public record UserPasswordResetEvent(Guid UserId) : INotification;

/// <summary>
/// 用户邮箱验证事件
/// </summary>
/// <param name="UserId">用户ID</param>
/// <param name="Email">邮箱地址</param>
public record UserEmailVerifiedEvent(Guid UserId, string Email) : INotification;

/// <summary>
/// 用户登录事件
/// </summary>
/// <param name="UserId">用户ID</param>
/// <param name="IpAddress">IP地址</param>
public record UserLoggedInEvent(Guid UserId, string IpAddress) : INotification;

/// <summary>
/// 用户锁定事件
/// </summary>
/// <param name="UserId">用户ID</param>
/// <param name="LockoutEnd">锁定结束时间</param>
public record UserLockedEvent(Guid UserId, DateTime LockoutEnd) : INotification;

/// <summary>
/// 用户激活事件
/// </summary>
/// <param name="UserId">用户ID</param>
public record UserActivatedEvent(Guid UserId) : INotification;

/// <summary>
/// 用户停用事件
/// </summary>
/// <param name="UserId">用户ID</param>
public record UserDeactivatedEvent(Guid UserId) : INotification;

/// <summary>
/// 角色创建事件
/// </summary>
/// <param name="RoleId">角色ID</param>
/// <param name="RoleName">角色名称</param>
public record RoleCreatedEvent(Guid RoleId, string RoleName) : INotification;

/// <summary>
/// 角色更新事件
/// </summary>
/// <param name="RoleId">角色ID</param>
/// <param name="RoleName">角色名称</param>
public record RoleUpdatedEvent(Guid RoleId, string RoleName) : INotification;

/// <summary>
/// 角色删除事件
/// </summary>
/// <param name="RoleId">角色ID</param>
/// <param name="RoleName">角色名称</param>
public record RoleDeletedEvent(Guid RoleId, string RoleName) : INotification;

/// <summary>
/// 用户角色分配事件
/// </summary>
/// <param name="UserId">用户ID</param>
/// <param name="RoleId">角色ID</param>
/// <param name="RoleName">角色名称</param>
public record UserRoleAssignedEvent(Guid UserId, Guid RoleId, string RoleName) : INotification;

/// <summary>
/// 用户角色移除事件
/// </summary>
/// <param name="UserId">用户ID</param>
/// <param name="RoleId">角色ID</param>
/// <param name="RoleName">角色名称</param>
public record UserRoleRemovedEvent(Guid UserId, Guid RoleId, string RoleName) : INotification;

/// <summary>
/// 权限创建事件
/// </summary>
/// <param name="PermissionId">权限ID</param>
/// <param name="PermissionName">权限名称</param>
public record PermissionCreatedEvent(Guid PermissionId, string PermissionName) : INotification;

/// <summary>
/// 权限更新事件
/// </summary>
/// <param name="PermissionId">权限ID</param>
/// <param name="PermissionName">权限名称</param>
public record PermissionUpdatedEvent(Guid PermissionId, string PermissionName) : INotification;

/// <summary>
/// 权限删除事件
/// </summary>
/// <param name="PermissionId">权限ID</param>
/// <param name="PermissionName">权限名称</param>
public record PermissionDeletedEvent(Guid PermissionId, string PermissionName) : INotification;

/// <summary>
/// 角色权限分配事件
/// </summary>
/// <param name="RoleId">角色ID</param>
/// <param name="PermissionId">权限ID</param>
/// <param name="PermissionName">权限名称</param>
public record RolePermissionAssignedEvent(Guid RoleId, Guid PermissionId, string PermissionName) : INotification;

/// <summary>
/// 角色权限移除事件
/// </summary>
/// <param name="RoleId">角色ID</param>
/// <param name="PermissionId">权限ID</param>
/// <param name="PermissionName">权限名称</param>
public record RolePermissionRemovedEvent(Guid RoleId, Guid PermissionId, string PermissionName) : INotification;
