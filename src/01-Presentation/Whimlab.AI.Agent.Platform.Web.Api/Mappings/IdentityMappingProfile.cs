using AutoMapper;
using Whimlab.AI.Agent.Platform.Web.Api.Models.Identity.Requests;
using Whimlab.AI.Agent.Platform.Web.Api.Models.Identity.Responses;
using Whimlab.AI.Agent.Platform.Application.Identity.Commands;
using Whimlab.AI.Agent.Platform.Application.Identity.DTOs;

namespace Whimlab.AI.Agent.Platform.Web.Api.Mappings;

/// <summary>
/// Identity模块的AutoMapper映射配置
/// 定义WebAPI请求/响应模型与应用层DTO之间的映射关系
/// </summary>
public class IdentityMappingProfile : Profile
{
    /// <summary>
    /// 初始化映射配置
    /// </summary>
    public IdentityMappingProfile()
    {
        ConfigureAuthenticationMappings();
        ConfigureUserMappings();
        ConfigureRoleMappings();
        ConfigurePermissionMappings();
    }

    /// <summary>
    /// 配置认证相关的映射
    /// </summary>
    private void ConfigureAuthenticationMappings()
    {
        // 注册请求 -> 创建用户命令
        CreateMap<RegisterRequest, CreateUserCommand>()
            .ForMember(dest => dest.UserType, opt => opt.MapFrom(src => Shared.Common.Enums.UserType.Customer))
            .ForMember(dest => dest.RoleIds, opt => opt.MapFrom(src => new List<Guid>()));

        // 登录请求 -> 登录命令
        CreateMap<LoginRequest, LoginCommand>();

        // 刷新令牌请求 -> 刷新令牌命令
        CreateMap<RefreshTokenRequest, RefreshTokenCommand>();

        // 撤销令牌请求 -> 撤销令牌命令
        CreateMap<RevokeTokenRequest, RevokeTokenCommand>();

        // 修改密码请求 -> 修改密码命令
        CreateMap<ChangePasswordRequest, ChangePasswordCommand>();

        // 忘记密码请求 -> 发送密码重置邮件命令
        CreateMap<ForgotPasswordRequest, SendPasswordResetEmailCommand>();

        // 重置密码请求 -> 重置密码命令
        CreateMap<ResetPasswordRequest, ResetPasswordCommand>();

        // 验证邮箱请求 -> 验证邮箱命令
        CreateMap<VerifyEmailRequest, VerifyEmailCommand>();

        // 验证手机号请求 -> 验证手机号命令
        CreateMap<VerifyPhoneRequest, VerifyPhoneCommand>();

        // 认证令牌DTO -> 认证令牌响应
        CreateMap<AuthTokenDto, AuthTokenResponse>()
            .ForMember(dest => dest.UserInfo, opt => opt.MapFrom(src => src.UserInfo));
    }

    /// <summary>
    /// 配置用户相关的映射
    /// </summary>
    private void ConfigureUserMappings()
    {
        // 创建用户请求 -> 创建用户命令
        CreateMap<CreateUserRequest, CreateUserCommand>();

        // 更新用户请求 -> 更新用户命令
        CreateMap<UpdateUserRequest, UpdateUserCommand>();

        // 分配角色请求 -> 分配角色给用户命令
        CreateMap<AssignRolesRequest, AssignRolesToUserCommand>();

        // 移除角色请求 -> 移除用户角色命令
        CreateMap<RemoveRolesRequest, RemoveRolesFromUserCommand>();

        // 用户DTO -> 用户信息响应
        CreateMap<UserDto, UserInfoResponse>()
            .ForMember(dest => dest.Roles, opt => opt.MapFrom(src => src.Roles.Select(r => r.Name)))
            .ForMember(dest => dest.Permissions, opt => opt.MapFrom(src => src.Roles.SelectMany(r => r.Permissions.Select(p => p.Name))));

        // 用户DTO -> 用户详细信息响应
        CreateMap<UserDto, UserDetailResponse>()
            .ForMember(dest => dest.Roles, opt => opt.MapFrom(src => src.Roles));

        // 用户DTO -> 用户简要信息响应
        CreateMap<UserDto, UserSummaryResponse>();

        // 用户简要DTO -> 用户简要信息响应
        CreateMap<UserSummaryDto, UserSummaryResponse>();
    }

    /// <summary>
    /// 配置角色相关的映射
    /// </summary>
    private void ConfigureRoleMappings()
    {
        // 创建角色请求 -> 创建角色命令
        CreateMap<CreateRoleRequest, CreateRoleCommand>();

        // 更新角色请求 -> 更新角色命令
        CreateMap<UpdateRoleRequest, UpdateRoleCommand>();

        // 分配权限请求 -> 分配权限给角色命令
        CreateMap<AssignPermissionsRequest, AssignPermissionsToRoleCommand>();

        // 移除权限请求 -> 移除角色权限命令
        CreateMap<RemovePermissionsRequest, RemovePermissionsFromRoleCommand>();

        // 角色DTO -> 角色响应
        CreateMap<RoleDto, RoleResponse>()
            .ForMember(dest => dest.Permissions, opt => opt.MapFrom(src => src.Permissions))
            .ForMember(dest => dest.DisplayName, opt => opt.MapFrom(src => src.Name)); // 如果DTO中没有DisplayName，使用Name
    }

    /// <summary>
    /// 配置权限相关的映射
    /// </summary>
    private void ConfigurePermissionMappings()
    {
        // 权限DTO -> 权限响应
        CreateMap<PermissionDto, PermissionResponse>()
            .ForMember(dest => dest.DisplayName, opt => opt.MapFrom(src => src.Name)); // 如果DTO中没有DisplayName，使用Name
    }
}
