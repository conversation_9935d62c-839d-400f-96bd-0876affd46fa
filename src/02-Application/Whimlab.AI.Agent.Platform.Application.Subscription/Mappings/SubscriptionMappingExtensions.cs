using AutoMapper;
using Whimlab.AI.Agent.Platform.Application.Subscription.DTOs;
using Whimlab.AI.Agent.Platform.Domain.Subscription.Entities;
using Whimlab.AI.Agent.Platform.Domain.Subscription.ValueObjects;

namespace Whimlab.AI.Agent.Platform.Application.Subscription.Mappings;

/// <summary>
/// 订阅映射扩展方法
/// </summary>
public static class SubscriptionMappingExtensions
{
    /// <summary>
    /// 映射客户订阅实体到DTO并填充计划名称
    /// </summary>
    /// <param name="mapper">AutoMapper实例</param>
    /// <param name="subscription">客户订阅实体</param>
    /// <param name="planName">计划名称</param>
    /// <returns>客户订阅DTO</returns>
    public static CustomerSubscriptionDto MapToDto(this IMapper mapper, CustomerSubscription subscription, string? planName = null)
    {
        var dto = mapper.Map<CustomerSubscriptionDto>(subscription);
        
        // 填充计划名称
        dto.PlanName = planName ?? "未知计划";
        
        return dto;
    }

    /// <summary>
    /// 映射配额使用实体到DTO并计算相关字段
    /// </summary>
    /// <param name="mapper">AutoMapper实例</param>
    /// <param name="quotaUsage">配额使用实体</param>
    /// <param name="planQuota">计划配额信息</param>
    /// <returns>配额使用DTO</returns>
    public static QuotaUsageDto MapToDto(this IMapper mapper, QuotaUsage quotaUsage, PlanQuota? planQuota = null)
    {
        var dto = mapper.Map<QuotaUsageDto>(quotaUsage);
        
        if (planQuota != null)
        {
            // 填充计划相关信息
            dto.QuotaLimit = planQuota.Limit;
            dto.ResetPeriod = planQuota.Period;
            dto.IsUnlimited = planQuota.Limit == 0;
            
            // 计算剩余配额
            if (planQuota.Limit > 0)
            {
                dto.RemainingQuota = Math.Max(0, planQuota.Limit - quotaUsage.CumulativeAmount);
                dto.UsageRate = planQuota.Limit > 0 ? (double)quotaUsage.CumulativeAmount / planQuota.Limit : 0;
                dto.IsOverLimit = quotaUsage.CumulativeAmount > planQuota.Limit;
            }
            else
            {
                dto.RemainingQuota = long.MaxValue;
                dto.UsageRate = 0;
                dto.IsOverLimit = false;
            }
            
            // 计算下次重置时间
            dto.NextResetDate = CalculateNextResetTime(quotaUsage.UsedAt, planQuota.Period);
        }
        else
        {
            // 默认值
            dto.QuotaLimit = 0;
            dto.RemainingQuota = 0;
            dto.UsageRate = 0;
            dto.ResetPeriod = "Unknown";
            dto.IsUnlimited = false;
            dto.IsOverLimit = false;
            dto.NextResetDate = null;
        }
        
        return dto;
    }

    /// <summary>
    /// 批量映射配额使用实体列表到DTO列表
    /// </summary>
    /// <param name="mapper">AutoMapper实例</param>
    /// <param name="quotaUsages">配额使用实体列表</param>
    /// <param name="planQuotas">计划配额字典（QuotaType -> PlanQuota）</param>
    /// <returns>配额使用DTO列表</returns>
    public static List<QuotaUsageDto> MapToDtos(this IMapper mapper, 
        IEnumerable<QuotaUsage> quotaUsages, 
        Dictionary<string, PlanQuota>? planQuotas = null)
    {
        return quotaUsages.Select(usage =>
        {
            var planQuota = planQuotas?.GetValueOrDefault(usage.QuotaType);
            return mapper.MapToDto(usage, planQuota);
        }).ToList();
    }

    /// <summary>
    /// 批量映射客户订阅实体列表到DTO列表
    /// </summary>
    /// <param name="mapper">AutoMapper实例</param>
    /// <param name="subscriptions">客户订阅实体列表</param>
    /// <param name="planNames">计划名称字典（PlanId -> PlanName）</param>
    /// <returns>客户订阅DTO列表</returns>
    public static List<CustomerSubscriptionDto> MapToDtos(this IMapper mapper, 
        IEnumerable<CustomerSubscription> subscriptions, 
        Dictionary<Guid, string>? planNames = null)
    {
        return subscriptions.Select(subscription =>
        {
            var planName = planNames?.GetValueOrDefault(subscription.SubscriptionPlanId);
            return mapper.MapToDto(subscription, planName);
        }).ToList();
    }

    /// <summary>
    /// 计算下次重置时间
    /// </summary>
    /// <param name="usedAt">使用时间</param>
    /// <param name="period">重置周期</param>
    /// <returns>下次重置时间</returns>
    private static DateTime? CalculateNextResetTime(DateTime usedAt, string period)
    {
        return period.ToLowerInvariant() switch
        {
            "daily" => usedAt.AddDays(1),
            "weekly" => usedAt.AddDays(7),
            "monthly" => usedAt.AddMonths(1),
            "yearly" => usedAt.AddYears(1),
            _ => null
        };
    }

    /// <summary>
    /// 创建订阅计划的辅助方法
    /// </summary>
    /// <param name="createPlanDto">创建计划DTO</param>
    /// <returns>订阅计划实体</returns>
    public static SubscriptionPlan CreatePlanFromDto(CreateSubscriptionPlanDto createPlanDto)
    {
        // 这里需要根据实际的SubscriptionPlan构造函数来实现
        // 由于SubscriptionPlan可能有复杂的构造逻辑，这个方法应该在具体的处理器中实现
        throw new NotImplementedException("此方法应该在具体的命令处理器中实现");
    }

    /// <summary>
    /// 创建客户订阅的辅助方法
    /// </summary>
    /// <param name="createSubscriptionDto">创建订阅DTO</param>
    /// <returns>客户订阅实体</returns>
    public static CustomerSubscription CreateSubscriptionFromDto(CreateCustomerSubscriptionDto createSubscriptionDto)
    {
        // 这里需要根据实际的CustomerSubscription构造函数来实现
        // 由于CustomerSubscription可能有复杂的构造逻辑，这个方法应该在具体的处理器中实现
        throw new NotImplementedException("此方法应该在具体的命令处理器中实现");
    }

    /// <summary>
    /// 创建配额限制值对象的辅助方法
    /// </summary>
    /// <param name="quotaLimitDto">配额限制DTO</param>
    /// <returns>计划配额值对象</returns>
    public static PlanQuota CreateQuotaFromDto(QuotaLimitDto quotaLimitDto)
    {
        // 这里需要根据实际的PlanQuota构造函数来实现
        // 由于PlanQuota是值对象，可能有特定的创建方法
        throw new NotImplementedException("此方法应该在具体的命令处理器中实现");
    }
}
