using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Whimlab.AI.Agent.Platform.Domain.Identity.Entities;
using Whimlab.AI.Agent.Platform.Domain.Identity.Repositories;
using Whimlab.AI.Agent.Platform.Infrastructure.Data;

namespace Whimlab.AI.Agent.Platform.Infrastructure.Repositories;

/// <summary>
/// 权限仓储实现
/// </summary>
public class PermissionRepository : BaseRepository<Permission, Guid>, IPermissionRepository
{
    /// <summary>
    /// 初始化权限仓储
    /// </summary>
    /// <param name="context">数据库上下文</param>
    /// <param name="logger">日志记录器</param>
    public PermissionRepository(
        AgentPlatformDbContext context,
        ILogger<PermissionRepository> logger) : base(context, logger)
    {
    }

    /// <summary>
    /// 根据名称获取权限
    /// </summary>
    /// <param name="name">权限名称</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>权限实体</returns>
    public async Task<Permission?> GetByNameAsync(string name, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("根据名称获取权限: {Name}", name);

            return await _dbSet
                .FirstOrDefaultAsync(p => p.Name == name, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "根据名称获取权限时发生错误: {Name}", name);
            throw;
        }
    }

    /// <summary>
    /// 检查权限名称是否存在
    /// </summary>
    /// <param name="name">权限名称</param>
    /// <param name="excludePermissionId">排除的权限ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>是否存在</returns>
    public async Task<bool> IsNameExistsAsync(string name, Guid? excludePermissionId = null, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("检查权限名称是否存在: {Name}, 排除权限ID: {ExcludePermissionId}", name, excludePermissionId);

            var query = _dbSet.Where(p => p.Name == name);

            if (excludePermissionId.HasValue)
            {
                query = query.Where(p => p.Id != excludePermissionId.Value);
            }

            return await query.AnyAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "检查权限名称是否存在时发生错误: {Name}", name);
            throw;
        }
    }

    /// <summary>
    /// 根据分组获取权限列表
    /// </summary>
    /// <param name="group">权限分组</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>权限列表</returns>
    public async Task<IEnumerable<Permission>> GetByGroupAsync(string group, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("根据分组获取权限列表: {Group}", group);

            return await _dbSet
                .Where(p => p.Group == group)
                .OrderBy(p => p.SortOrder)
                .ThenBy(p => p.Name)
                .ToListAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "根据分组获取权限列表时发生错误: {Group}", group);
            throw;
        }
    }

    /// <summary>
    /// 获取系统权限列表
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>系统权限列表</returns>
    public async Task<IEnumerable<Permission>> GetSystemPermissionsAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("获取系统权限列表");

            return await _dbSet
                .Where(p => p.IsSystemPermission)
                .OrderBy(p => p.Group)
                .ThenBy(p => p.SortOrder)
                .ThenBy(p => p.Name)
                .ToListAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取系统权限列表时发生错误");
            throw;
        }
    }

    /// <summary>
    /// 获取权限列表
    /// </summary>
    /// <param name="isActive">是否激活</param>
    /// <param name="group">权限分组</param>
    /// <param name="searchTerm">搜索关键词</param>
    /// <param name="pageNumber">页码</param>
    /// <param name="pageSize">页大小</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>权限列表</returns>
    public async Task<(IEnumerable<Permission> Permissions, int TotalCount)> GetPermissionsAsync(
        bool? isActive = null,
        string? group = null,
        string? searchTerm = null,
        int pageNumber = 1,
        int pageSize = 20,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("获取权限列表，是否激活: {IsActive}, 分组: {Group}, 搜索词: {SearchTerm}, 页码: {PageNumber}, 页大小: {PageSize}",
                isActive, group, searchTerm, pageNumber, pageSize);

            var query = _dbSet.AsQueryable();

            if (isActive.HasValue)
            {
                query = query.Where(p => p.IsActive == isActive.Value);
            }

            if (!string.IsNullOrWhiteSpace(group))
            {
                query = query.Where(p => p.Group == group);
            }

            if (!string.IsNullOrWhiteSpace(searchTerm))
            {
                var searchTermLower = searchTerm.ToLower();
                query = query.Where(p => 
                    p.Name.ToLower().Contains(searchTermLower) ||
                    p.DisplayName.ToLower().Contains(searchTermLower) ||
                    (p.Description != null && p.Description.ToLower().Contains(searchTermLower)));
            }

            var totalCount = await query.CountAsync(cancellationToken);

            var permissions = await query
                .OrderBy(p => p.Group)
                .ThenBy(p => p.SortOrder)
                .ThenBy(p => p.Name)
                .Skip((pageNumber - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync(cancellationToken);

            return (permissions, totalCount);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取权限列表时发生错误");
            throw;
        }
    }

    /// <summary>
    /// 获取所有权限分组
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>权限分组列表</returns>
    public async Task<IEnumerable<string>> GetGroupsAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("获取所有权限分组");

            return await _dbSet
                .Where(p => p.IsActive)
                .Select(p => p.Group)
                .Distinct()
                .OrderBy(g => g)
                .ToListAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取所有权限分组时发生错误");
            throw;
        }
    }

    /// <summary>
    /// 根据名称列表获取权限
    /// </summary>
    /// <param name="names">权限名称列表</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>权限列表</returns>
    public async Task<IEnumerable<Permission>> GetByNamesAsync(IEnumerable<string> names, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("根据名称列表获取权限: {Names}", string.Join(", ", names));

            return await _dbSet
                .Where(p => names.Contains(p.Name))
                .ToListAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "根据名称列表获取权限时发生错误: {Names}", string.Join(", ", names));
            throw;
        }
    }

    /// <summary>
    /// 获取权限的角色数量
    /// </summary>
    /// <param name="permissionId">权限ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>角色数量</returns>
    public async Task<int> GetRoleCountAsync(Guid permissionId, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("获取权限的角色数量: {PermissionId}", permissionId);

            return await _context.RolePermissions
                .Where(rp => rp.PermissionId == permissionId)
                .CountAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取权限的角色数量时发生错误: {PermissionId}", permissionId);
            throw;
        }
    }
}
