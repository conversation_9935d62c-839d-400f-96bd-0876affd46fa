version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:16
    container_name: whimlab-postgres
    ports:
      - "54321:5432"
    environment:
      POSTGRES_USER: luca
      POSTGRES_PASSWORD: secret
      POSTGRES_DB: whimlab-dev
      POSTGRES_INITDB_ARGS: "--encoding=UTF8 --locale=C"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./tools/database/init:/docker-entrypoint-initdb.d
    networks:
      - whimlab-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U luca -d whimlab-dev"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis Cache
  redis:
    image: redis:7.2-alpine
    container_name: whimlab-redis
    ports:
      - "26379:6379"
    command: redis-server --appendonly yes --requirepass ""
    volumes:
      - redis_data:/data
    networks:
      - whimlab-network
    healthcheck:
      test: ["<PERSON><PERSON>", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # MongoDB (Optional for document storage)
  mongodb:
    image: mongo:7.0
    container_name: whimlab-mongodb
    ports:
      - "27017:27017"
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: Passw0rd
      MONGO_INITDB_DATABASE: whimlab-dev
    volumes:
      - mongodb_data:/data/db
      - ./tools/database/mongo-init:/docker-entrypoint-initdb.d
    networks:
      - whimlab-network
    healthcheck:
      test: ["CMD", "mongosh", "--eval", "db.adminCommand('ping')"]
      interval: 10s
      timeout: 5s
      retries: 5

  # RabbitMQ (Optional for message queuing)
  rabbitmq:
    image: rabbitmq:3.12-management-alpine
    container_name: whimlab-rabbitmq
    ports:
      - "25672:5672"   # AMQP port
      - "15672:15672"  # Management UI
    environment:
      RABBITMQ_DEFAULT_USER: guest
      RABBITMQ_DEFAULT_PASS: guest
      RABBITMQ_DEFAULT_VHOST: /
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq
      - ./tools/deployment/rabbitmq/definitions.json:/etc/rabbitmq/definitions.json
      - ./tools/deployment/rabbitmq/rabbitmq.conf:/etc/rabbitmq/rabbitmq.conf
    networks:
      - whimlab-network
    healthcheck:
      test: ["CMD", "rabbitmq-diagnostics", "ping"]
      interval: 30s
      timeout: 10s
      retries: 5

  # Elasticsearch (Optional for logging and search)
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.11.0
    container_name: whimlab-elasticsearch
    ports:
      - "9200:9200"
      - "9300:9300"
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    networks:
      - whimlab-network
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:9200/_cluster/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 5

  # MinIO (Optional for object storage)
  minio:
    image: minio/minio:latest
    container_name: whimlab-minio
    ports:
      - "9000:9000"   # API port
      - "9001:9001"   # Console port
    environment:
      MINIO_ROOT_USER: minioadmin
      MINIO_ROOT_PASSWORD: minioadmin
    command: server /data --console-address ":9001"
    volumes:
      - minio_data:/data
    networks:
      - whimlab-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  mongodb_data:
    driver: local
  rabbitmq_data:
    driver: local
  elasticsearch_data:
    driver: local
  minio_data:
    driver: local

networks:
  whimlab-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
