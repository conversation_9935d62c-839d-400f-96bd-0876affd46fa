<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <GenerateDocumentationFile>true</GenerateDocumentationFile>
    <NoWarn>$(NoWarn);1591</NoWarn>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.OpenApi" />
    <PackageReference Include="Swashbuckle.AspNetCore" />
    <PackageReference Include="Swashbuckle.AspNetCore.Annotations" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" />
    <PackageReference Include="Asp.Versioning.Mvc" />
    <PackageReference Include="Asp.Versioning.Mvc.ApiExplorer" />
    <PackageReference Include="AutoMapper" />
    <PackageReference Include="AutoMapper.Extensions.Microsoft.DependencyInjection" />
    <PackageReference Include="MediatR" />
    <PackageReference Include="FluentValidation.AspNetCore" />
    <PackageReference Include="Serilog.AspNetCore" />
    <PackageReference Include="Microsoft.Extensions.Diagnostics.HealthChecks" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\02-Application\Whimlab.AI.Agent.Platform.Application.Core\Whimlab.AI.Agent.Platform.Application.Core.csproj" />
    <ProjectReference Include="..\..\02-Application\Whimlab.AI.Agent.Platform.Application.Identity\Whimlab.AI.Agent.Platform.Application.Identity.csproj" />
    <ProjectReference Include="..\..\02-Application\Whimlab.AI.Agent.Platform.Application.Agent\Whimlab.AI.Agent.Platform.Application.Agent.csproj" />
    <ProjectReference Include="..\..\02-Application\Whimlab.AI.Agent.Platform.Application.Conversation\Whimlab.AI.Agent.Platform.Application.Conversation.csproj" />
    <ProjectReference Include="..\..\02-Application\Whimlab.AI.Agent.Platform.Application.Subscription\Whimlab.AI.Agent.Platform.Application.Subscription.csproj" />
    <ProjectReference Include="..\..\04-Infrastructure\Whimlab.AI.Agent.Platform.Infrastructure\Whimlab.AI.Agent.Platform.Infrastructure.csproj" />
  </ItemGroup>

</Project>
