using Whimlab.AI.Agent.Platform.Domain.Subscription.Entities;
using Whimlab.AI.Agent.Platform.Domain.Core.Common;
using Whimlab.AI.Agent.Platform.Shared.Common.Enums;

namespace Whimlab.AI.Agent.Platform.Domain.Subscription.Repositories;

/// <summary>
/// 客户订阅仓储接口
/// </summary>
public interface ICustomerSubscriptionRepository
{
    /// <summary>
    /// 根据ID获取客户订阅
    /// </summary>
    /// <param name="id">订阅ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>客户订阅实体</returns>
    Task<CustomerSubscription?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default);

    /// <summary>
    /// 根据ID获取客户订阅（包含详细信息）
    /// </summary>
    /// <param name="id">订阅ID</param>
    /// <param name="includeQuotaUsages">是否包含配额使用记录</param>
    /// <param name="includeHistory">是否包含历史记录</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>客户订阅实体</returns>
    Task<CustomerSubscription?> GetByIdWithDetailsAsync(
        Guid id, 
        bool includeQuotaUsages = true, 
        bool includeHistory = true, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 根据客户ID获取当前活跃订阅
    /// </summary>
    /// <param name="customerId">客户ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>当前活跃订阅</returns>
    Task<CustomerSubscription?> GetActiveByCustomerIdAsync(Guid customerId, CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取客户的订阅列表
    /// </summary>
    /// <param name="customerId">客户ID</param>
    /// <param name="pageNumber">页码</param>
    /// <param name="pageSize">页大小</param>
    /// <param name="status">状态过滤</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>分页的订阅列表</returns>
    Task<PagedResult<CustomerSubscription>> GetCustomerSubscriptionsAsync(
        Guid customerId,
        int pageNumber = 1,
        int pageSize = 20,
        SubscriptionStatus? status = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取即将过期的订阅
    /// </summary>
    /// <param name="warningDays">预警天数</param>
    /// <param name="batchSize">批次大小</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>即将过期的订阅列表</returns>
    Task<IEnumerable<CustomerSubscription>> GetExpiringSoonAsync(
        int warningDays = 7,
        int batchSize = 100,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取需要续费的订阅
    /// </summary>
    /// <param name="batchSize">批次大小</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>需要续费的订阅列表</returns>
    Task<IEnumerable<CustomerSubscription>> GetSubscriptionsForRenewalAsync(
        int batchSize = 100,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取过期的订阅
    /// </summary>
    /// <param name="batchSize">批次大小</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>过期的订阅列表</returns>
    Task<IEnumerable<CustomerSubscription>> GetExpiredSubscriptionsAsync(
        int batchSize = 100,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取订阅计划的订阅统计
    /// </summary>
    /// <param name="subscriptionPlanId">订阅计划ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>订阅统计</returns>
    Task<SubscriptionPlanUsageStatistics> GetPlanUsageStatisticsAsync(
        Guid subscriptionPlanId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取客户的配额使用统计
    /// </summary>
    /// <param name="customerId">客户ID</param>
    /// <param name="quotaType">配额类型</param>
    /// <param name="startDate">开始日期</param>
    /// <param name="endDate">结束日期</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>配额使用统计</returns>
    Task<QuotaUsageStatistics> GetQuotaUsageStatisticsAsync(
        Guid customerId,
        string quotaType,
        DateTime startDate,
        DateTime endDate,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取订阅收入统计
    /// </summary>
    /// <param name="startDate">开始日期</param>
    /// <param name="endDate">结束日期</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>收入统计</returns>
    Task<SubscriptionRevenueStatistics> GetRevenueStatisticsAsync(
        DateTime startDate,
        DateTime endDate,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 添加客户订阅
    /// </summary>
    /// <param name="subscription">客户订阅实体</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>任务</returns>
    Task AddAsync(CustomerSubscription subscription, CancellationToken cancellationToken = default);

    /// <summary>
    /// 更新客户订阅
    /// </summary>
    /// <param name="subscription">客户订阅实体</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>任务</returns>
    Task UpdateAsync(CustomerSubscription subscription, CancellationToken cancellationToken = default);

    /// <summary>
    /// 删除客户订阅
    /// </summary>
    /// <param name="subscription">客户订阅实体</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>任务</returns>
    Task DeleteAsync(CustomerSubscription subscription, CancellationToken cancellationToken = default);

    /// <summary>
    /// 保存更改
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>受影响的行数</returns>
    Task<int> SaveChangesAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取需要配额重置的订阅列表
    /// </summary>
    /// <param name="quotaType">配额类型</param>
    /// <param name="resetThreshold">重置阈值时间</param>
    /// <param name="batchSize">批次大小</param>
    /// <param name="offset">偏移量</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>需要重置的订阅列表</returns>
    Task<List<CustomerSubscription>> GetSubscriptionsForQuotaResetAsync(
        string quotaType,
        DateTime resetThreshold,
        int batchSize,
        int offset,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取配额超限的客户列表
    /// </summary>
    /// <param name="quotaType">配额类型</param>
    /// <param name="batchSize">批次大小</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>超限客户ID列表</returns>
    Task<IEnumerable<Guid>> GetOverLimitCustomersAsync(
        string quotaType,
        int batchSize,
        CancellationToken cancellationToken = default);

}

/// <summary>
/// 订阅计划使用统计
/// </summary>
public class SubscriptionPlanUsageStatistics
{
    /// <summary>
    /// 订阅计划ID
    /// </summary>
    public Guid SubscriptionPlanId { get; set; }

    /// <summary>
    /// 总订阅数
    /// </summary>
    public int TotalSubscriptions { get; set; }

    /// <summary>
    /// 活跃订阅数
    /// </summary>
    public int ActiveSubscriptions { get; set; }

    /// <summary>
    /// 试用订阅数
    /// </summary>
    public int TrialSubscriptions { get; set; }

    /// <summary>
    /// 已取消订阅数
    /// </summary>
    public int CancelledSubscriptions { get; set; }

    /// <summary>
    /// 过期订阅数
    /// </summary>
    public int ExpiredSubscriptions { get; set; }

    /// <summary>
    /// 月度订阅数
    /// </summary>
    public int MonthlySubscriptions { get; set; }

    /// <summary>
    /// 年度订阅数
    /// </summary>
    public int YearlySubscriptions { get; set; }

    /// <summary>
    /// 平均订阅时长（天）
    /// </summary>
    public double AverageSubscriptionDuration { get; set; }

    /// <summary>
    /// 流失率
    /// </summary>
    public double ChurnRate { get; set; }
}

/// <summary>
/// 配额使用统计
/// </summary>
public class QuotaUsageStatistics
{
    /// <summary>
    /// 客户ID
    /// </summary>
    public Guid CustomerId { get; set; }

    /// <summary>
    /// 配额类型
    /// </summary>
    public string QuotaType { get; set; } = string.Empty;

    /// <summary>
    /// 总使用量
    /// </summary>
    public long TotalUsage { get; set; }

    /// <summary>
    /// 平均每日使用量
    /// </summary>
    public double AverageDailyUsage { get; set; }

    /// <summary>
    /// 最高单日使用量
    /// </summary>
    public long PeakDailyUsage { get; set; }

    /// <summary>
    /// 按日期分组的使用量
    /// </summary>
    public Dictionary<DateTime, long> UsageByDate { get; set; } = new();
}

/// <summary>
/// 订阅收入统计
/// </summary>
public class SubscriptionRevenueStatistics
{
    /// <summary>
    /// 总收入
    /// </summary>
    public decimal TotalRevenue { get; set; }

    /// <summary>
    /// 月度收入
    /// </summary>
    public decimal MonthlyRevenue { get; set; }

    /// <summary>
    /// 年度收入
    /// </summary>
    public decimal YearlyRevenue { get; set; }

    /// <summary>
    /// 平均每用户收入（ARPU）
    /// </summary>
    public decimal AverageRevenuePerUser { get; set; }

    /// <summary>
    /// 月度经常性收入（MRR）
    /// </summary>
    public decimal MonthlyRecurringRevenue { get; set; }

    /// <summary>
    /// 年度经常性收入（ARR）
    /// </summary>
    public decimal AnnualRecurringRevenue { get; set; }

    /// <summary>
    /// 按计划分组的收入
    /// </summary>
    public Dictionary<Guid, decimal> RevenueByPlan { get; set; } = new();

    /// <summary>
    /// 按日期分组的收入
    /// </summary>
    public Dictionary<DateTime, decimal> RevenueByDate { get; set; } = new();
}
