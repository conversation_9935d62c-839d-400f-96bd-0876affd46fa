using AutoMapper;
using Whimlab.AI.Agent.Platform.Application.Agent.DTOs;
using Whimlab.AI.Agent.Platform.Domain.Agent.ValueObjects;
using Whimlab.AI.Agent.Platform.Domain.Agent.Entities;

namespace Whimlab.AI.Agent.Platform.Application.Agent.Mappings;

/// <summary>
/// 智能体映射扩展方法
/// </summary>
public static class AgentMappingExtensions
{
    /// <summary>
    /// 创建智能体配置值对象的辅助方法
    /// </summary>
    /// <param name="configDto">配置DTO</param>
    /// <returns>智能体配置值对象</returns>
    public static AgentConfiguration CreateConfigurationFromDto(AgentConfigurationDto configDto)
    {
        // 创建模型配置
        var modelConfig = ModelConfiguration.Create(
            configDto.ModelConfig.Provider,
            configDto.ModelConfig.ModelName,
            configDto.ModelConfig.ApiEndpoint,
            configDto.ModelConfig.ApiKey
        );

        // 创建工具配置列表
        var toolConfigs = configDto.ToolConfigs?.Select(toolDto =>
            ToolConfiguration.Create(
                toolDto.ToolName,
                toolDto.ToolName, // 使用工具名称作为描述
                toolDto.ToolType,
                null, // 参数架构
                toolDto.Parameters,
                toolDto.IsEnabled
            )).ToList() ?? new List<ToolConfiguration>();

        // 创建智能体配置
        return AgentConfiguration.Create(
            Shared.Common.Enums.AgentType.SemanticKernel, // 默认Semantic Kernel类型
            modelConfig,
            configDto.SystemPrompt,
            0.7, // 默认温度
            2048, // 默认最大Token数
            1.0, // 默认TopP
            0.0, // 默认频率惩罚
            0.0, // 默认存在惩罚
            null, // 停止序列
            toolConfigs
        );
    }

    /// <summary>
    /// 创建模型配置值对象的辅助方法
    /// </summary>
    /// <param name="modelDto">模型配置DTO</param>
    /// <returns>模型配置值对象</returns>
    public static ModelConfiguration CreateModelConfigurationFromDto(ModelConfigurationDto modelDto)
    {
        return ModelConfiguration.Create(
            modelDto.Provider,
            modelDto.ModelName,
            modelDto.ApiEndpoint,
            modelDto.ApiKey
        );
    }

    /// <summary>
    /// 创建工具配置值对象的辅助方法
    /// </summary>
    /// <param name="toolDto">工具配置DTO</param>
    /// <returns>工具配置值对象</returns>
    public static ToolConfiguration CreateToolConfigurationFromDto(ToolConfigurationDto toolDto)
    {
        return ToolConfiguration.Create(
            toolDto.ToolName,
            toolDto.ToolName, // 使用工具名称作为描述
            toolDto.ToolType,
            null, // 参数架构
            toolDto.Parameters,
            toolDto.IsEnabled
        );
    }

    /// <summary>
    /// 创建智能体标签的辅助方法
    /// </summary>
    /// <param name="agentId">智能体ID</param>
    /// <param name="tagNames">标签名称列表</param>
    /// <returns>智能体标签列表</returns>
    public static List<AgentTag> CreateTagsFromNames(Guid agentId, IEnumerable<string> tagNames)
    {
        return tagNames?.Select(name => AgentTag.Create(agentId, name)).ToList() ?? new List<AgentTag>();
    }

    /// <summary>
    /// 更新智能体配置的辅助方法
    /// </summary>
    /// <param name="agent">智能体实体</param>
    /// <param name="configDto">新的配置DTO</param>
    /// <param name="versionNotes">版本说明</param>
    /// <param name="updatedBy">更新者ID</param>
    public static void UpdateConfigurationFromDto(Domain.Agent.Entities.Agent agent, AgentConfigurationDto configDto, string? versionNotes = null, Guid? updatedBy = null)
    {
        var newConfiguration = CreateConfigurationFromDto(configDto);
        agent.UpdateConfiguration(newConfiguration, versionNotes, updatedBy);
    }

    /// <summary>
    /// 验证配置DTO的有效性
    /// </summary>
    /// <param name="configDto">配置DTO</param>
    /// <returns>验证结果</returns>
    public static (bool IsValid, List<string> Errors) ValidateConfigurationDto(AgentConfigurationDto configDto)
    {
        var errors = new List<string>();

        if (string.IsNullOrWhiteSpace(configDto.SystemPrompt))
        {
            errors.Add("系统提示词不能为空");
        }

        if (configDto.ModelConfig == null)
        {
            errors.Add("模型配置不能为空");
        }
        else
        {
            if (string.IsNullOrWhiteSpace(configDto.ModelConfig.Provider))
            {
                errors.Add("模型提供商不能为空");
            }

            if (string.IsNullOrWhiteSpace(configDto.ModelConfig.ModelName))
            {
                errors.Add("模型名称不能为空");
            }
        }

        return (errors.Count == 0, errors);
    }

    /// <summary>
    /// 验证工具配置DTO的有效性
    /// </summary>
    /// <param name="toolDto">工具配置DTO</param>
    /// <returns>验证结果</returns>
    public static (bool IsValid, List<string> Errors) ValidateToolConfigurationDto(ToolConfigurationDto toolDto)
    {
        var errors = new List<string>();

        if (string.IsNullOrWhiteSpace(toolDto.ToolName))
        {
            errors.Add("工具名称不能为空");
        }

        // 可以根据需要添加更多验证规则

        return (errors.Count == 0, errors);
    }
}
