version: '3.8'

services:
  # Development overrides for PostgreSQL
  postgres:
    environment:
      POSTGRES_USER: luca
      POSTGRES_PASSWORD: secret
      POSTGRES_DB: whimlab-dev
    volumes:
      - ./tools/database/init-dev.sql:/docker-entrypoint-initdb.d/01-init-dev.sql

  # Development overrides for Redis
  redis:
    command: redis-server --appendonly yes --requirepass "" --maxmemory 256mb --maxmemory-policy allkeys-lru

  # Development overrides for MongoDB
  mongodb:
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: Passw0rd
      MONGO_INITDB_DATABASE: whimlab-dev

  # Development overrides for RabbitMQ
  rabbitmq:
    environment:
      RABBITMQ_DEFAULT_USER: guest
      RABBITMQ_DEFAULT_PASS: guest
      RABBITMQ_DEFAULT_VHOST: /
      RABBITMQ_ERLANG_COOKIE: whimlab-dev-cookie

  # Development overrides for Elasticsearch
  elasticsearch:
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms256m -Xmx256m"
      - cluster.name=whimlab-dev
      - node.name=whimlab-dev-node

  # Development overrides for MinIO
  minio:
    environment:
      MINIO_ROOT_USER: minioadmin
      MINIO_ROOT_PASSWORD: minioadmin
      MINIO_BROWSER_REDIRECT_URL: http://localhost:9001
