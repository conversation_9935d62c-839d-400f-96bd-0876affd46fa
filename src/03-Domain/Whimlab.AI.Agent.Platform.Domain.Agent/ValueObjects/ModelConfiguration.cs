using Whimlab.AI.Agent.Platform.Domain.Core.Common;

namespace Whimlab.AI.Agent.Platform.Domain.Agent.ValueObjects;

/// <summary>
/// 模型配置值对象
/// </summary>
public sealed class ModelConfiguration : ValueObject
{
    /// <summary>
    /// 模型提供商
    /// </summary>
    public string Provider { get; }

    /// <summary>
    /// 模型名称
    /// </summary>
    public string ModelName { get; }

    /// <summary>
    /// API端点
    /// </summary>
    public string? ApiEndpoint { get; }

    /// <summary>
    /// API密钥
    /// </summary>
    public string? ApiKey { get; }

    /// <summary>
    /// 模型版本
    /// </summary>
    public string? Version { get; }

    /// <summary>
    /// 是否启用
    /// </summary>
    public bool IsEnabled { get; }

    /// <summary>
    /// 初始化模型配置
    /// </summary>
    /// <param name="provider">模型提供商</param>
    /// <param name="modelName">模型名称</param>
    /// <param name="apiEndpoint">API端点</param>
    /// <param name="apiKey">API密钥</param>
    /// <param name="version">模型版本</param>
    /// <param name="isEnabled">是否启用</param>
    private ModelConfiguration(
        string provider,
        string modelName,
        string? apiEndpoint,
        string? apiKey,
        string? version,
        bool isEnabled)
    {
        Provider = provider;
        ModelName = modelName;
        ApiEndpoint = apiEndpoint;
        ApiKey = apiKey;
        Version = version;
        IsEnabled = isEnabled;
    }

    /// <summary>
    /// 创建模型配置
    /// </summary>
    /// <param name="provider">模型提供商</param>
    /// <param name="modelName">模型名称</param>
    /// <param name="apiEndpoint">API端点</param>
    /// <param name="apiKey">API密钥</param>
    /// <param name="version">模型版本</param>
    /// <param name="isEnabled">是否启用</param>
    /// <returns>模型配置值对象</returns>
    public static ModelConfiguration Create(
        string provider,
        string modelName,
        string? apiEndpoint = null,
        string? apiKey = null,
        string? version = null,
        bool isEnabled = true)
    {
        if (string.IsNullOrWhiteSpace(provider))
            throw new ArgumentException("模型提供商不能为空", nameof(provider));

        if (string.IsNullOrWhiteSpace(modelName))
            throw new ArgumentException("模型名称不能为空", nameof(modelName));

        return new ModelConfiguration(
            provider.Trim(),
            modelName.Trim(),
            apiEndpoint?.Trim(),
            apiKey?.Trim(),
            version?.Trim(),
            isEnabled);
    }

    /// <summary>
    /// 创建OpenAI配置
    /// </summary>
    /// <param name="modelName">模型名称</param>
    /// <param name="apiKey">API密钥</param>
    /// <param name="apiEndpoint">API端点</param>
    /// <returns>OpenAI模型配置</returns>
    public static ModelConfiguration CreateOpenAI(string modelName, string? apiKey = null, string? apiEndpoint = null)
    {
        return Create("OpenAI", modelName, apiEndpoint ?? "https://api.openai.com/v1", apiKey);
    }

    /// <summary>
    /// 创建Azure OpenAI配置
    /// </summary>
    /// <param name="deploymentName">部署名称</param>
    /// <param name="apiKey">API密钥</param>
    /// <param name="endpoint">Azure端点</param>
    /// <param name="apiVersion">API版本</param>
    /// <returns>Azure OpenAI模型配置</returns>
    public static ModelConfiguration CreateAzureOpenAI(
        string deploymentName, 
        string apiKey, 
        string endpoint, 
        string apiVersion = "2024-02-01")
    {
        if (string.IsNullOrWhiteSpace(endpoint))
            throw new ArgumentException("Azure端点不能为空", nameof(endpoint));

        return Create("AzureOpenAI", deploymentName, endpoint, apiKey, apiVersion);
    }

    /// <summary>
    /// 创建Anthropic配置
    /// </summary>
    /// <param name="modelName">模型名称</param>
    /// <param name="apiKey">API密钥</param>
    /// <returns>Anthropic模型配置</returns>
    public static ModelConfiguration CreateAnthropic(string modelName, string? apiKey = null)
    {
        return Create("Anthropic", modelName, "https://api.anthropic.com", apiKey);
    }

    /// <summary>
    /// 创建Dify配置
    /// </summary>
    /// <param name="appId">应用ID</param>
    /// <param name="apiKey">API密钥</param>
    /// <param name="baseUrl">基础URL</param>
    /// <returns>Dify模型配置</returns>
    public static ModelConfiguration CreateDify(string appId, string apiKey, string baseUrl)
    {
        if (string.IsNullOrWhiteSpace(baseUrl))
            throw new ArgumentException("Dify基础URL不能为空", nameof(baseUrl));

        return Create("Dify", appId, baseUrl, apiKey);
    }

    /// <summary>
    /// 更新API密钥
    /// </summary>
    /// <param name="apiKey">新的API密钥</param>
    /// <returns>新的配置对象</returns>
    public ModelConfiguration WithApiKey(string? apiKey)
    {
        return Create(Provider, ModelName, ApiEndpoint, apiKey, Version, IsEnabled);
    }

    /// <summary>
    /// 更新启用状态
    /// </summary>
    /// <param name="isEnabled">是否启用</param>
    /// <returns>新的配置对象</returns>
    public ModelConfiguration WithEnabled(bool isEnabled)
    {
        return Create(Provider, ModelName, ApiEndpoint, ApiKey, Version, isEnabled);
    }

    /// <summary>
    /// 获取掩码后的API密钥
    /// </summary>
    /// <returns>掩码后的API密钥</returns>
    public string? GetMaskedApiKey()
    {
        if (string.IsNullOrEmpty(ApiKey))
            return null;

        if (ApiKey.Length <= 8)
            return new string('*', ApiKey.Length);

        return $"{ApiKey[..4]}{'*'.ToString().PadLeft(ApiKey.Length - 8, '*')}{ApiKey[^4..]}";
    }

    /// <summary>
    /// 检查配置是否完整
    /// </summary>
    /// <returns>是否完整</returns>
    public bool IsComplete()
    {
        return !string.IsNullOrWhiteSpace(Provider) &&
               !string.IsNullOrWhiteSpace(ModelName) &&
               IsEnabled;
    }

    /// <summary>
    /// 获取相等性组件
    /// </summary>
    /// <returns>相等性组件集合</returns>
    protected override IEnumerable<object?> GetEqualityComponents()
    {
        yield return Provider;
        yield return ModelName;
        yield return ApiEndpoint;
        yield return ApiKey;
        yield return Version;
        yield return IsEnabled;
    }

    /// <summary>
    /// 转换为字符串
    /// </summary>
    /// <returns>字符串表示</returns>
    public override string ToString()
    {
        var maskedKey = GetMaskedApiKey();
        return $"{Provider}/{ModelName}" + 
               (string.IsNullOrEmpty(Version) ? "" : $":{Version}") +
               (string.IsNullOrEmpty(maskedKey) ? "" : $" (Key: {maskedKey})");
    }
}
