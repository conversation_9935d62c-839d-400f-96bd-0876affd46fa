<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <RootNamespace>Whimlab.AI.Agent.Platform.Application.Identity</RootNamespace>
    <AssemblyName>Whimlab.AI.Agent.Platform.Application.Identity</AssemblyName>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="../Whimlab.AI.Agent.Platform.Application.Core/Whimlab.AI.Agent.Platform.Application.Core.csproj" />
    <ProjectReference Include="../../03-Domain/Whimlab.AI.Agent.Platform.Domain.Identity/Whimlab.AI.Agent.Platform.Domain.Identity.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="MediatR" />
    <PackageReference Include="FluentValidation" />
    <PackageReference Include="FluentValidation.DependencyInjectionExtensions" />
    <PackageReference Include="AutoMapper" />
    <PackageReference Include="AutoMapper.Extensions.Microsoft.DependencyInjection" />
  </ItemGroup>

</Project>
