using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System.Linq.Expressions;
using Whimlab.AI.Agent.Platform.Domain.Agent.Entities;
using Whimlab.AI.Agent.Platform.Domain.Agent.Repositories;
using Whimlab.AI.Agent.Platform.Infrastructure.Data;
using Whimlab.AI.Agent.Platform.Shared.Common.Enums;

namespace Whimlab.AI.Agent.Platform.Infrastructure.Repositories;

/// <summary>
/// 智能体仓储实现
/// </summary>
public class AgentRepository : BaseRepository<Domain.Agent.Entities.Agent, Guid>, IAgentRepository
{
    /// <summary>
    /// 初始化智能体仓储
    /// </summary>
    /// <param name="context">数据库上下文</param>
    /// <param name="logger">日志记录器</param>
    public AgentRepository(
        AgentPlatformDbContext context,
        ILogger<AgentRepository> logger) : base(context, logger)
    {
    }

    /// <summary>
    /// 根据创建者ID获取智能体列表
    /// </summary>
    /// <param name="createdBy">创建者ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>智能体列表</returns>
    public async Task<List<Domain.Agent.Entities.Agent>> GetByCreatedByAsync(Guid createdBy, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("根据创建者ID获取智能体列表: {CreatedBy}", createdBy);

            return await _dbSet
                .Where(a => a.CreatedBy == createdBy)
                .OrderByDescending(a => a.CreatedAt)
                .ToListAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "根据创建者ID获取智能体列表时发生错误: {CreatedBy}", createdBy);
            throw;
        }
    }

    /// <summary>
    /// 根据状态获取智能体列表
    /// </summary>
    /// <param name="status">智能体状态</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>智能体列表</returns>
    public async Task<List<Domain.Agent.Entities.Agent>> GetByStatusAsync(AgentStatus status, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("根据状态获取智能体列表: {Status}", status);

            return await _dbSet
                .Where(a => a.Status == status)
                .OrderByDescending(a => a.CreatedAt)
                .ToListAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "根据状态获取智能体列表时发生错误: {Status}", status);
            throw;
        }
    }

    /// <summary>
    /// 获取已发布的智能体列表
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>已发布的智能体列表</returns>
    public async Task<List<Domain.Agent.Entities.Agent>> GetPublishedAgentsAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("获取已发布的智能体列表");

            return await _dbSet
                .Where(a => a.Status == AgentStatus.Published)
                .OrderByDescending(a => a.PublishedAt)
                .ToListAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取已发布的智能体列表时发生错误");
            throw;
        }
    }

    /// <summary>
    /// 根据类别获取智能体列表
    /// </summary>
    /// <param name="category">智能体类别</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>智能体列表</returns>
    public async Task<List<Domain.Agent.Entities.Agent>> GetByCategoryAsync(string category, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("根据类别获取智能体列表: {Category}", category);

            return await _dbSet
                .Where(a => a.Category == category && a.Status == AgentStatus.Published)
                .OrderByDescending(a => a.PublishedAt)
                .ToListAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "根据类别获取智能体列表时发生错误: {Category}", category);
            throw;
        }
    }

    /// <summary>
    /// 根据名称获取智能体
    /// </summary>
    /// <param name="name">智能体名称</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>智能体</returns>
    public async Task<Domain.Agent.Entities.Agent?> GetByNameAsync(string name, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("根据名称获取智能体: {Name}", name);

            return await _dbSet
                .FirstOrDefaultAsync(a => a.Name == name, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "根据名称获取智能体时发生错误: {Name}", name);
            throw;
        }
    }

    /// <summary>
    /// 检查智能体名称是否存在
    /// </summary>
    /// <param name="name">智能体名称</param>
    /// <param name="excludeId">排除的智能体ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>是否存在</returns>
    public async Task<bool> IsNameExistsAsync(string name, Guid? excludeId = null, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("检查智能体名称是否存在: {Name}, 排除ID: {ExcludeId}", name, excludeId);

            var query = _dbSet.Where(a => a.Name == name);

            if (excludeId.HasValue)
            {
                query = query.Where(a => a.Id != excludeId.Value);
            }

            return await query.AnyAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "检查智能体名称是否存在时发生错误: {Name}", name);
            throw;
        }
    }



    /// <summary>
    /// 搜索智能体
    /// </summary>
    /// <param name="searchTerm">搜索词</param>
    /// <param name="category">类别过滤</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>匹配的智能体列表</returns>
    public async Task<List<Domain.Agent.Entities.Agent>> SearchAgentsAsync(
        string searchTerm,
        string? category = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("搜索智能体: {SearchTerm}, 类别: {Category}", searchTerm, category);

            if (string.IsNullOrWhiteSpace(searchTerm))
            {
                return new List<Domain.Agent.Entities.Agent>();
            }

            var searchTermLower = searchTerm.ToLower();

            var query = _dbSet
                .Where(a => a.Status == AgentStatus.Published)
                .Where(a => a.Name.ToLower().Contains(searchTermLower) ||
                           a.Description.ToLower().Contains(searchTermLower));

            if (!string.IsNullOrWhiteSpace(category))
            {
                query = query.Where(a => a.Category == category);
            }

            return await query
                .OrderByDescending(a => a.PublishedAt)
                .ToListAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "搜索智能体时发生错误: {SearchTerm}, 类别: {Category}", searchTerm, category);
            throw;
        }
    }

    /// <summary>
    /// 获取热门智能体列表
    /// </summary>
    /// <param name="count">数量</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>热门智能体列表</returns>
    public async Task<IEnumerable<Domain.Agent.Entities.Agent>> GetPopularAgentsAsync(int count = 10, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("获取热门智能体列表，数量: {Count}", count);

            // 这里应该根据使用统计来排序，暂时按发布时间排序
            return await _dbSet
                .Where(a => a.Status == AgentStatus.Published)
                .OrderByDescending(a => a.PublishedAt)
                .Take(count)
                .ToListAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取热门智能体列表时发生错误，数量: {Count}", count);
            throw;
        }
    }

    /// <summary>
    /// 获取智能体统计信息
    /// </summary>
    /// <param name="createdBy">创建者ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>智能体统计信息</returns>
    public async Task<AgentStatistics> GetStatisticsAsync(Guid createdBy, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("获取智能体统计信息: {CreatedBy}", createdBy);

            var agents = await _dbSet
                .Where(a => a.CreatedBy == createdBy)
                .ToListAsync(cancellationToken);

            var totalAgents = agents.Count;
            var publishedAgents = agents.Count(a => a.Status == AgentStatus.Published);
            var draftAgents = agents.Count(a => a.Status == AgentStatus.Draft);

            return new AgentStatistics
            {
                TotalCount = totalAgents,
                PublishedCount = publishedAgents,
                DraftCount = draftAgents,
                ArchivedCount = agents.Count(a => a.Status == AgentStatus.Archived),
                TotalUsageCount = agents.Sum(a => a.UsageCount),
                AverageRating = agents.Where(a => a.RatingCount > 0).Average(a => a.Rating),
                CountByType = agents
                    .GroupBy(a => a.AgentType)
                    .ToDictionary(g => g.Key, g => g.Count())
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取智能体统计信息时发生错误: {CreatedBy}", createdBy);
            throw;
        }
    }
}
