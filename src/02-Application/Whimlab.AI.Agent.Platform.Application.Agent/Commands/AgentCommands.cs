using Whimlab.AI.Agent.Platform.Application.Core.Common;
using Whimlab.AI.Agent.Platform.Application.Agent.DTOs;
using Whimlab.AI.Agent.Platform.Shared.Common.Enums;

namespace Whimlab.AI.Agent.Platform.Application.Agent.Commands;

/// <summary>
/// 创建智能体命令
/// </summary>
public class CreateAgentCommand : ICommand<AgentDto>
{
    /// <summary>
    /// 智能体名称
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 智能体描述
    /// </summary>
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// 智能体图标URL
    /// </summary>
    public string? IconUrl { get; set; }

    /// <summary>
    /// 智能体类型
    /// </summary>
    public AgentType AgentType { get; set; }

    /// <summary>
    /// 分类
    /// </summary>
    public string? Category { get; set; }

    /// <summary>
    /// 是否公开
    /// </summary>
    public bool IsPublic { get; set; }

    /// <summary>
    /// 创建者ID
    /// </summary>
    public Guid CreatedBy { get; set; }

    /// <summary>
    /// 配置信息
    /// </summary>
    public AgentConfigurationDto Configuration { get; set; } = new();

    /// <summary>
    /// 标签列表
    /// </summary>
    public List<string> Tags { get; set; } = new();
}

/// <summary>
/// 更新智能体命令
/// </summary>
public class UpdateAgentCommand : ICommand<AgentDto>
{
    /// <summary>
    /// 智能体ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// 智能体名称
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 智能体描述
    /// </summary>
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// 智能体图标URL
    /// </summary>
    public string? IconUrl { get; set; }

    /// <summary>
    /// 分类
    /// </summary>
    public string? Category { get; set; }

    /// <summary>
    /// 是否公开
    /// </summary>
    public bool IsPublic { get; set; }

    /// <summary>
    /// 操作者ID
    /// </summary>
    public Guid OperatorId { get; set; }
}

/// <summary>
/// 更新智能体配置命令
/// </summary>
public class UpdateAgentConfigurationCommand : ICommand<AgentDto>
{
    /// <summary>
    /// 智能体ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// 配置信息
    /// </summary>
    public AgentConfigurationDto Configuration { get; set; } = new();

    /// <summary>
    /// 版本说明
    /// </summary>
    public string? VersionNotes { get; set; }

    /// <summary>
    /// 操作者ID
    /// </summary>
    public Guid OperatorId { get; set; }
}

/// <summary>
/// 发布智能体命令
/// </summary>
public class PublishAgentCommand : ICommand
{
    /// <summary>
    /// 智能体ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// 操作者ID
    /// </summary>
    public Guid OperatorId { get; set; }
}

/// <summary>
/// 归档智能体命令
/// </summary>
public class ArchiveAgentCommand : ICommand
{
    /// <summary>
    /// 智能体ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// 操作者ID
    /// </summary>
    public Guid OperatorId { get; set; }
}

/// <summary>
/// 恢复智能体命令
/// </summary>
public class RestoreAgentCommand : ICommand
{
    /// <summary>
    /// 智能体ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// 操作者ID
    /// </summary>
    public Guid OperatorId { get; set; }
}

/// <summary>
/// 删除智能体命令
/// </summary>
public class DeleteAgentCommand : ICommand
{
    /// <summary>
    /// 智能体ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// 操作者ID
    /// </summary>
    public Guid OperatorId { get; set; }
}

/// <summary>
/// 添加智能体标签命令
/// </summary>
public class AddAgentTagsCommand : ICommand
{
    /// <summary>
    /// 智能体ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// 标签列表
    /// </summary>
    public List<string> Tags { get; set; } = new();

    /// <summary>
    /// 操作者ID
    /// </summary>
    public Guid OperatorId { get; set; }
}

/// <summary>
/// 移除智能体标签命令
/// </summary>
public class RemoveAgentTagsCommand : ICommand
{
    /// <summary>
    /// 智能体ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// 标签列表
    /// </summary>
    public List<string> Tags { get; set; } = new();

    /// <summary>
    /// 操作者ID
    /// </summary>
    public Guid OperatorId { get; set; }
}

/// <summary>
/// 记录智能体使用命令
/// </summary>
public class RecordAgentUsageCommand : ICommand
{
    /// <summary>
    /// 智能体ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// 用户ID
    /// </summary>
    public Guid UserId { get; set; }
}

/// <summary>
/// 智能体评分命令
/// </summary>
public class RateAgentCommand : ICommand
{
    /// <summary>
    /// 智能体ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// 评分（1-5）
    /// </summary>
    public int Score { get; set; }

    /// <summary>
    /// 用户ID
    /// </summary>
    public Guid UserId { get; set; }

    /// <summary>
    /// 评价内容
    /// </summary>
    public string? Comment { get; set; }
}
