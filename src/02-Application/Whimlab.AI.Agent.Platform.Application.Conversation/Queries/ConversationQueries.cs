using Whimlab.AI.Agent.Platform.Application.Core.Common;
using Whimlab.AI.Agent.Platform.Application.Conversation.DTOs;
using Whimlab.AI.Agent.Platform.Domain.Core.Common;
using Whimlab.AI.Agent.Platform.Shared.Common.Enums;

namespace Whimlab.AI.Agent.Platform.Application.Conversation.Queries;

/// <summary>
/// 根据ID获取对话查询
/// </summary>
public class GetConversationByIdQuery : IQuery<ConversationDto?>
{
    /// <summary>
    /// 对话ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// 是否包含消息
    /// </summary>
    public bool IncludeMessages { get; set; } = true;

    /// <summary>
    /// 消息数量限制
    /// </summary>
    public int? MessageLimit { get; set; }

    /// <summary>
    /// 初始化查询
    /// </summary>
    /// <param name="id">对话ID</param>
    /// <param name="includeMessages">是否包含消息</param>
    /// <param name="messageLimit">消息数量限制</param>
    public GetConversationByIdQuery(Guid id, bool includeMessages = true, int? messageLimit = null)
    {
        Id = id;
        IncludeMessages = includeMessages;
        MessageLimit = messageLimit;
    }
}

/// <summary>
/// 获取用户对话列表查询
/// </summary>
public class GetUserConversationsQuery : PagedQuery, IQuery<PagedResult<ConversationSummaryDto>>
{
    /// <summary>
    /// 用户ID
    /// </summary>
    public Guid UserId { get; set; }

    /// <summary>
    /// 对话状态过滤
    /// </summary>
    public ConversationStatus? Status { get; set; }

    /// <summary>
    /// 智能体ID过滤
    /// </summary>
    public Guid? AgentId { get; set; }

    /// <summary>
    /// 标签过滤
    /// </summary>
    public List<string> Tags { get; set; } = new();

    /// <summary>
    /// 创建时间范围开始
    /// </summary>
    public DateTime? CreatedFrom { get; set; }

    /// <summary>
    /// 创建时间范围结束
    /// </summary>
    public DateTime? CreatedTo { get; set; }

    /// <summary>
    /// 初始化查询
    /// </summary>
    /// <param name="userId">用户ID</param>
    public GetUserConversationsQuery(Guid userId)
    {
        UserId = userId;
    }
}

/// <summary>
/// 搜索对话查询
/// </summary>
public class SearchConversationsQuery : PagedQuery, IQuery<PagedResult<ConversationSummaryDto>>
{
    /// <summary>
    /// 用户ID
    /// </summary>
    public Guid UserId { get; set; }

    /// <summary>
    /// 搜索关键词
    /// </summary>
    public string Query { get; set; } = string.Empty;

    /// <summary>
    /// 对话状态过滤
    /// </summary>
    public ConversationStatus? Status { get; set; }

    /// <summary>
    /// 智能体ID过滤
    /// </summary>
    public Guid? AgentId { get; set; }

    /// <summary>
    /// 标签过滤
    /// </summary>
    public List<string> Tags { get; set; } = new();

    /// <summary>
    /// 初始化查询
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="query">搜索关键词</param>
    public SearchConversationsQuery(Guid userId, string query)
    {
        UserId = userId;
        Query = query;
    }
}

/// <summary>
/// 获取对话消息查询
/// </summary>
public class GetConversationMessagesQuery : PagedQuery, IQuery<PagedResult<MessageDto>>
{
    /// <summary>
    /// 对话ID
    /// </summary>
    public Guid ConversationId { get; set; }

    /// <summary>
    /// 消息角色过滤
    /// </summary>
    public MessageRole? Role { get; set; }

    /// <summary>
    /// 消息类型过滤
    /// </summary>
    public MessageType? MessageType { get; set; }

    /// <summary>
    /// 是否包含附件
    /// </summary>
    public bool IncludeAttachments { get; set; } = true;

    /// <summary>
    /// 初始化查询
    /// </summary>
    /// <param name="conversationId">对话ID</param>
    public GetConversationMessagesQuery(Guid conversationId)
    {
        ConversationId = conversationId;
    }
}

/// <summary>
/// 根据ID获取消息查询
/// </summary>
public class GetMessageByIdQuery : IQuery<MessageDto?>
{
    /// <summary>
    /// 消息ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// 是否包含附件
    /// </summary>
    public bool IncludeAttachments { get; set; } = true;

    /// <summary>
    /// 初始化查询
    /// </summary>
    /// <param name="id">消息ID</param>
    /// <param name="includeAttachments">是否包含附件</param>
    public GetMessageByIdQuery(Guid id, bool includeAttachments = true)
    {
        Id = id;
        IncludeAttachments = includeAttachments;
    }
}

/// <summary>
/// 获取Token使用统计查询
/// </summary>
public class GetTokenUsageStatisticsQuery : IQuery<TokenUsageStatisticsDto>
{
    /// <summary>
    /// 用户ID
    /// </summary>
    public Guid UserId { get; set; }

    /// <summary>
    /// 统计开始日期
    /// </summary>
    public DateTime StartDate { get; set; }

    /// <summary>
    /// 统计结束日期
    /// </summary>
    public DateTime EndDate { get; set; }

    /// <summary>
    /// 智能体ID过滤
    /// </summary>
    public Guid? AgentId { get; set; }

    /// <summary>
    /// 初始化查询
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="startDate">开始日期</param>
    /// <param name="endDate">结束日期</param>
    public GetTokenUsageStatisticsQuery(Guid userId, DateTime startDate, DateTime endDate)
    {
        UserId = userId;
        StartDate = startDate;
        EndDate = endDate;
    }
}

/// <summary>
/// 获取对话统计查询
/// </summary>
public class GetConversationStatisticsQuery : IQuery<ConversationStatisticsDto>
{
    /// <summary>
    /// 用户ID（可选，为空则获取全局统计）
    /// </summary>
    public Guid? UserId { get; set; }

    /// <summary>
    /// 统计开始日期
    /// </summary>
    public DateTime StartDate { get; set; }

    /// <summary>
    /// 统计结束日期
    /// </summary>
    public DateTime EndDate { get; set; }

    /// <summary>
    /// 初始化查询
    /// </summary>
    /// <param name="startDate">开始日期</param>
    /// <param name="endDate">结束日期</param>
    /// <param name="userId">用户ID</param>
    public GetConversationStatisticsQuery(DateTime startDate, DateTime endDate, Guid? userId = null)
    {
        StartDate = startDate;
        EndDate = endDate;
        UserId = userId;
    }
}

/// <summary>
/// 获取热门对话查询
/// </summary>
public class GetPopularConversationsQuery : IQuery<IEnumerable<ConversationSummaryDto>>
{
    /// <summary>
    /// 用户ID
    /// </summary>
    public Guid UserId { get; set; }

    /// <summary>
    /// 数量限制
    /// </summary>
    public int Count { get; set; } = 10;

    /// <summary>
    /// 时间范围（天数）
    /// </summary>
    public int Days { get; set; } = 30;

    /// <summary>
    /// 初始化查询
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="count">数量限制</param>
    /// <param name="days">时间范围</param>
    public GetPopularConversationsQuery(Guid userId, int count = 10, int days = 30)
    {
        UserId = userId;
        Count = count;
        Days = days;
    }
}

/// <summary>
/// 获取最近对话查询
/// </summary>
public class GetRecentConversationsQuery : IQuery<IEnumerable<ConversationSummaryDto>>
{
    /// <summary>
    /// 用户ID
    /// </summary>
    public Guid UserId { get; set; }

    /// <summary>
    /// 数量限制
    /// </summary>
    public int Count { get; set; } = 10;

    /// <summary>
    /// 初始化查询
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="count">数量限制</param>
    public GetRecentConversationsQuery(Guid userId, int count = 10)
    {
        UserId = userId;
        Count = count;
    }
}

/// <summary>
/// 获取对话标签查询
/// </summary>
public class GetConversationTagsQuery : IQuery<IEnumerable<string>>
{
    /// <summary>
    /// 用户ID
    /// </summary>
    public Guid UserId { get; set; }

    /// <summary>
    /// 搜索关键词
    /// </summary>
    public string? SearchTerm { get; set; }

    /// <summary>
    /// 数量限制
    /// </summary>
    public int? Limit { get; set; }

    /// <summary>
    /// 初始化查询
    /// </summary>
    /// <param name="userId">用户ID</param>
    public GetConversationTagsQuery(Guid userId)
    {
        UserId = userId;
    }
}
