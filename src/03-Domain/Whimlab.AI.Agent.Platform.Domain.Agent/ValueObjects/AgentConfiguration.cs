using System.Text.Json;
using Whimlab.AI.Agent.Platform.Domain.Core.Common;
using Whimlab.AI.Agent.Platform.Shared.Common.Enums;

namespace Whimlab.AI.Agent.Platform.Domain.Agent.ValueObjects;

/// <summary>
/// 智能体配置值对象
/// </summary>
public sealed class AgentConfiguration : ValueObject
{
    /// <summary>
    /// 智能体类型
    /// </summary>
    public AgentType AgentType { get; }

    /// <summary>
    /// 模型配置
    /// </summary>
    public ModelConfiguration ModelConfig { get; }

    /// <summary>
    /// 系统提示词
    /// </summary>
    public string SystemPrompt { get; }

    /// <summary>
    /// 温度参数
    /// </summary>
    public double Temperature { get; }

    /// <summary>
    /// 最大令牌数
    /// </summary>
    public int MaxTokens { get; }

    /// <summary>
    /// Top-P 参数
    /// </summary>
    public double TopP { get; }

    /// <summary>
    /// 频率惩罚
    /// </summary>
    public double FrequencyPenalty { get; }

    /// <summary>
    /// 存在惩罚
    /// </summary>
    public double PresencePenalty { get; }

    /// <summary>
    /// 停止序列
    /// </summary>
    public IReadOnlyList<string> StopSequences { get; }

    /// <summary>
    /// 工具配置
    /// </summary>
    public IReadOnlyList<ToolConfiguration> Tools { get; }

    /// <summary>
    /// 扩展配置（JSON格式）
    /// </summary>
    public string? ExtendedConfig { get; }

    /// <summary>
    /// 初始化智能体配置
    /// </summary>
    /// <param name="agentType">智能体类型</param>
    /// <param name="modelConfig">模型配置</param>
    /// <param name="systemPrompt">系统提示词</param>
    /// <param name="temperature">温度参数</param>
    /// <param name="maxTokens">最大令牌数</param>
    /// <param name="topP">Top-P 参数</param>
    /// <param name="frequencyPenalty">频率惩罚</param>
    /// <param name="presencePenalty">存在惩罚</param>
    /// <param name="stopSequences">停止序列</param>
    /// <param name="tools">工具配置</param>
    /// <param name="extendedConfig">扩展配置</param>
    private AgentConfiguration(
        AgentType agentType,
        ModelConfiguration modelConfig,
        string systemPrompt,
        double temperature,
        int maxTokens,
        double topP,
        double frequencyPenalty,
        double presencePenalty,
        IEnumerable<string> stopSequences,
        IEnumerable<ToolConfiguration> tools,
        string? extendedConfig)
    {
        AgentType = agentType;
        ModelConfig = modelConfig;
        SystemPrompt = systemPrompt;
        Temperature = temperature;
        MaxTokens = maxTokens;
        TopP = topP;
        FrequencyPenalty = frequencyPenalty;
        PresencePenalty = presencePenalty;
        StopSequences = stopSequences.ToList().AsReadOnly();
        Tools = tools.ToList().AsReadOnly();
        ExtendedConfig = extendedConfig;
    }

    /// <summary>
    /// 创建智能体配置
    /// </summary>
    /// <param name="agentType">智能体类型</param>
    /// <param name="modelConfig">模型配置</param>
    /// <param name="systemPrompt">系统提示词</param>
    /// <param name="temperature">温度参数</param>
    /// <param name="maxTokens">最大令牌数</param>
    /// <param name="topP">Top-P 参数</param>
    /// <param name="frequencyPenalty">频率惩罚</param>
    /// <param name="presencePenalty">存在惩罚</param>
    /// <param name="stopSequences">停止序列</param>
    /// <param name="tools">工具配置</param>
    /// <param name="extendedConfig">扩展配置</param>
    /// <returns>智能体配置值对象</returns>
    public static AgentConfiguration Create(
        AgentType agentType,
        ModelConfiguration modelConfig,
        string systemPrompt,
        double temperature = 0.7,
        int maxTokens = 4096,
        double topP = 1.0,
        double frequencyPenalty = 0.0,
        double presencePenalty = 0.0,
        IEnumerable<string>? stopSequences = null,
        IEnumerable<ToolConfiguration>? tools = null,
        string? extendedConfig = null)
    {
        // 验证参数
        if (string.IsNullOrWhiteSpace(systemPrompt))
            throw new ArgumentException("系统提示词不能为空", nameof(systemPrompt));

        if (temperature < 0 || temperature > 2)
            throw new ArgumentException("温度参数必须在0-2之间", nameof(temperature));

        if (maxTokens <= 0 || maxTokens > 128000)
            throw new ArgumentException("最大令牌数必须在1-128000之间", nameof(maxTokens));

        if (topP < 0 || topP > 1)
            throw new ArgumentException("Top-P参数必须在0-1之间", nameof(topP));

        if (frequencyPenalty < -2 || frequencyPenalty > 2)
            throw new ArgumentException("频率惩罚必须在-2到2之间", nameof(frequencyPenalty));

        if (presencePenalty < -2 || presencePenalty > 2)
            throw new ArgumentException("存在惩罚必须在-2到2之间", nameof(presencePenalty));

        // 验证扩展配置JSON格式
        if (!string.IsNullOrWhiteSpace(extendedConfig))
        {
            try
            {
                JsonDocument.Parse(extendedConfig);
            }
            catch (JsonException)
            {
                throw new ArgumentException("扩展配置必须是有效的JSON格式", nameof(extendedConfig));
            }
        }

        return new AgentConfiguration(
            agentType,
            modelConfig,
            systemPrompt.Trim(),
            temperature,
            maxTokens,
            topP,
            frequencyPenalty,
            presencePenalty,
            stopSequences ?? Enumerable.Empty<string>(),
            tools ?? Enumerable.Empty<ToolConfiguration>(),
            extendedConfig);
    }

    /// <summary>
    /// 更新系统提示词
    /// </summary>
    /// <param name="systemPrompt">新的系统提示词</param>
    /// <returns>新的配置对象</returns>
    public AgentConfiguration WithSystemPrompt(string systemPrompt)
    {
        return Create(AgentType, ModelConfig, systemPrompt, Temperature, MaxTokens, TopP, 
                     FrequencyPenalty, PresencePenalty, StopSequences, Tools, ExtendedConfig);
    }

    /// <summary>
    /// 更新模型参数
    /// </summary>
    /// <param name="temperature">温度参数</param>
    /// <param name="maxTokens">最大令牌数</param>
    /// <param name="topP">Top-P 参数</param>
    /// <param name="frequencyPenalty">频率惩罚</param>
    /// <param name="presencePenalty">存在惩罚</param>
    /// <returns>新的配置对象</returns>
    public AgentConfiguration WithModelParameters(
        double? temperature = null,
        int? maxTokens = null,
        double? topP = null,
        double? frequencyPenalty = null,
        double? presencePenalty = null)
    {
        return Create(
            AgentType,
            ModelConfig,
            SystemPrompt,
            temperature ?? Temperature,
            maxTokens ?? MaxTokens,
            topP ?? TopP,
            frequencyPenalty ?? FrequencyPenalty,
            presencePenalty ?? PresencePenalty,
            StopSequences,
            Tools,
            ExtendedConfig);
    }

    /// <summary>
    /// 添加工具
    /// </summary>
    /// <param name="tool">工具配置</param>
    /// <returns>新的配置对象</returns>
    public AgentConfiguration AddTool(ToolConfiguration tool)
    {
        var newTools = Tools.ToList();
        newTools.Add(tool);
        
        return Create(AgentType, ModelConfig, SystemPrompt, Temperature, MaxTokens, TopP,
                     FrequencyPenalty, PresencePenalty, StopSequences, newTools, ExtendedConfig);
    }

    /// <summary>
    /// 移除工具
    /// </summary>
    /// <param name="toolName">工具名称</param>
    /// <returns>新的配置对象</returns>
    public AgentConfiguration RemoveTool(string toolName)
    {
        var newTools = Tools.Where(t => t.Name != toolName).ToList();
        
        return Create(AgentType, ModelConfig, SystemPrompt, Temperature, MaxTokens, TopP,
                     FrequencyPenalty, PresencePenalty, StopSequences, newTools, ExtendedConfig);
    }

    /// <summary>
    /// 获取相等性组件
    /// </summary>
    /// <returns>相等性组件集合</returns>
    protected override IEnumerable<object?> GetEqualityComponents()
    {
        yield return AgentType;
        yield return ModelConfig;
        yield return SystemPrompt;
        yield return Temperature;
        yield return MaxTokens;
        yield return TopP;
        yield return FrequencyPenalty;
        yield return PresencePenalty;
        
        foreach (var stopSequence in StopSequences)
            yield return stopSequence;
            
        foreach (var tool in Tools)
            yield return tool;
            
        yield return ExtendedConfig;
    }
}
