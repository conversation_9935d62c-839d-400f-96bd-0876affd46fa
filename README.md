# Whimlab AI Agent Platform

企业级AI智能体平台，支持多种AI模型集成和智能体管理。

## 🚀 功能特性

### 核心功能
- **智能体管理**: 创建、配置和管理多种类型的AI智能体
- **实时对话**: 基于SignalR的实时对话系统，支持流式响应
- **用户管理**: 完整的用户认证、授权和权限管理系统
- **订阅管理**: 灵活的订阅套餐和配额管理
- **支付集成**: 支持支付宝、微信支付等多种支付方式
- **数据分析**: 全面的使用统计和业务分析

### AI集成
- **Semantic Kernel**: 支持OpenAI、Azure OpenAI、Anthropic等多种模型
- **Dify平台**: 集成Dify的Chat、Completion、Agent和Workflow功能
- **智能路由**: 自动选择最优模型和负载均衡

### 技术架构
- **架构模式**: 模块化单体 + DDD + CQRS + 事件驱动
- **前端技术**: Blazor Auto模式 + MudBlazor UI组件库
- **后端技术**: .NET 9.0 + ASP.NET Core + Entity Framework Core
- **数据存储**: PostgreSQL + Redis + MongoDB + Elasticsearch
- **消息队列**: RabbitMQ + 内存事件总线
- **监控日志**: Serilog + OpenTelemetry + 健康检查

## 🏗️ 项目结构

```
Whimlab.AI/
├── src/
│   ├── 01-Presentation/          # 表现层
│   │   ├── Web.Api/              # RESTful API服务
│   │   ├── Web.SignalR/          # 实时通信服务
│   │   ├── Client.Customer/      # 客户端前端
│   │   └── Client.Admin/         # 管理端前端
│   ├── 02-Application/           # 应用层
│   │   ├── Application.Core/     # 核心应用服务
│   │   ├── Application.Identity/ # 身份认证应用
│   │   ├── Application.Agent/    # 智能体应用
│   │   ├── Application.Conversation/ # 对话应用
│   │   ├── Application.Subscription/ # 订阅应用
│   │   ├── Application.Payment/  # 支付应用
│   │   └── Application.Analytics/ # 分析应用
│   ├── 03-Domain/                # 领域层
│   │   ├── Domain.Core/          # 核心领域
│   │   ├── Domain.Identity/      # 身份认证领域
│   │   ├── Domain.Agent/         # 智能体领域
│   │   ├── Domain.Conversation/  # 对话领域
│   │   ├── Domain.Subscription/  # 订阅领域
│   │   ├── Domain.Payment/       # 支付领域
│   │   └── Domain.Analytics/     # 分析领域
│   ├── 04-Infrastructure/        # 基础设施层
│   │   ├── Infrastructure.Data/  # 数据访问
│   │   ├── Infrastructure.AI/    # AI服务集成
│   │   ├── Infrastructure.Caching/ # 缓存服务
│   │   ├── Infrastructure.Messaging/ # 消息服务
│   │   ├── Infrastructure.Payment/ # 支付服务
│   │   ├── Infrastructure.Storage/ # 文件存储
│   │   ├── Infrastructure.Monitoring/ # 监控服务
│   │   ├── Infrastructure.Identity/ # 身份认证基础设施
│   │   └── Infrastructure.Notifications/ # 通知服务
│   └── 05-Shared/                # 共享库
│       ├── Shared.Common/        # 通用共享
│       ├── Shared.Contracts/     # 契约定义
│       ├── Shared.Exceptions/    # 异常定义
│       └── Shared.Configuration/ # 配置模型
├── tests/                        # 测试项目
│   ├── 01-UnitTests/            # 单元测试
│   ├── 02-IntegrationTests/     # 集成测试
│   ├── 03-E2ETests/             # 端到端测试
│   ├── 04-PerformanceTests/     # 性能测试
│   └── 05-TestUtilities/        # 测试工具
├── tools/                        # 工具和脚本
│   ├── build/                   # 构建脚本
│   ├── deployment/              # 部署配置
│   ├── database/                # 数据库工具
│   └── monitoring/              # 监控配置
└── docs/                         # 文档
```

## 🛠️ 开发环境设置

### 前置要求
- .NET 9.0 SDK
- Docker Desktop
- Visual Studio 2022 或 VS Code
- PostgreSQL 16
- Redis 7.2
- MongoDB 7.0 (可选)
- RabbitMQ 3.12 (可选)

### 快速开始

1. **克隆项目**
   ```bash
   git clone <repository-url>
   cd Whimlab.AI
   ```

2. **启动基础设施服务**
   ```bash
   docker-compose up -d postgres redis
   ```

3. **恢复依赖**
   ```bash
   dotnet restore
   ```

4. **运行数据库迁移**
   ```bash
   dotnet ef database update --project src/04-Infrastructure/Whimlab.AI.Agent.Platform.Infrastructure.Data
   ```

5. **启动应用**
   ```bash
   dotnet run --project src/01-Presentation/Whimlab.AI.Agent.Platform.Web.Api
   ```

### 配置说明

主要配置文件位于 `appsettings.json`，包含以下配置项：

- **数据库连接**: PostgreSQL、Redis、MongoDB连接字符串
- **AI服务**: OpenAI、Azure OpenAI、Dify API配置
- **支付网关**: 支付宝、微信支付配置
- **JWT认证**: 密钥和过期时间配置
- **日志配置**: Serilog和OpenTelemetry配置

## 🧪 测试

```bash
# 运行所有测试
dotnet test

# 运行单元测试
dotnet test --filter "Category=Unit"

# 运行集成测试
dotnet test --filter "Category=Integration"

# 生成测试覆盖率报告
dotnet test --collect:"XPlat Code Coverage"
```

## 📦 部署

### Docker部署
```bash
# 构建镜像
docker build -t whimlab-ai-platform .

# 运行容器
docker-compose up -d
```

### Kubernetes部署
```bash
# 应用配置
kubectl apply -f tools/deployment/kubernetes/

# 检查状态
kubectl get pods -n whimlab-ai
```

## 📚 API文档

启动应用后，访问以下地址查看API文档：
- Swagger UI: `https://localhost:5001/swagger`
- ReDoc: `https://localhost:5001/redoc`

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 联系我们

- 项目主页: [GitHub Repository](https://github.com/whimlab/ai-agent-platform)
- 问题反馈: [Issues](https://github.com/whimlab/ai-agent-platform/issues)
- 邮箱: <EMAIL>

## 🙏 致谢

感谢所有为这个项目做出贡献的开发者和开源社区。
