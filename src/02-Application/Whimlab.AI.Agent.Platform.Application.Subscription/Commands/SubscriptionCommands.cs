using Whimlab.AI.Agent.Platform.Application.Core.Common;
using Whimlab.AI.Agent.Platform.Application.Subscription.DTOs;
using Whimlab.AI.Agent.Platform.Shared.Common.Enums;

namespace Whimlab.AI.Agent.Platform.Application.Subscription.Commands;

/// <summary>
/// 创建订阅计划命令
/// </summary>
public class CreateSubscriptionPlanCommand : ICommand<SubscriptionPlanDto>
{
    /// <summary>
    /// 计划名称
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 计划描述
    /// </summary>
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// 计划类型
    /// </summary>
    public PlanType PlanType { get; set; }

    /// <summary>
    /// 月费价格
    /// </summary>
    public decimal MonthlyPrice { get; set; }

    /// <summary>
    /// 年费价格
    /// </summary>
    public decimal YearlyPrice { get; set; }

    /// <summary>
    /// 是否公开
    /// </summary>
    public bool IsPublic { get; set; } = true;

    /// <summary>
    /// 排序顺序
    /// </summary>
    public int SortOrder { get; set; }

    /// <summary>
    /// 配额限制列表
    /// </summary>
    public List<CreateQuotaLimitDto> QuotaLimits { get; set; } = new();

    /// <summary>
    /// 功能列表
    /// </summary>
    public List<string> Features { get; set; } = new();

    /// <summary>
    /// 操作者ID
    /// </summary>
    public Guid OperatorId { get; set; }
}

/// <summary>
/// 更新订阅计划命令
/// </summary>
public class UpdateSubscriptionPlanCommand : ICommand<SubscriptionPlanDto>
{
    /// <summary>
    /// 计划ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// 计划名称
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 计划描述
    /// </summary>
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// 月费价格
    /// </summary>
    public decimal MonthlyPrice { get; set; }

    /// <summary>
    /// 年费价格
    /// </summary>
    public decimal YearlyPrice { get; set; }

    /// <summary>
    /// 是否公开
    /// </summary>
    public bool IsPublic { get; set; }

    /// <summary>
    /// 排序顺序
    /// </summary>
    public int SortOrder { get; set; }

    /// <summary>
    /// 功能列表
    /// </summary>
    public List<string> Features { get; set; } = new();

    /// <summary>
    /// 操作者ID
    /// </summary>
    public Guid OperatorId { get; set; }
}

/// <summary>
/// 激活订阅计划命令
/// </summary>
public class ActivateSubscriptionPlanCommand : ICommand
{
    /// <summary>
    /// 计划ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// 操作者ID
    /// </summary>
    public Guid OperatorId { get; set; }
}

/// <summary>
/// 停用订阅计划命令
/// </summary>
public class DeactivateSubscriptionPlanCommand : ICommand
{
    /// <summary>
    /// 计划ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// 操作者ID
    /// </summary>
    public Guid OperatorId { get; set; }
}

/// <summary>
/// 删除订阅计划命令
/// </summary>
public class DeleteSubscriptionPlanCommand : ICommand
{
    /// <summary>
    /// 计划ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// 操作者ID
    /// </summary>
    public Guid OperatorId { get; set; }
}

/// <summary>
/// 创建客户订阅命令
/// </summary>
public class CreateCustomerSubscriptionCommand : ICommand<CustomerSubscriptionDto>
{
    /// <summary>
    /// 客户ID
    /// </summary>
    public Guid CustomerId { get; set; }

    /// <summary>
    /// 订阅计划ID
    /// </summary>
    public Guid SubscriptionPlanId { get; set; }

    /// <summary>
    /// 计费周期
    /// </summary>
    public BillingCycle BillingCycle { get; set; }

    /// <summary>
    /// 开始时间
    /// </summary>
    public DateTime StartDate { get; set; }

    /// <summary>
    /// 试用天数
    /// </summary>
    public int? TrialDays { get; set; }

    /// <summary>
    /// 是否自动续费
    /// </summary>
    public bool AutoRenew { get; set; } = true;

    /// <summary>
    /// 促销代码
    /// </summary>
    public string? PromoCode { get; set; }

    /// <summary>
    /// 操作者ID
    /// </summary>
    public Guid OperatorId { get; set; }
}

/// <summary>
/// 升级客户订阅命令
/// </summary>
public class UpgradeCustomerSubscriptionCommand : ICommand<CustomerSubscriptionDto>
{
    /// <summary>
    /// 订阅ID
    /// </summary>
    public Guid SubscriptionId { get; set; }

    /// <summary>
    /// 新订阅计划ID
    /// </summary>
    public Guid NewPlanId { get; set; }

    /// <summary>
    /// 新计费周期
    /// </summary>
    public BillingCycle? NewBillingCycle { get; set; }

    /// <summary>
    /// 是否立即生效
    /// </summary>
    public bool EffectiveImmediately { get; set; } = true;

    /// <summary>
    /// 促销代码
    /// </summary>
    public string? PromoCode { get; set; }

    /// <summary>
    /// 操作者ID
    /// </summary>
    public Guid OperatorId { get; set; }
}

/// <summary>
/// 取消客户订阅命令
/// </summary>
public class CancelCustomerSubscriptionCommand : ICommand
{
    /// <summary>
    /// 订阅ID
    /// </summary>
    public Guid SubscriptionId { get; set; }

    /// <summary>
    /// 取消原因
    /// </summary>
    public string CancellationReason { get; set; } = string.Empty;

    /// <summary>
    /// 是否立即取消
    /// </summary>
    public bool CancelImmediately { get; set; } = false;

    /// <summary>
    /// 操作者ID
    /// </summary>
    public Guid OperatorId { get; set; }
}

/// <summary>
/// 续费客户订阅命令
/// </summary>
public class RenewCustomerSubscriptionCommand : ICommand<CustomerSubscriptionDto>
{
    /// <summary>
    /// 订阅ID
    /// </summary>
    public Guid SubscriptionId { get; set; }

    /// <summary>
    /// 续费周期数
    /// </summary>
    public int RenewalPeriods { get; set; } = 1;

    /// <summary>
    /// 促销代码
    /// </summary>
    public string? PromoCode { get; set; }

    /// <summary>
    /// 操作者ID
    /// </summary>
    public Guid OperatorId { get; set; }
}

/// <summary>
/// 暂停客户订阅命令
/// </summary>
public class SuspendCustomerSubscriptionCommand : ICommand
{
    /// <summary>
    /// 订阅ID
    /// </summary>
    public Guid SubscriptionId { get; set; }

    /// <summary>
    /// 暂停原因
    /// </summary>
    public string SuspensionReason { get; set; } = string.Empty;

    /// <summary>
    /// 暂停到期时间
    /// </summary>
    public DateTime? SuspensionEndDate { get; set; }

    /// <summary>
    /// 操作者ID
    /// </summary>
    public Guid OperatorId { get; set; }
}

/// <summary>
/// 恢复客户订阅命令
/// </summary>
public class ResumeCustomerSubscriptionCommand : ICommand
{
    /// <summary>
    /// 订阅ID
    /// </summary>
    public Guid SubscriptionId { get; set; }

    /// <summary>
    /// 操作者ID
    /// </summary>
    public Guid OperatorId { get; set; }
}

/// <summary>
/// 使用配额命令
/// </summary>
public class UseQuotaCommand : ICommand<bool>
{
    /// <summary>
    /// 客户ID
    /// </summary>
    public Guid CustomerId { get; set; }

    /// <summary>
    /// 配额类型
    /// </summary>
    public string QuotaType { get; set; } = string.Empty;

    /// <summary>
    /// 使用量
    /// </summary>
    public long Amount { get; set; }

    /// <summary>
    /// 使用描述
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// 操作者ID
    /// </summary>
    public Guid OperatorId { get; set; }
}

/// <summary>
/// 重置配额命令
/// </summary>
public class ResetQuotaCommand : ICommand
{
    /// <summary>
    /// 客户ID
    /// </summary>
    public Guid CustomerId { get; set; }

    /// <summary>
    /// 配额类型
    /// </summary>
    public string QuotaType { get; set; } = string.Empty;

    /// <summary>
    /// 操作者ID
    /// </summary>
    public Guid OperatorId { get; set; }
}

/// <summary>
/// 批量重置配额命令
/// </summary>
public class BatchResetQuotaCommand : ICommand<int>
{
    /// <summary>
    /// 配额类型
    /// </summary>
    public string QuotaType { get; set; } = string.Empty;

    /// <summary>
    /// 重置周期
    /// </summary>
    public string ResetPeriod { get; set; } = string.Empty;

    /// <summary>
    /// 批次大小
    /// </summary>
    public int BatchSize { get; set; } = 1000;

    /// <summary>
    /// 操作者ID
    /// </summary>
    public Guid OperatorId { get; set; }
}
