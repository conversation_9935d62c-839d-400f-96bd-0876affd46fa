using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Whimlab.AI.Agent.Platform.Domain.Core.Common;
using Whimlab.AI.Agent.Platform.Domain.Identity.Entities;
using Whimlab.AI.Agent.Platform.Domain.Identity.Repositories;
using Whimlab.AI.Agent.Platform.Domain.Identity.ValueObjects;
using Whimlab.AI.Agent.Platform.Infrastructure.Data;
using Whimlab.AI.Agent.Platform.Shared.Common.Enums;

namespace Whimlab.AI.Agent.Platform.Infrastructure.Repositories;

/// <summary>
/// 用户仓储实现
/// </summary>
public class UserRepository : BaseRepository<User, Guid>, IUserRepository
{
    /// <summary>
    /// 初始化用户仓储
    /// </summary>
    /// <param name="context">数据库上下文</param>
    /// <param name="logger">日志记录器</param>
    public UserRepository(
        AgentPlatformDbContext context,
        ILogger<UserRepository> logger) : base(context, logger)
    {
    }

    /// <summary>
    /// 根据用户名获取用户
    /// </summary>
    /// <param name="username">用户名</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>用户实体</returns>
    public async Task<User?> GetByUsernameAsync(string username, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("根据用户名获取用户: {Username}", username);

            return await _dbSet
                .FirstOrDefaultAsync(u => u.Username == username, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "根据用户名获取用户时发生错误: {Username}", username);
            throw;
        }
    }

    /// <summary>
    /// 根据邮箱获取用户
    /// </summary>
    /// <param name="email">邮箱</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>用户实体</returns>
    public async Task<User?> GetByEmailAsync(Email email, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("根据邮箱获取用户: {Email}", email);

            return await _dbSet
                .FirstOrDefaultAsync(u => u.Email.Value == email.Value, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "根据邮箱获取用户时发生错误: {Email}", email);
            throw;
        }
    }

    /// <summary>
    /// 根据电话号码获取用户
    /// </summary>
    /// <param name="phoneNumber">电话号码</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>用户实体</returns>
    public async Task<User?> GetByPhoneNumberAsync(PhoneNumber phoneNumber, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("根据电话号码获取用户: {PhoneNumber}", phoneNumber);

            return await _dbSet
                .FirstOrDefaultAsync(u => u.PhoneNumber != null && u.PhoneNumber.Value == phoneNumber.Value, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "根据电话号码获取用户时发生错误: {PhoneNumber}", phoneNumber);
            throw;
        }
    }

    /// <summary>
    /// 检查用户名是否存在
    /// </summary>
    /// <param name="username">用户名</param>
    /// <param name="excludeUserId">排除的用户ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>是否存在</returns>
    public async Task<bool> IsUsernameExistsAsync(string username, Guid? excludeUserId = null, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("检查用户名是否存在: {Username}, 排除用户ID: {ExcludeUserId}", username, excludeUserId);

            var query = _dbSet.Where(u => u.Username == username);

            if (excludeUserId.HasValue)
            {
                query = query.Where(u => u.Id != excludeUserId.Value);
            }

            return await query.AnyAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "检查用户名是否存在时发生错误: {Username}", username);
            throw;
        }
    }

    /// <summary>
    /// 检查邮箱是否存在
    /// </summary>
    /// <param name="email">邮箱</param>
    /// <param name="excludeUserId">排除的用户ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>是否存在</returns>
    public async Task<bool> IsEmailExistsAsync(Email email, Guid? excludeUserId = null, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("检查邮箱是否存在: {Email}, 排除用户ID: {ExcludeUserId}", email, excludeUserId);

            var query = _dbSet.Where(u => u.Email.Value == email.Value);

            if (excludeUserId.HasValue)
            {
                query = query.Where(u => u.Id != excludeUserId.Value);
            }

            return await query.AnyAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "检查邮箱是否存在时发生错误: {Email}", email);
            throw;
        }
    }

    /// <summary>
    /// 检查电话号码是否存在
    /// </summary>
    /// <param name="phoneNumber">电话号码</param>
    /// <param name="excludeUserId">排除的用户ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>是否存在</returns>
    public async Task<bool> IsPhoneNumberExistsAsync(PhoneNumber phoneNumber, Guid? excludeUserId = null, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("检查电话号码是否存在: {PhoneNumber}, 排除用户ID: {ExcludeUserId}", phoneNumber, excludeUserId);

            var query = _dbSet.Where(u => u.PhoneNumber != null && u.PhoneNumber.Value == phoneNumber.Value);

            if (excludeUserId.HasValue)
            {
                query = query.Where(u => u.Id != excludeUserId.Value);
            }

            return await query.AnyAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "检查电话号码是否存在时发生错误: {PhoneNumber}", phoneNumber);
            throw;
        }
    }

    /// <summary>
    /// 获取用户列表
    /// </summary>
    /// <param name="userType">用户类型</param>
    /// <param name="isActive">是否激活</param>
    /// <param name="searchTerm">搜索关键词</param>
    /// <param name="pageNumber">页码</param>
    /// <param name="pageSize">页大小</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>用户列表</returns>
    public async Task<(IEnumerable<User> Users, int TotalCount)> GetUsersAsync(
        UserType? userType = null,
        bool? isActive = null,
        string? searchTerm = null,
        int pageNumber = 1,
        int pageSize = 20,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("获取用户列表，用户类型: {UserType}, 是否激活: {IsActive}, 搜索词: {SearchTerm}, 页码: {PageNumber}, 页大小: {PageSize}",
                userType, isActive, searchTerm, pageNumber, pageSize);

            var query = _dbSet.AsQueryable();

            if (userType.HasValue)
            {
                query = query.Where(u => u.UserType == userType.Value);
            }

            if (isActive.HasValue)
            {
                query = query.Where(u => u.IsActive == isActive.Value);
            }

            if (!string.IsNullOrWhiteSpace(searchTerm))
            {
                var searchTermLower = searchTerm.ToLower();
                query = query.Where(u =>
                    u.Username.ToLower().Contains(searchTermLower) ||
                    u.Email.Value.ToLower().Contains(searchTermLower) ||
                    u.DisplayName.ToLower().Contains(searchTermLower));
            }

            var totalCount = await query.CountAsync(cancellationToken);

            var users = await query
                .OrderByDescending(u => u.CreatedAt)
                .Skip((pageNumber - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync(cancellationToken);

            return (users, totalCount);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取用户列表时发生错误");
            throw;
        }
    }

    /// <summary>
    /// 获取用户角色
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>用户角色列表</returns>
    public async Task<IEnumerable<Role>> GetUserRolesAsync(Guid userId, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("获取用户角色: {UserId}", userId);

            return await _context.UserRoles
                .Where(ur => ur.UserId == userId)
                .Include(ur => ur.Role)
                .Select(ur => ur.Role)
                .ToListAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取用户角色时发生错误: {UserId}", userId);
            throw;
        }
    }

    /// <summary>
    /// 获取用户权限
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>用户权限列表</returns>
    public async Task<IEnumerable<Permission>> GetUserPermissionsAsync(Guid userId, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("获取用户权限: {UserId}", userId);

            // 获取通过角色分配的权限
            var rolePermissions = await _context.UserRoles
                .Where(ur => ur.UserId == userId && ur.IsActive)
                .SelectMany(ur => ur.Role.RolePermissions)
                .Select(rp => rp.Permission)
                .ToListAsync(cancellationToken);

            return rolePermissions;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取用户权限时发生错误: {UserId}", userId);
            throw;
        }
    }

    /// <summary>
    /// 获取最近登录的用户
    /// </summary>
    /// <param name="count">数量</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>最近登录的用户列表</returns>
    public async Task<IEnumerable<User>> GetRecentlyLoggedInUsersAsync(int count = 10, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("获取最近登录的用户，数量: {Count}", count);

            return await _dbSet
                .Where(u => u.LastLoginAt.HasValue)
                .OrderByDescending(u => u.LastLoginAt)
                .Take(count)
                .ToListAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取最近登录的用户时发生错误，数量: {Count}", count);
            throw;
        }
    }

    /// <summary>
    /// 获取新注册的用户
    /// </summary>
    /// <param name="days">天数</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>新注册的用户列表</returns>
    public async Task<IEnumerable<User>> GetNewlyRegisteredUsersAsync(int days = 7, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("获取新注册的用户，天数: {Days}", days);

            var cutoffDate = DateTime.UtcNow.AddDays(-days);

            return await _dbSet
                .Where(u => u.CreatedAt >= cutoffDate)
                .OrderByDescending(u => u.CreatedAt)
                .ToListAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取新注册的用户时发生错误，天数: {Days}", days);
            throw;
        }
    }

    /// <summary>
    /// 根据邮箱验证令牌获取用户
    /// </summary>
    /// <param name="token">验证令牌</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>用户实体</returns>
    public async Task<User?> GetByEmailVerificationTokenAsync(string token, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("根据邮箱验证令牌获取用户: {Token}", token);

            return await _dbSet
                .FirstOrDefaultAsync(u => u.EmailVerificationToken == token, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "根据邮箱验证令牌获取用户时发生错误: {Token}", token);
            throw;
        }
    }

    /// <summary>
    /// 根据密码重置令牌获取用户
    /// </summary>
    /// <param name="token">重置令牌</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>用户实体</returns>
    public async Task<User?> GetByPasswordResetTokenAsync(string token, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("根据密码重置令牌获取用户: {Token}", token);

            return await _dbSet
                .FirstOrDefaultAsync(u => u.PasswordResetToken == token, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "根据密码重置令牌获取用户时发生错误: {Token}", token);
            throw;
        }
    }

    /// <summary>
    /// 检查用户是否拥有权限
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="permissionName">权限名称</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>是否拥有权限</returns>
    public async Task<bool> HasPermissionAsync(Guid userId, string permissionName, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("检查用户权限: {UserId}, 权限: {PermissionName}", userId, permissionName);

            // 检查通过角色分配的权限
            var hasRolePermission = await _context.UserRoles
                .Where(ur => ur.UserId == userId && ur.IsActive)
                .SelectMany(ur => ur.Role.RolePermissions)
                .AnyAsync(rp => rp.Permission.Name == permissionName, cancellationToken);

            return hasRolePermission;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "检查用户权限时发生错误: {UserId}, 权限: {PermissionName}", userId, permissionName);
            throw;
        }
    }

    /// <summary>
    /// 分配角色给用户
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="roleId">角色ID</param>
    /// <param name="assignedBy">分配者ID</param>
    /// <param name="expiresAt">过期时间</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>任务</returns>
    public async Task AssignRoleAsync(Guid userId, Guid roleId, Guid? assignedBy = null, DateTime? expiresAt = null, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("分配角色给用户: {UserId}, 角色: {RoleId}", userId, roleId);

            // 检查是否已存在
            var existingUserRole = await _context.UserRoles
                .FirstOrDefaultAsync(ur => ur.UserId == userId && ur.RoleId == roleId, cancellationToken);

            if (existingUserRole != null)
            {
                if (existingUserRole.IsActive)
                {
                    _logger.LogWarning("用户 {UserId} 已拥有角色 {RoleId}", userId, roleId);
                    return;
                }

                // 重新激活
                existingUserRole.Activate();
                existingUserRole.ExtendExpiry(expiresAt);
            }
            else
            {
                // 创建新的用户角色关联
                var userRole = UserRole.Create(userId, roleId, assignedBy, expiresAt);
                await _context.UserRoles.AddAsync(userRole, cancellationToken);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "分配角色给用户时发生错误: {UserId}, 角色: {RoleId}", userId, roleId);
            throw;
        }
    }

    /// <summary>
    /// 移除用户角色
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="roleId">角色ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>任务</returns>
    public async Task RemoveRoleAsync(Guid userId, Guid roleId, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("移除用户角色: {UserId}, 角色: {RoleId}", userId, roleId);

            var userRole = await _context.UserRoles
                .FirstOrDefaultAsync(ur => ur.UserId == userId && ur.RoleId == roleId, cancellationToken);

            if (userRole != null)
            {
                userRole.Deactivate();
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "移除用户角色时发生错误: {UserId}, 角色: {RoleId}", userId, roleId);
            throw;
        }
    }
}
