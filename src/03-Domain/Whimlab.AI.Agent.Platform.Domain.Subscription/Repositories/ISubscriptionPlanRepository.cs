using Whimlab.AI.Agent.Platform.Domain.Subscription.Entities;
using Whimlab.AI.Agent.Platform.Domain.Core.Common;

namespace Whimlab.AI.Agent.Platform.Domain.Subscription.Repositories;

/// <summary>
/// 订阅计划仓储接口
/// </summary>
public interface ISubscriptionPlanRepository
{
    /// <summary>
    /// 根据ID获取订阅计划
    /// </summary>
    /// <param name="id">计划ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>订阅计划实体</returns>
    Task<SubscriptionPlan?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default);

    /// <summary>
    /// 根据ID获取订阅计划（包含功能和配额）
    /// </summary>
    /// <param name="id">计划ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>订阅计划实体</returns>
    Task<SubscriptionPlan?> GetByIdWithDetailsAsync(Guid id, CancellationToken cancellationToken = default);

    /// <summary>
    /// 根据计划代码获取订阅计划
    /// </summary>
    /// <param name="planCode">计划代码</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>订阅计划实体</returns>
    Task<SubscriptionPlan?> GetByPlanCodeAsync(string planCode, CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取所有激活的订阅计划
    /// </summary>
    /// <param name="includeDetails">是否包含功能和配额详情</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>激活的订阅计划列表</returns>
    Task<IEnumerable<SubscriptionPlan>> GetActiveAsync(bool includeDetails = false, CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取公开的订阅计划（客户可见）
    /// </summary>
    /// <param name="includeDetails">是否包含功能和配额详情</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>公开的订阅计划列表</returns>
    Task<IEnumerable<SubscriptionPlan>> GetPublicPlansAsync(bool includeDetails = true, CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取所有订阅计划（管理员视图）
    /// </summary>
    /// <param name="pageNumber">页码</param>
    /// <param name="pageSize">页大小</param>
    /// <param name="searchTerm">搜索关键词</param>
    /// <param name="isActive">激活状态过滤</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>分页的订阅计划列表</returns>
    Task<PagedResult<SubscriptionPlan>> GetAllAsync(
        int pageNumber = 1,
        int pageSize = 20,
        string? searchTerm = null,
        bool? isActive = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取流行的订阅计划
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>流行的订阅计划列表</returns>
    Task<IEnumerable<SubscriptionPlan>> GetPopularPlansAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 检查计划代码是否已存在
    /// </summary>
    /// <param name="planCode">计划代码</param>
    /// <param name="excludeId">排除的计划ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>是否存在</returns>
    Task<bool> ExistsByPlanCodeAsync(string planCode, Guid? excludeId = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取订阅计划统计信息
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>统计信息</returns>
    Task<SubscriptionPlanStatistics> GetStatisticsAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 添加订阅计划
    /// </summary>
    /// <param name="plan">订阅计划实体</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>任务</returns>
    Task AddAsync(SubscriptionPlan plan, CancellationToken cancellationToken = default);

    /// <summary>
    /// 更新订阅计划
    /// </summary>
    /// <param name="plan">订阅计划实体</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>任务</returns>
    Task UpdateAsync(SubscriptionPlan plan, CancellationToken cancellationToken = default);

    /// <summary>
    /// 删除订阅计划
    /// </summary>
    /// <param name="plan">订阅计划实体</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>任务</returns>
    Task DeleteAsync(SubscriptionPlan plan, CancellationToken cancellationToken = default);

    /// <summary>
    /// 保存更改
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>受影响的行数</returns>
    Task<int> SaveChangesAsync(CancellationToken cancellationToken = default);
}

/// <summary>
/// 订阅计划统计信息
/// </summary>
public class SubscriptionPlanStatistics
{
    /// <summary>
    /// 总计划数
    /// </summary>
    public int TotalPlans { get; set; }

    /// <summary>
    /// 激活的计划数
    /// </summary>
    public int ActivePlans { get; set; }

    /// <summary>
    /// 停用的计划数
    /// </summary>
    public int InactivePlans { get; set; }

    /// <summary>
    /// 流行计划数
    /// </summary>
    public int PopularPlans { get; set; }

    /// <summary>
    /// 免费计划数
    /// </summary>
    public int FreePlans { get; set; }

    /// <summary>
    /// 付费计划数
    /// </summary>
    public int PaidPlans { get; set; }

    /// <summary>
    /// 平均月度价格
    /// </summary>
    public decimal AverageMonthlyPrice { get; set; }

    /// <summary>
    /// 平均年度价格
    /// </summary>
    public decimal AverageYearlyPrice { get; set; }

    /// <summary>
    /// 最受欢迎的功能
    /// </summary>
    public Dictionary<string, int> PopularFeatures { get; set; } = new();

    /// <summary>
    /// 配额类型分布
    /// </summary>
    public Dictionary<string, int> QuotaTypeDistribution { get; set; } = new();
}
