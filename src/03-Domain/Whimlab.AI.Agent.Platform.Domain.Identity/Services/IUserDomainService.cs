using Whimlab.AI.Agent.Platform.Domain.Identity.Entities;
using Whimlab.AI.Agent.Platform.Domain.Identity.ValueObjects;

namespace Whimlab.AI.Agent.Platform.Domain.Identity.Services;

/// <summary>
/// 用户领域服务接口
/// </summary>
public interface IUserDomainService
{
    /// <summary>
    /// 验证用户名是否可用
    /// </summary>
    /// <param name="username">用户名</param>
    /// <param name="excludeUserId">排除的用户ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>是否可用</returns>
    Task<bool> IsUsernameAvailableAsync(string username, Guid? excludeUserId = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// 验证邮箱是否可用
    /// </summary>
    /// <param name="email">邮箱地址</param>
    /// <param name="excludeUserId">排除的用户ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>是否可用</returns>
    Task<bool> IsEmailAvailableAsync(string email, Guid? excludeUserId = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// 验证手机号是否可用
    /// </summary>
    /// <param name="phoneNumber">手机号</param>
    /// <param name="excludeUserId">排除的用户ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>是否可用</returns>
    Task<bool> IsPhoneNumberAvailableAsync(string phoneNumber, Guid? excludeUserId = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// 验证密码强度
    /// </summary>
    /// <param name="password">密码</param>
    /// <returns>密码强度验证结果</returns>
    PasswordStrengthResult ValidatePasswordStrength(string password);

    /// <summary>
    /// 检查密码是否符合策略
    /// </summary>
    /// <param name="password">密码</param>
    /// <param name="user">用户信息（用于检查是否包含用户信息）</param>
    /// <returns>密码策略验证结果</returns>
    PasswordPolicyResult ValidatePasswordPolicy(string password, User? user = null);

    /// <summary>
    /// 生成安全的随机密码
    /// </summary>
    /// <param name="length">密码长度</param>
    /// <param name="includeSpecialChars">是否包含特殊字符</param>
    /// <returns>随机密码</returns>
    string GenerateSecurePassword(int length = 12, bool includeSpecialChars = true);

    /// <summary>
    /// 验证用户登录凭据
    /// </summary>
    /// <param name="loginIdentifier">登录标识（用户名、邮箱或手机号）</param>
    /// <param name="password">密码</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>登录验证结果</returns>
    Task<LoginValidationResult> ValidateLoginCredentialsAsync(string loginIdentifier, string password, CancellationToken cancellationToken = default);

    /// <summary>
    /// 检查用户是否被锁定
    /// </summary>
    /// <param name="user">用户实体</param>
    /// <returns>是否被锁定</returns>
    bool IsUserLocked(User user);

    /// <summary>
    /// 记录登录失败
    /// </summary>
    /// <param name="user">用户实体</param>
    /// <param name="ipAddress">IP地址</param>
    /// <param name="userAgent">用户代理</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>任务</returns>
    Task RecordLoginFailureAsync(User user, string? ipAddress = null, string? userAgent = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// 记录登录成功
    /// </summary>
    /// <param name="user">用户实体</param>
    /// <param name="ipAddress">IP地址</param>
    /// <param name="userAgent">用户代理</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>任务</returns>
    Task RecordLoginSuccessAsync(User user, string? ipAddress = null, string? userAgent = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// 检查用户权限
    /// </summary>
    /// <param name="user">用户实体</param>
    /// <param name="permissionName">权限名称</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>是否有权限</returns>
    Task<bool> HasPermissionAsync(User user, string permissionName, CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取用户的所有权限
    /// </summary>
    /// <param name="user">用户实体</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>权限列表</returns>
    Task<IEnumerable<string>> GetUserPermissionsAsync(User user, CancellationToken cancellationToken = default);

    /// <summary>
    /// 验证邮箱验证码
    /// </summary>
    /// <param name="email">邮箱地址</param>
    /// <param name="code">验证码</param>
    /// <param name="purpose">验证目的</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>验证结果</returns>
    Task<VerificationResult> ValidateEmailVerificationCodeAsync(string email, string code, string purpose, CancellationToken cancellationToken = default);

    /// <summary>
    /// 验证手机验证码
    /// </summary>
    /// <param name="phoneNumber">手机号</param>
    /// <param name="code">验证码</param>
    /// <param name="purpose">验证目的</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>验证结果</returns>
    Task<VerificationResult> ValidatePhoneVerificationCodeAsync(string phoneNumber, string code, string purpose, CancellationToken cancellationToken = default);

    /// <summary>
    /// 生成密码重置令牌
    /// </summary>
    /// <param name="user">用户实体</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>重置令牌</returns>
    Task<string> GeneratePasswordResetTokenAsync(User user, CancellationToken cancellationToken = default);

    /// <summary>
    /// 验证密码重置令牌
    /// </summary>
    /// <param name="user">用户实体</param>
    /// <param name="token">重置令牌</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>是否有效</returns>
    Task<bool> ValidatePasswordResetTokenAsync(User user, string token, CancellationToken cancellationToken = default);
}

/// <summary>
/// 密码强度验证结果
/// </summary>
public class PasswordStrengthResult
{
    /// <summary>
    /// 是否通过验证
    /// </summary>
    public bool IsValid { get; set; }

    /// <summary>
    /// 强度分数（0-100）
    /// </summary>
    public int Score { get; set; }

    /// <summary>
    /// 强度等级
    /// </summary>
    public PasswordStrengthLevel Level { get; set; }

    /// <summary>
    /// 验证消息
    /// </summary>
    public List<string> Messages { get; set; } = new();

    /// <summary>
    /// 建议
    /// </summary>
    public List<string> Suggestions { get; set; } = new();
}

/// <summary>
/// 密码策略验证结果
/// </summary>
public class PasswordPolicyResult
{
    /// <summary>
    /// 是否通过验证
    /// </summary>
    public bool IsValid { get; set; }

    /// <summary>
    /// 错误消息
    /// </summary>
    public List<string> Errors { get; set; } = new();

    /// <summary>
    /// 警告消息
    /// </summary>
    public List<string> Warnings { get; set; } = new();
}

/// <summary>
/// 登录验证结果
/// </summary>
public class LoginValidationResult
{
    /// <summary>
    /// 是否成功
    /// </summary>
    public bool IsSuccess { get; set; }

    /// <summary>
    /// 用户实体
    /// </summary>
    public User? User { get; set; }

    /// <summary>
    /// 失败原因
    /// </summary>
    public LoginFailureReason? FailureReason { get; set; }

    /// <summary>
    /// 错误消息
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// 是否需要额外验证
    /// </summary>
    public bool RequiresAdditionalVerification { get; set; }

    /// <summary>
    /// 锁定剩余时间（分钟）
    /// </summary>
    public int? LockoutRemainingMinutes { get; set; }
}

/// <summary>
/// 验证结果
/// </summary>
public class VerificationResult
{
    /// <summary>
    /// 是否成功
    /// </summary>
    public bool IsSuccess { get; set; }

    /// <summary>
    /// 错误消息
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// 是否已过期
    /// </summary>
    public bool IsExpired { get; set; }

    /// <summary>
    /// 剩余尝试次数
    /// </summary>
    public int? RemainingAttempts { get; set; }
}

/// <summary>
/// 密码强度等级
/// </summary>
public enum PasswordStrengthLevel
{
    /// <summary>
    /// 非常弱
    /// </summary>
    VeryWeak = 1,

    /// <summary>
    /// 弱
    /// </summary>
    Weak = 2,

    /// <summary>
    /// 中等
    /// </summary>
    Medium = 3,

    /// <summary>
    /// 强
    /// </summary>
    Strong = 4,

    /// <summary>
    /// 非常强
    /// </summary>
    VeryStrong = 5
}

/// <summary>
/// 登录失败原因
/// </summary>
public enum LoginFailureReason
{
    /// <summary>
    /// 用户不存在
    /// </summary>
    UserNotFound = 1,

    /// <summary>
    /// 密码错误
    /// </summary>
    InvalidPassword = 2,

    /// <summary>
    /// 账户被锁定
    /// </summary>
    AccountLocked = 3,

    /// <summary>
    /// 账户未激活
    /// </summary>
    AccountNotActivated = 4,

    /// <summary>
    /// 账户被禁用
    /// </summary>
    AccountDisabled = 5,

    /// <summary>
    /// 需要邮箱验证
    /// </summary>
    EmailVerificationRequired = 6,

    /// <summary>
    /// 需要手机验证
    /// </summary>
    PhoneVerificationRequired = 7
}
