using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Whimlab.AI.Agent.Platform.Domain.Core.Common;

namespace Whimlab.AI.Agent.Platform.Infrastructure.Configurations.Abstractions;

/// <summary>
/// 实体配置基类
/// </summary>
/// <typeparam name="TEntity">实体类型</typeparam>
/// <typeparam name="TKey">主键类型</typeparam>
public abstract class BaseEntityConfiguration<TEntity, TKey> : IEntityTypeConfiguration<TEntity>
    where TEntity : Entity<TKey>
    where TKey : notnull
{
    /// <summary>
    /// 配置实体
    /// </summary>
    /// <param name="builder">实体类型构建器</param>
    public virtual void Configure(EntityTypeBuilder<TEntity> builder)
    {
        // 配置主键
        ConfigurePrimaryKey(builder);

        // 配置基础属性
        ConfigureBaseProperties(builder);

        // 配置索引
        ConfigureIndexes(builder);

        // 配置特定实体属性
        ConfigureEntity(builder);

        // 配置关系
        ConfigureRelationships(builder);

        // 配置查询过滤器
        ConfigureQueryFilters(builder);
    }

    /// <summary>
    /// 配置主键
    /// </summary>
    /// <param name="builder">实体类型构建器</param>
    protected virtual void ConfigurePrimaryKey(EntityTypeBuilder<TEntity> builder)
    {
        builder.HasKey(e => e.Id);
        
        // 如果主键是Guid类型，配置默认值生成
        if (typeof(TKey) == typeof(Guid))
        {
            builder.Property(e => e.Id)
                .ValueGeneratedOnAdd();
        }
    }

    /// <summary>
    /// 配置基础属性
    /// </summary>
    /// <param name="builder">实体类型构建器</param>
    protected virtual void ConfigureBaseProperties(EntityTypeBuilder<TEntity> builder)
    {
        // 配置创建时间
        builder.Property(e => e.CreatedAt)
            .IsRequired()
            .HasDefaultValueSql("GETUTCDATE()")
            .HasComment("创建时间");

        // 配置最后修改时间
        builder.Property(e => e.LastModifiedAt)
            .IsRequired()
            .HasDefaultValueSql("GETUTCDATE()")
            .HasComment("最后修改时间");

        // 配置并发令牌（如果实体支持）
        ConfigureConcurrencyToken(builder);
    }

    /// <summary>
    /// 配置并发令牌
    /// </summary>
    /// <param name="builder">实体类型构建器</param>
    protected virtual void ConfigureConcurrencyToken(EntityTypeBuilder<TEntity> builder)
    {
        // 如果实体是聚合根，配置版本号作为并发令牌
        if (typeof(TEntity).IsSubclassOf(typeof(AggregateRoot<TKey>)))
        {
            builder.Property("Version")
                .IsRowVersion()
                .HasComment("版本号（并发控制）");
        }
    }

    /// <summary>
    /// 配置索引
    /// </summary>
    /// <param name="builder">实体类型构建器</param>
    protected virtual void ConfigureIndexes(EntityTypeBuilder<TEntity> builder)
    {
        // 为创建时间创建索引
        builder.HasIndex(e => e.CreatedAt)
            .HasDatabaseName($"IX_{GetTableName()}_CreatedAt");

        // 为最后修改时间创建索引
        builder.HasIndex(e => e.LastModifiedAt)
            .HasDatabaseName($"IX_{GetTableName()}_LastModifiedAt");
    }

    /// <summary>
    /// 配置特定实体属性
    /// </summary>
    /// <param name="builder">实体类型构建器</param>
    protected abstract void ConfigureEntity(EntityTypeBuilder<TEntity> builder);

    /// <summary>
    /// 配置关系
    /// </summary>
    /// <param name="builder">实体类型构建器</param>
    protected virtual void ConfigureRelationships(EntityTypeBuilder<TEntity> builder)
    {
        // 默认实现为空，子类可以重写
    }

    /// <summary>
    /// 配置查询过滤器
    /// </summary>
    /// <param name="builder">实体类型构建器</param>
    protected virtual void ConfigureQueryFilters(EntityTypeBuilder<TEntity> builder)
    {
        // 默认实现为空，子类可以重写
        // 例如：软删除过滤器
    }

    /// <summary>
    /// 获取表名
    /// </summary>
    /// <returns>表名</returns>
    protected virtual string GetTableName()
    {
        return typeof(TEntity).Name;
    }

    /// <summary>
    /// 配置字符串属性
    /// </summary>
    /// <param name="builder">属性构建器</param>
    /// <param name="maxLength">最大长度</param>
    /// <param name="isRequired">是否必需</param>
    /// <param name="comment">注释</param>
    /// <returns>属性构建器</returns>
    protected static PropertyBuilder<string> ConfigureStringProperty(
        PropertyBuilder<string> builder,
        int maxLength,
        bool isRequired = true,
        string? comment = null)
    {
        builder.HasMaxLength(maxLength);

        if (isRequired)
        {
            builder.IsRequired();
        }

        if (!string.IsNullOrEmpty(comment))
        {
            builder.HasComment(comment);
        }

        return builder;
    }

    /// <summary>
    /// 配置可空字符串属性
    /// </summary>
    /// <param name="builder">属性构建器</param>
    /// <param name="maxLength">最大长度</param>
    /// <param name="comment">注释</param>
    /// <returns>属性构建器</returns>
    protected static PropertyBuilder<string?> ConfigureNullableStringProperty(
        PropertyBuilder<string?> builder,
        int maxLength,
        string? comment = null)
    {
        builder.HasMaxLength(maxLength);

        if (!string.IsNullOrEmpty(comment))
        {
            builder.HasComment(comment);
        }

        return builder;
    }

    /// <summary>
    /// 配置枚举属性
    /// </summary>
    /// <param name="builder">属性构建器</param>
    /// <param name="comment">注释</param>
    /// <returns>属性构建器</returns>
    protected static PropertyBuilder<TEnum> ConfigureEnumProperty<TEnum>(
        PropertyBuilder<TEnum> builder,
        string? comment = null)
        where TEnum : struct, Enum
    {
        builder.HasConversion<string>();

        if (!string.IsNullOrEmpty(comment))
        {
            builder.HasComment(comment);
        }

        return builder;
    }

    /// <summary>
    /// 配置decimal属性
    /// </summary>
    /// <param name="builder">属性构建器</param>
    /// <param name="precision">精度</param>
    /// <param name="scale">小数位数</param>
    /// <param name="comment">注释</param>
    /// <returns>属性构建器</returns>
    protected static PropertyBuilder<decimal> ConfigureDecimalProperty(
        PropertyBuilder<decimal> builder,
        int precision = 18,
        int scale = 2,
        string? comment = null)
    {
        builder.HasPrecision(precision, scale);

        if (!string.IsNullOrEmpty(comment))
        {
            builder.HasComment(comment);
        }

        return builder;
    }

    /// <summary>
    /// 配置可空decimal属性
    /// </summary>
    /// <param name="builder">属性构建器</param>
    /// <param name="precision">精度</param>
    /// <param name="scale">小数位数</param>
    /// <param name="comment">注释</param>
    /// <returns>属性构建器</returns>
    protected static PropertyBuilder<decimal?> ConfigureNullableDecimalProperty(
        PropertyBuilder<decimal?> builder,
        int precision = 18,
        int scale = 2,
        string? comment = null)
    {
        builder.HasPrecision(precision, scale);

        if (!string.IsNullOrEmpty(comment))
        {
            builder.HasComment(comment);
        }

        return builder;
    }

    /// <summary>
    /// 配置JSON属性
    /// </summary>
    /// <param name="builder">属性构建器</param>
    /// <param name="comment">注释</param>
    /// <returns>属性构建器</returns>
    protected static PropertyBuilder<T> ConfigureJsonProperty<T>(
        PropertyBuilder<T> builder,
        string? comment = null)
    {
        builder.HasConversion(
            v => System.Text.Json.JsonSerializer.Serialize(v, (System.Text.Json.JsonSerializerOptions?)null),
            v => System.Text.Json.JsonSerializer.Deserialize<T>(v, (System.Text.Json.JsonSerializerOptions?)null)!);

        if (!string.IsNullOrEmpty(comment))
        {
            builder.HasComment(comment);
        }

        return builder;
    }
}
