using Whimlab.AI.Agent.Platform.Application.Core.Common;
using Whimlab.AI.Agent.Platform.Application.Subscription.DTOs;
using Whimlab.AI.Agent.Platform.Domain.Core.Common;
using Whimlab.AI.Agent.Platform.Shared.Common.Enums;

namespace Whimlab.AI.Agent.Platform.Application.Subscription.Queries;

/// <summary>
/// 根据ID获取订阅计划查询
/// </summary>
public class GetSubscriptionPlanByIdQuery : IQuery<SubscriptionPlanDto?>
{
    /// <summary>
    /// 计划ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// 是否包含配额限制
    /// </summary>
    public bool IncludeQuotaLimits { get; set; } = true;

    /// <summary>
    /// 初始化查询
    /// </summary>
    /// <param name="id">计划ID</param>
    /// <param name="includeQuotaLimits">是否包含配额限制</param>
    public GetSubscriptionPlanByIdQuery(Guid id, bool includeQuotaLimits = true)
    {
        Id = id;
        IncludeQuotaLimits = includeQuotaLimits;
    }
}

/// <summary>
/// 获取订阅计划列表查询
/// </summary>
public class GetSubscriptionPlansQuery : PagedQuery, IQuery<PagedResult<SubscriptionPlanDto>>
{
    /// <summary>
    /// 计划类型过滤
    /// </summary>
    public PlanType? PlanType { get; set; }

    /// <summary>
    /// 是否激活过滤
    /// </summary>
    public bool? IsActive { get; set; }

    /// <summary>
    /// 是否公开过滤
    /// </summary>
    public bool? IsPublic { get; set; }

    /// <summary>
    /// 价格范围最小值
    /// </summary>
    public decimal? MinPrice { get; set; }

    /// <summary>
    /// 价格范围最大值
    /// </summary>
    public decimal? MaxPrice { get; set; }
}

/// <summary>
/// 获取公开订阅计划查询
/// </summary>
public class GetPublicSubscriptionPlansQuery : IQuery<IEnumerable<SubscriptionPlanDto>>
{
    /// <summary>
    /// 计划类型过滤
    /// </summary>
    public PlanType? PlanType { get; set; }

    /// <summary>
    /// 是否按价格排序
    /// </summary>
    public bool SortByPrice { get; set; } = true;

    /// <summary>
    /// 初始化查询
    /// </summary>
    /// <param name="planType">计划类型过滤</param>
    public GetPublicSubscriptionPlansQuery(PlanType? planType = null)
    {
        PlanType = planType;
    }
}

/// <summary>
/// 根据ID获取客户订阅查询
/// </summary>
public class GetCustomerSubscriptionByIdQuery : IQuery<CustomerSubscriptionDto?>
{
    /// <summary>
    /// 订阅ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// 是否包含配额使用情况
    /// </summary>
    public bool IncludeQuotaUsage { get; set; } = true;

    /// <summary>
    /// 是否包含历史记录
    /// </summary>
    public bool IncludeHistory { get; set; } = false;

    /// <summary>
    /// 初始化查询
    /// </summary>
    /// <param name="id">订阅ID</param>
    /// <param name="includeQuotaUsage">是否包含配额使用情况</param>
    /// <param name="includeHistory">是否包含历史记录</param>
    public GetCustomerSubscriptionByIdQuery(Guid id, bool includeQuotaUsage = true, bool includeHistory = false)
    {
        Id = id;
        IncludeQuotaUsage = includeQuotaUsage;
        IncludeHistory = includeHistory;
    }
}

/// <summary>
/// 获取客户当前订阅查询
/// </summary>
public class GetCustomerCurrentSubscriptionQuery : IQuery<CustomerSubscriptionDto?>
{
    /// <summary>
    /// 客户ID
    /// </summary>
    public Guid CustomerId { get; set; }

    /// <summary>
    /// 是否包含配额使用情况
    /// </summary>
    public bool IncludeQuotaUsage { get; set; } = true;

    /// <summary>
    /// 初始化查询
    /// </summary>
    /// <param name="customerId">客户ID</param>
    /// <param name="includeQuotaUsage">是否包含配额使用情况</param>
    public GetCustomerCurrentSubscriptionQuery(Guid customerId, bool includeQuotaUsage = true)
    {
        CustomerId = customerId;
        IncludeQuotaUsage = includeQuotaUsage;
    }
}

/// <summary>
/// 获取客户订阅历史查询
/// </summary>
public class GetCustomerSubscriptionHistoryQuery : PagedQuery, IQuery<PagedResult<CustomerSubscriptionDto>>
{
    /// <summary>
    /// 客户ID
    /// </summary>
    public Guid CustomerId { get; set; }

    /// <summary>
    /// 订阅状态过滤
    /// </summary>
    public SubscriptionStatus? Status { get; set; }

    /// <summary>
    /// 开始时间范围
    /// </summary>
    public DateTime? StartDateFrom { get; set; }

    /// <summary>
    /// 结束时间范围
    /// </summary>
    public DateTime? StartDateTo { get; set; }

    /// <summary>
    /// 初始化查询
    /// </summary>
    /// <param name="customerId">客户ID</param>
    public GetCustomerSubscriptionHistoryQuery(Guid customerId)
    {
        CustomerId = customerId;
    }
}

/// <summary>
/// 获取客户配额使用情况查询
/// </summary>
public class GetCustomerQuotaUsageQuery : IQuery<IEnumerable<QuotaUsageDto>>
{
    /// <summary>
    /// 客户ID
    /// </summary>
    public Guid CustomerId { get; set; }

    /// <summary>
    /// 配额类型过滤
    /// </summary>
    public string? QuotaType { get; set; }

    /// <summary>
    /// 初始化查询
    /// </summary>
    /// <param name="customerId">客户ID</param>
    /// <param name="quotaType">配额类型过滤</param>
    public GetCustomerQuotaUsageQuery(Guid customerId, string? quotaType = null)
    {
        CustomerId = customerId;
        QuotaType = quotaType;
    }
}

/// <summary>
/// 检查配额可用性查询
/// </summary>
public class CheckQuotaAvailabilityQuery : IQuery<bool>
{
    /// <summary>
    /// 客户ID
    /// </summary>
    public Guid CustomerId { get; set; }

    /// <summary>
    /// 配额类型
    /// </summary>
    public string QuotaType { get; set; } = string.Empty;

    /// <summary>
    /// 请求使用量
    /// </summary>
    public long RequestedAmount { get; set; }

    /// <summary>
    /// 初始化查询
    /// </summary>
    /// <param name="customerId">客户ID</param>
    /// <param name="quotaType">配额类型</param>
    /// <param name="requestedAmount">请求使用量</param>
    public CheckQuotaAvailabilityQuery(Guid customerId, string quotaType, long requestedAmount)
    {
        CustomerId = customerId;
        QuotaType = quotaType;
        RequestedAmount = requestedAmount;
    }
}

/// <summary>
/// 获取订阅统计查询
/// </summary>
public class GetSubscriptionStatisticsQuery : IQuery<SubscriptionStatisticsDto>
{
    /// <summary>
    /// 统计开始日期
    /// </summary>
    public DateTime StartDate { get; set; }

    /// <summary>
    /// 统计结束日期
    /// </summary>
    public DateTime EndDate { get; set; }

    /// <summary>
    /// 计划ID过滤
    /// </summary>
    public Guid? PlanId { get; set; }

    /// <summary>
    /// 初始化查询
    /// </summary>
    /// <param name="startDate">开始日期</param>
    /// <param name="endDate">结束日期</param>
    /// <param name="planId">计划ID过滤</param>
    public GetSubscriptionStatisticsQuery(DateTime startDate, DateTime endDate, Guid? planId = null)
    {
        StartDate = startDate;
        EndDate = endDate;
        PlanId = planId;
    }
}

/// <summary>
/// 获取配额分析查询
/// </summary>
public class GetQuotaAnalysisQuery : IQuery<QuotaAnalysisDto>
{
    /// <summary>
    /// 客户ID
    /// </summary>
    public Guid CustomerId { get; set; }

    /// <summary>
    /// 分析开始日期
    /// </summary>
    public DateTime StartDate { get; set; }

    /// <summary>
    /// 分析结束日期
    /// </summary>
    public DateTime EndDate { get; set; }

    /// <summary>
    /// 配额类型过滤
    /// </summary>
    public string? QuotaType { get; set; }

    /// <summary>
    /// 是否包含预测
    /// </summary>
    public bool IncludePrediction { get; set; } = true;

    /// <summary>
    /// 初始化查询
    /// </summary>
    /// <param name="customerId">客户ID</param>
    /// <param name="startDate">开始日期</param>
    /// <param name="endDate">结束日期</param>
    /// <param name="quotaType">配额类型过滤</param>
    public GetQuotaAnalysisQuery(Guid customerId, DateTime startDate, DateTime endDate, string? quotaType = null)
    {
        CustomerId = customerId;
        StartDate = startDate;
        EndDate = endDate;
        QuotaType = quotaType;
    }
}

/// <summary>
/// 获取即将到期的订阅查询
/// </summary>
public class GetExpiringSubscriptionsQuery : IQuery<IEnumerable<CustomerSubscriptionDto>>
{
    /// <summary>
    /// 到期天数阈值
    /// </summary>
    public int DaysThreshold { get; set; } = 7;

    /// <summary>
    /// 数量限制
    /// </summary>
    public int? Limit { get; set; }

    /// <summary>
    /// 初始化查询
    /// </summary>
    /// <param name="daysThreshold">到期天数阈值</param>
    /// <param name="limit">数量限制</param>
    public GetExpiringSubscriptionsQuery(int daysThreshold = 7, int? limit = null)
    {
        DaysThreshold = daysThreshold;
        Limit = limit;
    }
}

/// <summary>
/// 获取超限客户查询
/// </summary>
public class GetOverLimitCustomersQuery : IQuery<IEnumerable<Guid>>
{
    /// <summary>
    /// 配额类型
    /// </summary>
    public string QuotaType { get; set; } = string.Empty;

    /// <summary>
    /// 数量限制
    /// </summary>
    public int? Limit { get; set; }

    /// <summary>
    /// 初始化查询
    /// </summary>
    /// <param name="quotaType">配额类型</param>
    /// <param name="limit">数量限制</param>
    public GetOverLimitCustomersQuery(string quotaType, int? limit = null)
    {
        QuotaType = quotaType;
        Limit = limit;
    }
}

/// <summary>
/// 获取收入报告查询
/// </summary>
public class GetRevenueReportQuery : IQuery<IEnumerable<RevenueDataPointDto>>
{
    /// <summary>
    /// 报告开始日期
    /// </summary>
    public DateTime StartDate { get; set; }

    /// <summary>
    /// 报告结束日期
    /// </summary>
    public DateTime EndDate { get; set; }

    /// <summary>
    /// 报告粒度
    /// </summary>
    public ReportGranularity Granularity { get; set; } = ReportGranularity.Daily;

    /// <summary>
    /// 计划ID过滤
    /// </summary>
    public Guid? PlanId { get; set; }

    /// <summary>
    /// 初始化查询
    /// </summary>
    /// <param name="startDate">开始日期</param>
    /// <param name="endDate">结束日期</param>
    /// <param name="granularity">报告粒度</param>
    public GetRevenueReportQuery(DateTime startDate, DateTime endDate, ReportGranularity granularity = ReportGranularity.Daily)
    {
        StartDate = startDate;
        EndDate = endDate;
        Granularity = granularity;
    }
}
