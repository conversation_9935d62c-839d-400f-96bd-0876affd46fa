<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <IsPackable>false</IsPackable>
    <IsTestProject>true</IsTestProject>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="coverlet.collector" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\src\01-Presentation\Whimlab.AI.Agent.Platform.Web.Api\Whimlab.AI.Agent.Platform.Web.Api.csproj" />
    <ProjectReference Include="..\..\src\02-Application\Whimlab.AI.Agent.Platform.Application.Identity\Whimlab.AI.Agent.Platform.Application.Identity.csproj" />
    <ProjectReference Include="..\..\src\03-Domain\Whimlab.AI.Agent.Platform.Domain.Identity\Whimlab.AI.Agent.Platform.Domain.Identity.csproj" />
    <ProjectReference Include="..\..\src\04-Infrastructure\Whimlab.AI.Agent.Platform.Infrastructure\Whimlab.AI.Agent.Platform.Infrastructure.csproj" />
  </ItemGroup>




  <ItemGroup>
    <Using Include="Xunit" />
    <Using Include="Moq" />
    <Using Include="FluentAssertions" />
  </ItemGroup>

</Project>
