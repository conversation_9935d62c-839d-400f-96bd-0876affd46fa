using AutoMapper;
using Whimlab.AI.Agent.Platform.Application.Conversation.DTOs;
using Whimlab.AI.Agent.Platform.Domain.Conversation.Entities;
using Whimlab.AI.Agent.Platform.Shared.Common.Enums;

namespace Whimlab.AI.Agent.Platform.Application.Conversation.Mappings;

/// <summary>
/// 对话模块映射配置
/// </summary>
public class ConversationMappingProfile : Profile
{
    /// <summary>
    /// 初始化映射配置
    /// </summary>
    public ConversationMappingProfile()
    {
        CreateConversationMappings();
        CreateMessageMappings();
        CreateAttachmentMappings();
    }

    /// <summary>
    /// 创建对话映射
    /// </summary>
    private void CreateConversationMappings()
    {
        // Conversation -> ConversationDto
        CreateMap<Domain.Conversation.Entities.Conversation, ConversationDto>()
            .ForMember(dest => dest.AgentName, opt => opt.Ignore()) // 需要在查询时单独处理
            .ForMember(dest => dest.Tags, opt => opt.MapFrom(src => new List<string>())) // 默认空列表
            .ForMember(dest => dest.Messages, opt => opt.MapFrom(src => src.Messages))
            .ForMember(dest => dest.Summary, opt => opt.MapFrom(src => src.Context))
            .ForMember(dest => dest.MessageCount, opt => opt.MapFrom(src => src.Messages.Count))
            .ForMember(dest => dest.LastActivityAt, opt => opt.MapFrom(src =>
                src.Messages.Any() ? src.Messages.Max(m => m.CreatedAt) : src.CreatedAt));

        // Conversation -> ConversationSummaryDto
        CreateMap<Domain.Conversation.Entities.Conversation, ConversationSummaryDto>()
            .ForMember(dest => dest.AgentName, opt => opt.Ignore()) // 需要在查询时单独处理
            .ForMember(dest => dest.Tags, opt => opt.MapFrom(src => new List<string>())) // 默认空列表
            .ForMember(dest => dest.Summary, opt => opt.MapFrom(src => src.Context))
            .ForMember(dest => dest.MessageCount, opt => opt.MapFrom(src => src.Messages.Count))
            .ForMember(dest => dest.LastActivityAt, opt => opt.MapFrom(src =>
                src.Messages.Any() ? src.Messages.Max(m => m.CreatedAt) : src.CreatedAt));
    }

    /// <summary>
    /// 创建消息映射
    /// </summary>
    private void CreateMessageMappings()
    {
        // Message -> MessageDto
        CreateMap<Message, MessageDto>()
            .ForMember(dest => dest.Attachments, opt => opt.MapFrom(src => src.Attachments));

        // 自定义映射方法将在处理器中实现
        // CreateMessageDto -> Message (需要在处理器中手动处理，因为涉及值对象创建)
    }

    /// <summary>
    /// 创建附件映射
    /// </summary>
    private void CreateAttachmentMappings()
    {
        // MessageAttachment -> MessageAttachmentDto
        CreateMap<MessageAttachment, MessageAttachmentDto>();

        // CreateMessageAttachmentDto -> MessageAttachment (需要在处理器中手动处理)
    }
}
