using MediatR;
using Microsoft.Extensions.Logging;
using Whimlab.AI.Agent.Platform.Domain.Conversation.Events;

namespace Whimlab.AI.Agent.Platform.Application.Conversation.Events;

/// <summary>
/// 对话创建事件处理器
/// </summary>
public class ConversationCreatedEventHandler : INotificationHandler<ConversationCreatedEvent>
{
    private readonly ILogger<ConversationCreatedEventHandler> _logger;

    /// <summary>
    /// 初始化对话创建事件处理器
    /// </summary>
    /// <param name="logger">日志记录器</param>
    public ConversationCreatedEventHandler(ILogger<ConversationCreatedEventHandler> logger)
    {
        _logger = logger;
    }

    /// <summary>
    /// 处理对话创建事件
    /// </summary>
    /// <param name="notification">事件通知</param>
    /// <param name="cancellationToken">取消令牌</param>
    public async Task Handle(ConversationCreatedEvent notification, CancellationToken cancellationToken)
    {
        _logger.LogInformation("处理对话创建事件: 对话ID {ConversationId}, 用户ID {UserId}, 智能体ID {AgentId}, 标题 {Title}",
            notification.ConversationId, notification.UserId, notification.AgentId, notification.Title);

        // 这里可以添加对话创建后的业务逻辑
        // 例如：初始化对话上下文、记录统计信息、发送通知等

        await Task.CompletedTask;
    }
}

/// <summary>
/// 消息添加事件处理器
/// </summary>
public class MessageAddedEventHandler : INotificationHandler<MessageAddedEvent>
{
    private readonly ILogger<MessageAddedEventHandler> _logger;

    /// <summary>
    /// 初始化消息添加事件处理器
    /// </summary>
    /// <param name="logger">日志记录器</param>
    public MessageAddedEventHandler(ILogger<MessageAddedEventHandler> logger)
    {
        _logger = logger;
    }

    /// <summary>
    /// 处理消息添加事件
    /// </summary>
    /// <param name="notification">事件通知</param>
    /// <param name="cancellationToken">取消令牌</param>
    public async Task Handle(MessageAddedEvent notification, CancellationToken cancellationToken)
    {
        _logger.LogInformation("处理消息添加事件: 消息ID {MessageId}, 对话ID {ConversationId}, 发送者类型 {SenderType}, 内容长度 {ContentLength}",
            notification.MessageId, notification.ConversationId, notification.SenderType, notification.ContentLength);

        // 这里可以添加消息添加后的业务逻辑
        // 例如：内容审核、实时推送、统计更新等

        await Task.CompletedTask;
    }
}

/// <summary>
/// 对话归档事件处理器
/// </summary>
public class ConversationArchivedEventHandler : INotificationHandler<ConversationArchivedEvent>
{
    private readonly ILogger<ConversationArchivedEventHandler> _logger;

    /// <summary>
    /// 初始化对话归档事件处理器
    /// </summary>
    /// <param name="logger">日志记录器</param>
    public ConversationArchivedEventHandler(ILogger<ConversationArchivedEventHandler> logger)
    {
        _logger = logger;
    }

    /// <summary>
    /// 处理对话归档事件
    /// </summary>
    /// <param name="notification">事件通知</param>
    /// <param name="cancellationToken">取消令牌</param>
    public async Task Handle(ConversationArchivedEvent notification, CancellationToken cancellationToken)
    {
        _logger.LogInformation("处理对话归档事件: 对话ID {ConversationId}, 用户ID {UserId}, 状态 {Status}",
            notification.ConversationId, notification.UserId, notification.Status);

        // 这里可以添加对话归档后的业务逻辑
        // 例如：数据压缩、索引更新、清理缓存等

        await Task.CompletedTask;
    }
}

/// <summary>
/// Token消耗事件处理器
/// </summary>
public class TokensConsumedEventHandler : INotificationHandler<TokensConsumedEvent>
{
    private readonly ILogger<TokensConsumedEventHandler> _logger;

    /// <summary>
    /// 初始化Token消耗事件处理器
    /// </summary>
    /// <param name="logger">日志记录器</param>
    public TokensConsumedEventHandler(ILogger<TokensConsumedEventHandler> logger)
    {
        _logger = logger;
    }

    /// <summary>
    /// 处理Token消耗事件
    /// </summary>
    /// <param name="notification">事件通知</param>
    /// <param name="cancellationToken">取消令牌</param>
    public async Task Handle(TokensConsumedEvent notification, CancellationToken cancellationToken)
    {
        _logger.LogInformation("处理Token消耗事件: 对话ID {ConversationId}, 用户ID {UserId}, 智能体ID {AgentId}, 使用量 {TokensUsed}",
            notification.ConversationId, notification.UserId, notification.AgentId, notification.TokensUsed);

        // 这里可以添加Token消耗后的业务逻辑
        // 例如：更新配额、计费统计、使用量监控等

        await Task.CompletedTask;
    }
}

/// <summary>
/// 消息删除事件处理器
/// </summary>
public class MessageDeletedEventHandler : INotificationHandler<MessageDeletedEvent>
{
    private readonly ILogger<MessageDeletedEventHandler> _logger;

    /// <summary>
    /// 初始化消息删除事件处理器
    /// </summary>
    /// <param name="logger">日志记录器</param>
    public MessageDeletedEventHandler(ILogger<MessageDeletedEventHandler> logger)
    {
        _logger = logger;
    }

    /// <summary>
    /// 处理消息删除事件
    /// </summary>
    /// <param name="notification">事件通知</param>
    /// <param name="cancellationToken">取消令牌</param>
    public async Task Handle(MessageDeletedEvent notification, CancellationToken cancellationToken)
    {
        _logger.LogInformation("处理消息删除事件: 消息ID {MessageId}, 对话ID {ConversationId}, 发送者类型 {SenderType}",
            notification.MessageId, notification.ConversationId, notification.SenderType);

        // 这里可以添加消息删除后的业务逻辑
        // 例如：清理相关文件、更新统计、记录审计日志等

        await Task.CompletedTask;
    }
}
