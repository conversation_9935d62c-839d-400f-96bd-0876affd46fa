using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System.Linq.Expressions;
using Whimlab.AI.Agent.Platform.Domain.Core.Common;
using Whimlab.AI.Agent.Platform.Infrastructure.Data;

namespace Whimlab.AI.Agent.Platform.Infrastructure.Repositories;

/// <summary>
/// 基础仓储实现
/// </summary>
/// <typeparam name="TEntity">实体类型</typeparam>
/// <typeparam name="TKey">主键类型</typeparam>
public abstract class BaseRepository<TEntity, TKey>
    where TEntity : class, AggregateRoot<TKey>
    where TKey : IEquatable<TKey>
{
    protected readonly AgentPlatformDbContext _context;
    protected readonly DbSet<TEntity> _dbSet;
    protected readonly ILogger _logger;

    /// <summary>
    /// 初始化基础仓储
    /// </summary>
    /// <param name="context">数据库上下文</param>
    /// <param name="logger">日志记录器</param>
    protected BaseRepository(AgentPlatformDbContext context, ILogger logger)
    {
        _context = context ?? throw new ArgumentNullException(nameof(context));
        _dbSet = _context.Set<TEntity>();
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// 根据ID获取实体
    /// </summary>
    /// <param name="id">实体ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>实体</returns>
    public virtual async Task<TEntity?> GetByIdAsync(TKey id, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("获取实体 {EntityType}，ID: {Id}", typeof(TEntity).Name, id);

            var entity = await _dbSet.FindAsync(new object[] { id }, cancellationToken);

            if (entity == null)
            {
                _logger.LogDebug("未找到实体 {EntityType}，ID: {Id}", typeof(TEntity).Name, id);
            }

            return entity;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取实体时发生错误，类型: {EntityType}，ID: {Id}", typeof(TEntity).Name, id);
            throw;
        }
    }



    /// <summary>
    /// 获取所有实体
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>实体列表</returns>
    public virtual async Task<List<TEntity>> GetAllAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("获取所有实体 {EntityType}", typeof(TEntity).Name);

            return await _dbSet.ToListAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取所有实体时发生错误，类型: {EntityType}", typeof(TEntity).Name);
            throw;
        }
    }

    /// <summary>
    /// 添加实体
    /// </summary>
    /// <param name="entity">实体</param>
    /// <param name="cancellationToken">取消令牌</param>
    public virtual async Task AddAsync(TEntity entity, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("添加实体 {EntityType}，ID: {Id}", typeof(TEntity).Name, entity.Id);

            await _dbSet.AddAsync(entity, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "添加实体时发生错误，类型: {EntityType}，ID: {Id}", typeof(TEntity).Name, entity.Id);
            throw;
        }
    }

    /// <summary>
    /// 更新实体
    /// </summary>
    /// <param name="entity">实体</param>
    /// <param name="cancellationToken">取消令牌</param>
    public virtual Task UpdateAsync(TEntity entity, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("更新实体 {EntityType}，ID: {Id}", typeof(TEntity).Name, entity.Id);

            _dbSet.Update(entity);
            return Task.CompletedTask;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新实体时发生错误，类型: {EntityType}，ID: {Id}", typeof(TEntity).Name, entity.Id);
            throw;
        }
    }

    /// <summary>
    /// 删除实体
    /// </summary>
    /// <param name="entity">实体</param>
    /// <param name="cancellationToken">取消令牌</param>
    public virtual Task DeleteAsync(TEntity entity, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("删除实体 {EntityType}，ID: {Id}", typeof(TEntity).Name, entity.Id);

            _dbSet.Remove(entity);
            return Task.CompletedTask;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "删除实体时发生错误，类型: {EntityType}，ID: {Id}", typeof(TEntity).Name, entity.Id);
            throw;
        }
    }

    /// <summary>
    /// 检查实体是否存在
    /// </summary>
    /// <param name="predicate">条件表达式</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>是否存在</returns>
    public virtual async Task<bool> ExistsAsync(
        Expression<Func<TEntity, bool>> predicate, 
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("检查实体是否存在 {EntityType}", typeof(TEntity).Name);

            return await _dbSet.AnyAsync(predicate, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "检查实体是否存在时发生错误，类型: {EntityType}", typeof(TEntity).Name);
            throw;
        }
    }

    /// <summary>
    /// 获取实体数量
    /// </summary>
    /// <param name="predicate">条件表达式</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>实体数量</returns>
    public virtual async Task<int> CountAsync(
        Expression<Func<TEntity, bool>>? predicate = null, 
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("获取实体数量 {EntityType}", typeof(TEntity).Name);

            if (predicate == null)
            {
                return await _dbSet.CountAsync(cancellationToken);
            }

            return await _dbSet.CountAsync(predicate, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取实体数量时发生错误，类型: {EntityType}", typeof(TEntity).Name);
            throw;
        }
    }

    /// <summary>
    /// 保存更改
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>受影响的行数</returns>
    public virtual async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("保存数据库更改");

            return await _context.SaveChangesAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "保存数据库更改时发生错误");
            throw;
        }
    }


}
