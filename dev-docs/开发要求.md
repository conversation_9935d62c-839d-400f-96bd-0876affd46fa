**功能完整性验证：**

**代码实现完整性检查：**
1. 验证所有仓储实现是否为真实的、可执行的代码实现，不允许存在：
- TODO标记或占位符- 简化的假实现（如直接返回空值或默认值）
- 仅有注释但未实现的方法- throw NotImplementedException的方法
2. 确保所有接口方法都有完整的业务逻辑实现
3. 检查所有异步方法是否正确实现了CancellationToken支持

**代码质量要求：**
- 遵循企业级开发标准
- 包含详尽的中文注释
- 实现完整的异常处理
- 确保所有代码能够成功编译
- 遵循已建立的代码风格和架构模式
- 包含完整的中文注释和错误处理
- 实现高性能的数据库查询优化
- 支持事务管理和并发控制

**代码质量检查：**
- 验证所有代码是否遵循DDD+CQRS架构模式
- 检查异常处理是否完整且符合企业级标准
- 确认中文注释和XML文档是否详尽
- 验证编译是否无警告和错误

**遗漏和疏忽识别：**
- 识别可能遗漏的边界条件处理
- 检查是否有未实现的关键业务逻辑
- 验证错误处理和日志记录的完整性
- 识别是否有未实现的接口方法
- 检查是否有缺失的依赖注入配置
- 验证实体配置是否完整且与实体定义匹配
- 确认所有必要的using语句和命名空间引用

**执行要求：**
- 每完成一个阶段后必须进行完整的编译验证
- 保持中文注释和企业级代码质量标准
- 遵循DDD和CQRS架构模式
- 每个修复步骤都要提供具体的代码实现，不允许TODO或占位符
- 最终交付必须是可编译、可运行的生产级代码

**验证要求(包括但是不限于以下几点,你可以根据需要对验证要求进行补充或者优化)：**
- 每完成一个子任务后立即进行编译验证
- 确保所有层项目都能成功编译
- 确保实现的正确性和业务逻辑的完整性
- 保持与现有代码的架构一致性和代码质量标准
- 提供实现进度报告