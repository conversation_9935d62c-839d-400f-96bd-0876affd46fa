using AutoMapper;
using Microsoft.Extensions.Logging;
using Whimlab.AI.Agent.Platform.Application.Core.Common;
using Whimlab.AI.Agent.Platform.Application.Subscription.Commands;
using Whimlab.AI.Agent.Platform.Application.Subscription.DTOs;
using Whimlab.AI.Agent.Platform.Domain.Subscription.Entities;
using Whimlab.AI.Agent.Platform.Domain.Subscription.Repositories;
using Whimlab.AI.Agent.Platform.Domain.Subscription.ValueObjects;

namespace Whimlab.AI.Agent.Platform.Application.Subscription.Handlers;

/// <summary>
/// 创建订阅计划命令处理器
/// </summary>
public class CreateSubscriptionPlanCommandHandler : ICommandHandler<CreateSubscriptionPlanCommand, SubscriptionPlanDto>
{
    private readonly ISubscriptionPlanRepository _subscriptionPlanRepository;
    private readonly IMapper _mapper;
    private readonly ILogger<CreateSubscriptionPlanCommandHandler> _logger;

    /// <summary>
    /// 初始化创建订阅计划命令处理器
    /// </summary>
    public CreateSubscriptionPlanCommandHandler(
        ISubscriptionPlanRepository subscriptionPlanRepository,
        IMapper mapper,
        ILogger<CreateSubscriptionPlanCommandHandler> logger)
    {
        _subscriptionPlanRepository = subscriptionPlanRepository;
        _mapper = mapper;
        _logger = logger;
    }

    /// <summary>
    /// 处理创建订阅计划命令
    /// </summary>
    public async Task<Result<SubscriptionPlanDto>> Handle(CreateSubscriptionPlanCommand request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("开始创建订阅计划: {Name}", request.Name);

            // 检查计划代码是否已存在
            var planCode = request.Name.ToUpperInvariant().Replace(" ", "_");
            var existingPlan = await _subscriptionPlanRepository.ExistsByPlanCodeAsync(planCode, null, cancellationToken);
            if (existingPlan)
            {
                return Result.Failure<SubscriptionPlanDto>("订阅计划代码已存在", "PLAN_CODE_EXISTS");
            }

            // 创建价格信息
            var monthlyPrice = Money.Create(request.MonthlyPrice, "USD");
            var yearlyPrice = Money.Create(request.YearlyPrice, "USD");

            // 创建订阅计划实体
            var subscriptionPlan = SubscriptionPlan.Create(
                request.Name,
                request.Description,
                planCode,
                monthlyPrice,
                yearlyPrice,
                0, // 试用天数
                null, // 最大用户数
                request.SortOrder);

            // 添加功能
            foreach (var feature in request.Features)
            {
                subscriptionPlan.AddFeature(feature);
            }

            // 添加配额
            foreach (var quotaLimitDto in request.QuotaLimits)
            {
                subscriptionPlan.AddQuota(
                    quotaLimitDto.QuotaType,
                    quotaLimitDto.LimitValue,
                    quotaLimitDto.ResetPeriod,
                    quotaLimitDto.QuotaType);
            }

            // 保存订阅计划
            await _subscriptionPlanRepository.AddAsync(subscriptionPlan, cancellationToken);
            await _subscriptionPlanRepository.SaveChangesAsync(cancellationToken);

            // 重新获取订阅计划
            var createdPlan = await _subscriptionPlanRepository.GetByIdAsync(subscriptionPlan.Id, cancellationToken);
            if (createdPlan == null)
            {
                return Result.Failure<SubscriptionPlanDto>("订阅计划创建失败", "PLAN_CREATION_FAILED");
            }

            var planDto = _mapper.Map<SubscriptionPlanDto>(createdPlan);

            _logger.LogInformation("订阅计划创建成功: {PlanId} - {Name}", subscriptionPlan.Id, request.Name);

            return Result.Success(planDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "创建订阅计划时发生错误: {Name}", request.Name);
            return Result.Failure<SubscriptionPlanDto>("创建订阅计划时发生内部错误", "INTERNAL_ERROR");
        }
    }
}

/// <summary>
/// 更新订阅计划命令处理器
/// </summary>
public class UpdateSubscriptionPlanCommandHandler : ICommandHandler<UpdateSubscriptionPlanCommand, SubscriptionPlanDto>
{
    private readonly ISubscriptionPlanRepository _subscriptionPlanRepository;
    private readonly IMapper _mapper;
    private readonly ILogger<UpdateSubscriptionPlanCommandHandler> _logger;

    /// <summary>
    /// 初始化更新订阅计划命令处理器
    /// </summary>
    public UpdateSubscriptionPlanCommandHandler(
        ISubscriptionPlanRepository subscriptionPlanRepository,
        IMapper mapper,
        ILogger<UpdateSubscriptionPlanCommandHandler> logger)
    {
        _subscriptionPlanRepository = subscriptionPlanRepository;
        _mapper = mapper;
        _logger = logger;
    }

    /// <summary>
    /// 处理更新订阅计划命令
    /// </summary>
    public async Task<Result<SubscriptionPlanDto>> Handle(UpdateSubscriptionPlanCommand request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("开始更新订阅计划: {PlanId}", request.Id);

            // 获取订阅计划
            var subscriptionPlan = await _subscriptionPlanRepository.GetByIdAsync(request.Id, cancellationToken);
            if (subscriptionPlan == null)
            {
                return Result.Failure<SubscriptionPlanDto>("订阅计划不存在", "PLAN_NOT_FOUND");
            }

            // 更新订阅计划信息
            var monthlyPrice = Money.Create(request.MonthlyPrice, "USD");
            var yearlyPrice = Money.Create(request.YearlyPrice, "USD");

            subscriptionPlan.UpdateInfo(request.Name, request.Description, null, null, request.SortOrder);
            subscriptionPlan.UpdatePricing(monthlyPrice, yearlyPrice);

            // 清除现有功能并添加新功能
            foreach (var existingFeature in subscriptionPlan.Features.ToList())
            {
                subscriptionPlan.RemoveFeature(existingFeature.Name);
            }

            foreach (var feature in request.Features)
            {
                subscriptionPlan.AddFeature(feature);
            }

            // 保存更改
            await _subscriptionPlanRepository.UpdateAsync(subscriptionPlan, cancellationToken);
            await _subscriptionPlanRepository.SaveChangesAsync(cancellationToken);

            // 重新获取订阅计划
            var updatedPlan = await _subscriptionPlanRepository.GetByIdAsync(subscriptionPlan.Id, cancellationToken);
            var planDto = _mapper.Map<SubscriptionPlanDto>(updatedPlan);

            _logger.LogInformation("订阅计划更新成功: {PlanId}", request.Id);

            return Result.Success(planDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新订阅计划时发生错误: {PlanId}", request.Id);
            return Result.Failure<SubscriptionPlanDto>("更新订阅计划时发生内部错误", "INTERNAL_ERROR");
        }
    }
}

/// <summary>
/// 创建客户订阅命令处理器
/// </summary>
public class CreateCustomerSubscriptionCommandHandler : ICommandHandler<CreateCustomerSubscriptionCommand, CustomerSubscriptionDto>
{
    private readonly ICustomerSubscriptionRepository _customerSubscriptionRepository;
    private readonly ISubscriptionPlanRepository _subscriptionPlanRepository;
    private readonly IMapper _mapper;
    private readonly ILogger<CreateCustomerSubscriptionCommandHandler> _logger;

    /// <summary>
    /// 初始化创建客户订阅命令处理器
    /// </summary>
    public CreateCustomerSubscriptionCommandHandler(
        ICustomerSubscriptionRepository customerSubscriptionRepository,
        ISubscriptionPlanRepository subscriptionPlanRepository,
        IMapper mapper,
        ILogger<CreateCustomerSubscriptionCommandHandler> logger)
    {
        _customerSubscriptionRepository = customerSubscriptionRepository;
        _subscriptionPlanRepository = subscriptionPlanRepository;
        _mapper = mapper;
        _logger = logger;
    }

    /// <summary>
    /// 处理创建客户订阅命令
    /// </summary>
    public async Task<Result<CustomerSubscriptionDto>> Handle(CreateCustomerSubscriptionCommand request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("开始创建客户订阅: 客户 {CustomerId}, 计划 {PlanId}", request.CustomerId, request.SubscriptionPlanId);

            // 检查订阅计划是否存在
            var subscriptionPlan = await _subscriptionPlanRepository.GetByIdAsync(request.SubscriptionPlanId, cancellationToken);
            if (subscriptionPlan == null)
            {
                return Result.Failure<CustomerSubscriptionDto>("订阅计划不存在", "PLAN_NOT_FOUND");
            }

            // 检查客户是否已有活跃订阅
            var existingSubscription = await _customerSubscriptionRepository.GetActiveByCustomerIdAsync(request.CustomerId, cancellationToken);
            if (existingSubscription != null)
            {
                return Result.Failure<CustomerSubscriptionDto>("客户已有活跃订阅", "ACTIVE_SUBSCRIPTION_EXISTS");
            }

            // 创建客户订阅实体
            var customerSubscription = CustomerSubscription.Create(
                request.CustomerId,
                request.SubscriptionPlanId,
                request.BillingCycle,
                request.StartDate,
                request.TrialDays ?? 0,
                request.AutoRenew);

            // 保存客户订阅
            await _customerSubscriptionRepository.AddAsync(customerSubscription, cancellationToken);
            await _customerSubscriptionRepository.SaveChangesAsync(cancellationToken);

            // 重新获取客户订阅
            var createdSubscription = await _customerSubscriptionRepository.GetByIdAsync(customerSubscription.Id, cancellationToken);
            if (createdSubscription == null)
            {
                return Result.Failure<CustomerSubscriptionDto>("客户订阅创建失败", "SUBSCRIPTION_CREATION_FAILED");
            }

            var subscriptionDto = _mapper.Map<CustomerSubscriptionDto>(createdSubscription);

            _logger.LogInformation("客户订阅创建成功: {SubscriptionId}", customerSubscription.Id);

            return Result.Success(subscriptionDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "创建客户订阅时发生错误: 客户 {CustomerId}", request.CustomerId);
            return Result.Failure<CustomerSubscriptionDto>("创建客户订阅时发生内部错误", "INTERNAL_ERROR");
        }
    }
}

/// <summary>
/// 使用配额命令处理器
/// </summary>
public class UseQuotaCommandHandler : ICommandHandler<UseQuotaCommand, bool>
{
    private readonly ICustomerSubscriptionRepository _customerSubscriptionRepository;
    private readonly ILogger<UseQuotaCommandHandler> _logger;

    /// <summary>
    /// 初始化使用配额命令处理器
    /// </summary>
    public UseQuotaCommandHandler(
        ICustomerSubscriptionRepository customerSubscriptionRepository,
        ILogger<UseQuotaCommandHandler> logger)
    {
        _customerSubscriptionRepository = customerSubscriptionRepository;
        _logger = logger;
    }

    /// <summary>
    /// 处理使用配额命令
    /// </summary>
    public async Task<Result<bool>> Handle(UseQuotaCommand request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("开始使用配额: 客户 {CustomerId}, 类型 {QuotaType}, 数量 {Amount}", 
                request.CustomerId, request.QuotaType, request.Amount);

            // 获取客户当前订阅
            var subscription = await _customerSubscriptionRepository.GetActiveByCustomerIdAsync(request.CustomerId, cancellationToken);
            if (subscription == null)
            {
                return Result.Failure<bool>("客户没有活跃订阅", "NO_ACTIVE_SUBSCRIPTION");
            }

            // 使用配额
            var success = subscription.UseQuota(request.QuotaType, request.Amount, request.Description);
            if (!success)
            {
                return Result.Success(false); // 配额不足
            }

            // 保存更改
            await _customerSubscriptionRepository.UpdateAsync(subscription, cancellationToken);
            await _customerSubscriptionRepository.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("配额使用成功: 客户 {CustomerId}, 类型 {QuotaType}, 数量 {Amount}", 
                request.CustomerId, request.QuotaType, request.Amount);

            return Result.Success(true);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "使用配额时发生错误: 客户 {CustomerId}", request.CustomerId);
            return Result.Failure<bool>("使用配额时发生内部错误", "INTERNAL_ERROR");
        }
    }
}
