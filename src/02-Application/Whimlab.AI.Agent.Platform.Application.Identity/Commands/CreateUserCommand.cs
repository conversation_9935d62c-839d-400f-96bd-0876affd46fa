using Whimlab.AI.Agent.Platform.Application.Core.Common;
using Whimlab.AI.Agent.Platform.Application.Identity.DTOs;
using Whimlab.AI.Agent.Platform.Shared.Common.Enums;

namespace Whimlab.AI.Agent.Platform.Application.Identity.Commands;

/// <summary>
/// 创建用户命令
/// </summary>
public class CreateUserCommand : ICommand<UserDto>
{
    /// <summary>
    /// 用户名
    /// </summary>
    public string Username { get; set; } = string.Empty;

    /// <summary>
    /// 邮箱地址
    /// </summary>
    public string Email { get; set; } = string.Empty;

    /// <summary>
    /// 密码
    /// </summary>
    public string Password { get; set; } = string.Empty;

    /// <summary>
    /// 确认密码
    /// </summary>
    public string ConfirmPassword { get; set; } = string.Empty;

    /// <summary>
    /// 手机号
    /// </summary>
    public string? PhoneNumber { get; set; }

    /// <summary>
    /// 显示名称
    /// </summary>
    public string? DisplayName { get; set; }

    /// <summary>
    /// 用户类型
    /// </summary>
    public UserType UserType { get; set; } = UserType.Customer;

    /// <summary>
    /// 角色ID列表
    /// </summary>
    public List<Guid> RoleIds { get; set; } = new();
}

/// <summary>
/// 更新用户命令
/// </summary>
public class UpdateUserCommand : ICommand<UserDto>
{
    /// <summary>
    /// 用户ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// 显示名称
    /// </summary>
    public string? DisplayName { get; set; }

    /// <summary>
    /// 手机号
    /// </summary>
    public string? PhoneNumber { get; set; }

    /// <summary>
    /// 头像URL
    /// </summary>
    public string? AvatarUrl { get; set; }
}

/// <summary>
/// 删除用户命令
/// </summary>
public class DeleteUserCommand : ICommand
{
    /// <summary>
    /// 用户ID
    /// </summary>
    public Guid Id { get; set; }
}

/// <summary>
/// 激活用户命令
/// </summary>
public class ActivateUserCommand : ICommand
{
    /// <summary>
    /// 用户ID
    /// </summary>
    public Guid Id { get; set; }
}

/// <summary>
/// 停用用户命令
/// </summary>
public class DeactivateUserCommand : ICommand
{
    /// <summary>
    /// 用户ID
    /// </summary>
    public Guid Id { get; set; }
}

/// <summary>
/// 修改密码命令
/// </summary>
public class ChangePasswordCommand : ICommand
{
    /// <summary>
    /// 用户ID
    /// </summary>
    public Guid UserId { get; set; }

    /// <summary>
    /// 当前密码
    /// </summary>
    public string CurrentPassword { get; set; } = string.Empty;

    /// <summary>
    /// 新密码
    /// </summary>
    public string NewPassword { get; set; } = string.Empty;

    /// <summary>
    /// 确认新密码
    /// </summary>
    public string ConfirmNewPassword { get; set; } = string.Empty;
}

/// <summary>
/// 重置密码命令
/// </summary>
public class ResetPasswordCommand : ICommand
{
    /// <summary>
    /// 邮箱地址
    /// </summary>
    public string Email { get; set; } = string.Empty;

    /// <summary>
    /// 重置令牌
    /// </summary>
    public string Token { get; set; } = string.Empty;

    /// <summary>
    /// 新密码
    /// </summary>
    public string NewPassword { get; set; } = string.Empty;

    /// <summary>
    /// 确认新密码
    /// </summary>
    public string ConfirmNewPassword { get; set; } = string.Empty;
}

/// <summary>
/// 发送密码重置邮件命令
/// </summary>
public class SendPasswordResetEmailCommand : ICommand
{
    /// <summary>
    /// 邮箱地址
    /// </summary>
    public string Email { get; set; } = string.Empty;
}

/// <summary>
/// 验证邮箱命令
/// </summary>
public class VerifyEmailCommand : ICommand
{
    /// <summary>
    /// 用户ID
    /// </summary>
    public Guid UserId { get; set; }

    /// <summary>
    /// 验证码
    /// </summary>
    public string VerificationCode { get; set; } = string.Empty;
}

/// <summary>
/// 验证手机号命令
/// </summary>
public class VerifyPhoneCommand : ICommand
{
    /// <summary>
    /// 用户ID
    /// </summary>
    public Guid UserId { get; set; }

    /// <summary>
    /// 验证码
    /// </summary>
    public string VerificationCode { get; set; } = string.Empty;
}

/// <summary>
/// 分配角色命令
/// </summary>
public class AssignRolesToUserCommand : ICommand
{
    /// <summary>
    /// 用户ID
    /// </summary>
    public Guid UserId { get; set; }

    /// <summary>
    /// 角色ID列表
    /// </summary>
    public List<Guid> RoleIds { get; set; } = new();
}

/// <summary>
/// 移除用户角色命令
/// </summary>
public class RemoveRolesFromUserCommand : ICommand
{
    /// <summary>
    /// 用户ID
    /// </summary>
    public Guid UserId { get; set; }

    /// <summary>
    /// 角色ID列表
    /// </summary>
    public List<Guid> RoleIds { get; set; } = new();
}
