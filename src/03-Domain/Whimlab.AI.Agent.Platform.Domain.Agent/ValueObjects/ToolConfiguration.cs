using System.Text.Json;
using Whimlab.AI.Agent.Platform.Domain.Core.Common;

namespace Whimlab.AI.Agent.Platform.Domain.Agent.ValueObjects;

/// <summary>
/// 工具配置值对象
/// </summary>
public sealed class ToolConfiguration : ValueObject
{
    /// <summary>
    /// 工具名称
    /// </summary>
    public string Name { get; }

    /// <summary>
    /// 工具描述
    /// </summary>
    public string Description { get; }

    /// <summary>
    /// 工具类型
    /// </summary>
    public string Type { get; }

    /// <summary>
    /// 工具参数架构（JSON Schema）
    /// </summary>
    public string? ParametersSchema { get; }

    /// <summary>
    /// 工具配置（JSON格式）
    /// </summary>
    public string? Configuration { get; }

    /// <summary>
    /// 是否启用
    /// </summary>
    public bool IsEnabled { get; }

    /// <summary>
    /// 初始化工具配置
    /// </summary>
    /// <param name="name">工具名称</param>
    /// <param name="description">工具描述</param>
    /// <param name="type">工具类型</param>
    /// <param name="parametersSchema">参数架构</param>
    /// <param name="configuration">工具配置</param>
    /// <param name="isEnabled">是否启用</param>
    private ToolConfiguration(
        string name,
        string description,
        string type,
        string? parametersSchema,
        string? configuration,
        bool isEnabled)
    {
        Name = name;
        Description = description;
        Type = type;
        ParametersSchema = parametersSchema;
        Configuration = configuration;
        IsEnabled = isEnabled;
    }

    /// <summary>
    /// 创建工具配置
    /// </summary>
    /// <param name="name">工具名称</param>
    /// <param name="description">工具描述</param>
    /// <param name="type">工具类型</param>
    /// <param name="parametersSchema">参数架构</param>
    /// <param name="configuration">工具配置</param>
    /// <param name="isEnabled">是否启用</param>
    /// <returns>工具配置值对象</returns>
    public static ToolConfiguration Create(
        string name,
        string description,
        string type,
        string? parametersSchema = null,
        string? configuration = null,
        bool isEnabled = true)
    {
        if (string.IsNullOrWhiteSpace(name))
            throw new ArgumentException("工具名称不能为空", nameof(name));

        if (string.IsNullOrWhiteSpace(description))
            throw new ArgumentException("工具描述不能为空", nameof(description));

        if (string.IsNullOrWhiteSpace(type))
            throw new ArgumentException("工具类型不能为空", nameof(type));

        // 验证参数架构JSON格式
        if (!string.IsNullOrWhiteSpace(parametersSchema))
        {
            try
            {
                JsonDocument.Parse(parametersSchema);
            }
            catch (JsonException)
            {
                throw new ArgumentException("参数架构必须是有效的JSON格式", nameof(parametersSchema));
            }
        }

        // 验证配置JSON格式
        if (!string.IsNullOrWhiteSpace(configuration))
        {
            try
            {
                JsonDocument.Parse(configuration);
            }
            catch (JsonException)
            {
                throw new ArgumentException("工具配置必须是有效的JSON格式", nameof(configuration));
            }
        }

        return new ToolConfiguration(
            name.Trim(),
            description.Trim(),
            type.Trim(),
            parametersSchema?.Trim(),
            configuration?.Trim(),
            isEnabled);
    }

    /// <summary>
    /// 创建函数工具配置
    /// </summary>
    /// <param name="name">函数名称</param>
    /// <param name="description">函数描述</param>
    /// <param name="parametersSchema">参数架构</param>
    /// <param name="isEnabled">是否启用</param>
    /// <returns>函数工具配置</returns>
    public static ToolConfiguration CreateFunction(
        string name,
        string description,
        string? parametersSchema = null,
        bool isEnabled = true)
    {
        return Create(name, description, "function", parametersSchema, null, isEnabled);
    }

    /// <summary>
    /// 创建Web搜索工具配置
    /// </summary>
    /// <param name="searchEngine">搜索引擎</param>
    /// <param name="apiKey">API密钥</param>
    /// <param name="isEnabled">是否启用</param>
    /// <returns>Web搜索工具配置</returns>
    public static ToolConfiguration CreateWebSearch(
        string searchEngine = "google",
        string? apiKey = null,
        bool isEnabled = true)
    {
        var config = apiKey != null ? JsonSerializer.Serialize(new { apiKey }) : null;
        
        return Create(
            "web_search",
            "搜索互联网获取最新信息",
            "web_search",
            JsonSerializer.Serialize(new
            {
                type = "object",
                properties = new
                {
                    query = new { type = "string", description = "搜索查询" },
                    num_results = new { type = "integer", description = "结果数量", minimum = 1, maximum = 10 }
                },
                required = new[] { "query" }
            }),
            config,
            isEnabled);
    }

    /// <summary>
    /// 创建代码执行工具配置
    /// </summary>
    /// <param name="language">编程语言</param>
    /// <param name="timeout">超时时间（秒）</param>
    /// <param name="isEnabled">是否启用</param>
    /// <returns>代码执行工具配置</returns>
    public static ToolConfiguration CreateCodeExecution(
        string language = "python",
        int timeout = 30,
        bool isEnabled = true)
    {
        var config = JsonSerializer.Serialize(new { language, timeout });
        
        return Create(
            "code_execution",
            "执行代码并返回结果",
            "code_execution",
            JsonSerializer.Serialize(new
            {
                type = "object",
                properties = new
                {
                    code = new { type = "string", description = "要执行的代码" },
                    language = new { type = "string", description = "编程语言", @enum = new[] { "python", "javascript", "bash" } }
                },
                required = new[] { "code" }
            }),
            config,
            isEnabled);
    }

    /// <summary>
    /// 创建文件操作工具配置
    /// </summary>
    /// <param name="allowedOperations">允许的操作</param>
    /// <param name="maxFileSize">最大文件大小（字节）</param>
    /// <param name="isEnabled">是否启用</param>
    /// <returns>文件操作工具配置</returns>
    public static ToolConfiguration CreateFileOperations(
        string[] allowedOperations,
        long maxFileSize = 10 * 1024 * 1024,
        bool isEnabled = true)
    {
        var config = JsonSerializer.Serialize(new { allowedOperations, maxFileSize });
        
        return Create(
            "file_operations",
            "文件读写和操作",
            "file_operations",
            JsonSerializer.Serialize(new
            {
                type = "object",
                properties = new
                {
                    operation = new { type = "string", description = "操作类型", @enum = allowedOperations },
                    path = new { type = "string", description = "文件路径" },
                    content = new { type = "string", description = "文件内容（写入时）" }
                },
                required = new[] { "operation", "path" }
            }),
            config,
            isEnabled);
    }

    /// <summary>
    /// 更新启用状态
    /// </summary>
    /// <param name="isEnabled">是否启用</param>
    /// <returns>新的配置对象</returns>
    public ToolConfiguration WithEnabled(bool isEnabled)
    {
        return Create(Name, Description, Type, ParametersSchema, Configuration, isEnabled);
    }

    /// <summary>
    /// 更新配置
    /// </summary>
    /// <param name="configuration">新的配置</param>
    /// <returns>新的配置对象</returns>
    public ToolConfiguration WithConfiguration(string? configuration)
    {
        return Create(Name, Description, Type, ParametersSchema, configuration, IsEnabled);
    }

    /// <summary>
    /// 获取相等性组件
    /// </summary>
    /// <returns>相等性组件集合</returns>
    protected override IEnumerable<object?> GetEqualityComponents()
    {
        yield return Name;
        yield return Description;
        yield return Type;
        yield return ParametersSchema;
        yield return Configuration;
        yield return IsEnabled;
    }

    /// <summary>
    /// 转换为字符串
    /// </summary>
    /// <returns>字符串表示</returns>
    public override string ToString()
    {
        return $"{Name} ({Type})" + (IsEnabled ? "" : " [Disabled]");
    }
}
