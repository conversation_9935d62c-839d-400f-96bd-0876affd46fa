using FluentValidation;
using Whimlab.AI.Agent.Platform.Application.Subscription.Commands;
using Whimlab.AI.Agent.Platform.Application.Subscription.DTOs;
using Whimlab.AI.Agent.Platform.Shared.Common.Enums;

namespace Whimlab.AI.Agent.Platform.Application.Subscription.Validators;

/// <summary>
/// 创建订阅计划命令验证器
/// </summary>
public class CreateSubscriptionPlanCommandValidator : AbstractValidator<CreateSubscriptionPlanCommand>
{
    /// <summary>
    /// 初始化验证器
    /// </summary>
    public CreateSubscriptionPlanCommandValidator()
    {
        RuleFor(x => x.Name)
            .NotEmpty().WithMessage("计划名称不能为空")
            .Length(2, 100).WithMessage("计划名称长度必须在2-100个字符之间");

        RuleFor(x => x.Description)
            .NotEmpty().WithMessage("计划描述不能为空")
            .Length(10, 1000).WithMessage("计划描述长度必须在10-1000个字符之间");

        RuleFor(x => x.PlanType)
            .IsInEnum().WithMessage("计划类型无效");

        RuleFor(x => x.MonthlyPrice)
            .GreaterThanOrEqualTo(0).WithMessage("月费价格不能为负数");

        RuleFor(x => x.YearlyPrice)
            .GreaterThanOrEqualTo(0).WithMessage("年费价格不能为负数");

        RuleFor(x => x.SortOrder)
            .GreaterThanOrEqualTo(0).WithMessage("排序顺序不能为负数");

        RuleFor(x => x.QuotaLimits)
            .NotNull().WithMessage("配额限制不能为空")
            .Must(quotaLimits => quotaLimits.Count > 0).WithMessage("至少需要一个配额限制");

        RuleForEach(x => x.QuotaLimits)
            .SetValidator(new CreateQuotaLimitDtoValidator());

        RuleFor(x => x.Features)
            .Must(features => features.Count <= 50).WithMessage("功能数量不能超过50个")
            .Must(features => features.All(f => !string.IsNullOrWhiteSpace(f) && f.Length <= 100))
            .WithMessage("每个功能描述长度不能超过100个字符且不能为空");

        RuleFor(x => x.OperatorId)
            .NotEmpty().WithMessage("操作者ID不能为空");
    }
}

/// <summary>
/// 更新订阅计划命令验证器
/// </summary>
public class UpdateSubscriptionPlanCommandValidator : AbstractValidator<UpdateSubscriptionPlanCommand>
{
    /// <summary>
    /// 初始化验证器
    /// </summary>
    public UpdateSubscriptionPlanCommandValidator()
    {
        RuleFor(x => x.Id)
            .NotEmpty().WithMessage("计划ID不能为空");

        RuleFor(x => x.Name)
            .NotEmpty().WithMessage("计划名称不能为空")
            .Length(2, 100).WithMessage("计划名称长度必须在2-100个字符之间");

        RuleFor(x => x.Description)
            .NotEmpty().WithMessage("计划描述不能为空")
            .Length(10, 1000).WithMessage("计划描述长度必须在10-1000个字符之间");

        RuleFor(x => x.MonthlyPrice)
            .GreaterThanOrEqualTo(0).WithMessage("月费价格不能为负数");

        RuleFor(x => x.YearlyPrice)
            .GreaterThanOrEqualTo(0).WithMessage("年费价格不能为负数");

        RuleFor(x => x.SortOrder)
            .GreaterThanOrEqualTo(0).WithMessage("排序顺序不能为负数");

        RuleFor(x => x.Features)
            .Must(features => features.Count <= 50).WithMessage("功能数量不能超过50个")
            .Must(features => features.All(f => !string.IsNullOrWhiteSpace(f) && f.Length <= 100))
            .WithMessage("每个功能描述长度不能超过100个字符且不能为空");

        RuleFor(x => x.OperatorId)
            .NotEmpty().WithMessage("操作者ID不能为空");
    }
}

/// <summary>
/// 创建配额限制DTO验证器
/// </summary>
public class CreateQuotaLimitDtoValidator : AbstractValidator<CreateQuotaLimitDto>
{
    /// <summary>
    /// 初始化验证器
    /// </summary>
    public CreateQuotaLimitDtoValidator()
    {
        RuleFor(x => x.QuotaType)
            .NotEmpty().WithMessage("配额类型不能为空")
            .MaximumLength(50).WithMessage("配额类型长度不能超过50个字符");

        RuleFor(x => x.LimitValue)
            .GreaterThan(0).WithMessage("限制值必须大于0")
            .When(x => !x.IsUnlimited);

        RuleFor(x => x.ResetPeriod)
            .NotEmpty().WithMessage("重置周期不能为空")
            .Must(BeValidResetPeriod).WithMessage("重置周期格式无效，支持：daily、weekly、monthly、yearly");
    }

    /// <summary>
    /// 验证重置周期格式
    /// </summary>
    private static bool BeValidResetPeriod(string resetPeriod)
    {
        var validPeriods = new[] { "daily", "weekly", "monthly", "yearly", "never" };
        return validPeriods.Contains(resetPeriod.ToLowerInvariant());
    }
}

/// <summary>
/// 创建客户订阅命令验证器
/// </summary>
public class CreateCustomerSubscriptionCommandValidator : AbstractValidator<CreateCustomerSubscriptionCommand>
{
    /// <summary>
    /// 初始化验证器
    /// </summary>
    public CreateCustomerSubscriptionCommandValidator()
    {
        RuleFor(x => x.CustomerId)
            .NotEmpty().WithMessage("客户ID不能为空");

        RuleFor(x => x.SubscriptionPlanId)
            .NotEmpty().WithMessage("订阅计划ID不能为空");

        RuleFor(x => x.BillingCycle)
            .IsInEnum().WithMessage("计费周期无效");

        RuleFor(x => x.StartDate)
            .GreaterThanOrEqualTo(DateTime.UtcNow.Date).WithMessage("开始时间不能早于今天");

        RuleFor(x => x.TrialDays)
            .GreaterThanOrEqualTo(0).WithMessage("试用天数不能为负数")
            .LessThanOrEqualTo(365).WithMessage("试用天数不能超过365天")
            .When(x => x.TrialDays.HasValue);

        RuleFor(x => x.PromoCode)
            .MaximumLength(50).WithMessage("促销代码长度不能超过50个字符")
            .When(x => !string.IsNullOrWhiteSpace(x.PromoCode));

        RuleFor(x => x.OperatorId)
            .NotEmpty().WithMessage("操作者ID不能为空");
    }
}

/// <summary>
/// 升级客户订阅命令验证器
/// </summary>
public class UpgradeCustomerSubscriptionCommandValidator : AbstractValidator<UpgradeCustomerSubscriptionCommand>
{
    /// <summary>
    /// 初始化验证器
    /// </summary>
    public UpgradeCustomerSubscriptionCommandValidator()
    {
        RuleFor(x => x.SubscriptionId)
            .NotEmpty().WithMessage("订阅ID不能为空");

        RuleFor(x => x.NewPlanId)
            .NotEmpty().WithMessage("新订阅计划ID不能为空");

        RuleFor(x => x.NewBillingCycle)
            .IsInEnum().WithMessage("新计费周期无效")
            .When(x => x.NewBillingCycle.HasValue);

        RuleFor(x => x.PromoCode)
            .MaximumLength(50).WithMessage("促销代码长度不能超过50个字符")
            .When(x => !string.IsNullOrWhiteSpace(x.PromoCode));

        RuleFor(x => x.OperatorId)
            .NotEmpty().WithMessage("操作者ID不能为空");
    }
}

/// <summary>
/// 取消客户订阅命令验证器
/// </summary>
public class CancelCustomerSubscriptionCommandValidator : AbstractValidator<CancelCustomerSubscriptionCommand>
{
    /// <summary>
    /// 初始化验证器
    /// </summary>
    public CancelCustomerSubscriptionCommandValidator()
    {
        RuleFor(x => x.SubscriptionId)
            .NotEmpty().WithMessage("订阅ID不能为空");

        RuleFor(x => x.CancellationReason)
            .NotEmpty().WithMessage("取消原因不能为空")
            .MaximumLength(500).WithMessage("取消原因长度不能超过500个字符");

        RuleFor(x => x.OperatorId)
            .NotEmpty().WithMessage("操作者ID不能为空");
    }
}

/// <summary>
/// 使用配额命令验证器
/// </summary>
public class UseQuotaCommandValidator : AbstractValidator<UseQuotaCommand>
{
    /// <summary>
    /// 初始化验证器
    /// </summary>
    public UseQuotaCommandValidator()
    {
        RuleFor(x => x.CustomerId)
            .NotEmpty().WithMessage("客户ID不能为空");

        RuleFor(x => x.QuotaType)
            .NotEmpty().WithMessage("配额类型不能为空")
            .MaximumLength(50).WithMessage("配额类型长度不能超过50个字符");

        RuleFor(x => x.Amount)
            .GreaterThan(0).WithMessage("使用量必须大于0");

        RuleFor(x => x.Description)
            .MaximumLength(200).WithMessage("使用描述长度不能超过200个字符")
            .When(x => !string.IsNullOrWhiteSpace(x.Description));

        RuleFor(x => x.OperatorId)
            .NotEmpty().WithMessage("操作者ID不能为空");
    }
}

/// <summary>
/// 重置配额命令验证器
/// </summary>
public class ResetQuotaCommandValidator : AbstractValidator<ResetQuotaCommand>
{
    /// <summary>
    /// 初始化验证器
    /// </summary>
    public ResetQuotaCommandValidator()
    {
        RuleFor(x => x.CustomerId)
            .NotEmpty().WithMessage("客户ID不能为空");

        RuleFor(x => x.QuotaType)
            .NotEmpty().WithMessage("配额类型不能为空")
            .MaximumLength(50).WithMessage("配额类型长度不能超过50个字符");

        RuleFor(x => x.OperatorId)
            .NotEmpty().WithMessage("操作者ID不能为空");
    }
}

/// <summary>
/// 批量重置配额命令验证器
/// </summary>
public class BatchResetQuotaCommandValidator : AbstractValidator<BatchResetQuotaCommand>
{
    /// <summary>
    /// 初始化验证器
    /// </summary>
    public BatchResetQuotaCommandValidator()
    {
        RuleFor(x => x.QuotaType)
            .NotEmpty().WithMessage("配额类型不能为空")
            .MaximumLength(50).WithMessage("配额类型长度不能超过50个字符");

        RuleFor(x => x.ResetPeriod)
            .NotEmpty().WithMessage("重置周期不能为空")
            .Must(BeValidResetPeriod).WithMessage("重置周期格式无效，支持：daily、weekly、monthly、yearly");

        RuleFor(x => x.BatchSize)
            .InclusiveBetween(1, 10000).WithMessage("批次大小必须在1-10000之间");

        RuleFor(x => x.OperatorId)
            .NotEmpty().WithMessage("操作者ID不能为空");
    }

    /// <summary>
    /// 验证重置周期格式
    /// </summary>
    private static bool BeValidResetPeriod(string resetPeriod)
    {
        var validPeriods = new[] { "daily", "weekly", "monthly", "yearly", "never" };
        return validPeriods.Contains(resetPeriod.ToLowerInvariant());
    }
}
