using AutoMapper;
using Whimlab.AI.Agent.Platform.Application.Identity.DTOs;
using Whimlab.AI.Agent.Platform.Domain.Identity.Entities;
using Whimlab.AI.Agent.Platform.Domain.Identity.ValueObjects;

namespace Whimlab.AI.Agent.Platform.Application.Identity.Mappings;

/// <summary>
/// 身份认证映射扩展方法
/// </summary>
public static class IdentityMappingExtensions
{
    /// <summary>
    /// 映射用户实体到DTO并填充角色信息
    /// </summary>
    /// <param name="mapper">AutoMapper实例</param>
    /// <param name="user">用户实体</param>
    /// <param name="roles">用户角色列表</param>
    /// <returns>用户DTO</returns>
    public static UserDto MapToDto(this IMapper mapper, User user, IEnumerable<RoleDto>? roles = null)
    {
        var dto = mapper.Map<UserDto>(user);

        // 填充角色信息
        dto.Roles = roles?.ToList() ?? new List<RoleDto>();

        return dto;
    }

    /// <summary>
    /// 映射角色实体到DTO并填充权限信息
    /// </summary>
    /// <param name="mapper">AutoMapper实例</param>
    /// <param name="role">角色实体</param>
    /// <param name="permissions">角色权限列表</param>
    /// <returns>角色DTO</returns>
    public static RoleDto MapToDto(this IMapper mapper, Role role, IEnumerable<PermissionDto>? permissions = null)
    {
        var dto = mapper.Map<RoleDto>(role);

        // 填充权限信息
        dto.Permissions = permissions?.ToList() ?? new List<PermissionDto>();

        return dto;
    }

    /// <summary>
    /// 批量映射用户实体列表到DTO列表
    /// </summary>
    /// <param name="mapper">AutoMapper实例</param>
    /// <param name="users">用户实体列表</param>
    /// <param name="userRoles">用户角色字典（UserId -> Roles）</param>
    /// <returns>用户DTO列表</returns>
    public static List<UserDto> MapToDtos(this IMapper mapper,
        IEnumerable<User> users,
        Dictionary<Guid, List<RoleDto>>? userRoles = null)
    {
        return users.Select(user =>
        {
            var roles = userRoles?.GetValueOrDefault(user.Id) ?? new List<RoleDto>();
            return mapper.MapToDto(user, roles);
        }).ToList();
    }

    /// <summary>
    /// 批量映射角色实体列表到DTO列表
    /// </summary>
    /// <param name="mapper">AutoMapper实例</param>
    /// <param name="roles">角色实体列表</param>
    /// <param name="rolePermissions">角色权限字典（RoleId -> Permissions）</param>
    /// <returns>角色DTO列表</returns>
    public static List<RoleDto> MapToDtos(this IMapper mapper,
        IEnumerable<Role> roles,
        Dictionary<Guid, List<PermissionDto>>? rolePermissions = null)
    {
        return roles.Select(role =>
        {
            var permissions = rolePermissions?.GetValueOrDefault(role.Id) ?? new List<PermissionDto>();
            return mapper.MapToDto(role, permissions);
        }).ToList();
    }

    /// <summary>
    /// 创建邮箱值对象的辅助方法
    /// </summary>
    /// <param name="email">邮箱地址</param>
    /// <returns>邮箱值对象</returns>
    public static Email CreateEmailFromString(string email)
    {
        return Email.Create(email);
    }

    /// <summary>
    /// 创建电话号码值对象的辅助方法
    /// </summary>
    /// <param name="phoneNumber">电话号码</param>
    /// <returns>电话号码值对象</returns>
    public static PhoneNumber? CreatePhoneNumberFromString(string? phoneNumber)
    {
        return string.IsNullOrWhiteSpace(phoneNumber) ? null : PhoneNumber.Create(phoneNumber);
    }

    /// <summary>
    /// 创建用户实体的辅助方法
    /// </summary>
    /// <param name="createUserDto">创建用户DTO</param>
    /// <returns>用户实体</returns>
    public static User CreateUserFromDto(CreateUserDto createUserDto)
    {
        // 这里需要根据实际的User构造函数来实现
        // 由于User可能有复杂的构造逻辑，这个方法应该在具体的处理器中实现
        throw new NotImplementedException("此方法应该在具体的命令处理器中实现");
    }

    /// <summary>
    /// 更新用户信息的辅助方法
    /// </summary>
    /// <param name="user">用户实体</param>
    /// <param name="updateUserDto">更新用户DTO</param>
    public static void UpdateUserFromDto(User user, UpdateUserDto updateUserDto)
    {
        // 更新显示名称
        if (!string.IsNullOrWhiteSpace(updateUserDto.DisplayName))
        {
            // 这里需要根据实际的User实体方法来实现
            // 由于User可能有特定的更新方法，这个方法应该在具体的处理器中实现
        }

        // 更新电话号码
        if (!string.IsNullOrWhiteSpace(updateUserDto.PhoneNumber))
        {
            var phoneNumber = PhoneNumber.Create(updateUserDto.PhoneNumber);
            // 这里需要根据实际的User实体方法来实现
        }

        // 更新头像URL
        if (!string.IsNullOrWhiteSpace(updateUserDto.AvatarUrl))
        {
            // 这里需要根据实际的User实体方法来实现
        }
    }

    /// <summary>
    /// 验证创建用户DTO的有效性
    /// </summary>
    /// <param name="createUserDto">创建用户DTO</param>
    /// <returns>验证结果</returns>
    public static (bool IsValid, List<string> Errors) ValidateCreateUserDto(CreateUserDto createUserDto)
    {
        var errors = new List<string>();

        if (string.IsNullOrWhiteSpace(createUserDto.Username))
        {
            errors.Add("用户名不能为空");
        }

        if (string.IsNullOrWhiteSpace(createUserDto.Email))
        {
            errors.Add("邮箱不能为空");
        }
        else if (!IsValidEmail(createUserDto.Email))
        {
            errors.Add("邮箱格式不正确");
        }

        if (string.IsNullOrWhiteSpace(createUserDto.DisplayName))
        {
            errors.Add("显示名称不能为空");
        }

        return (errors.Count == 0, errors);
    }

    /// <summary>
    /// 验证邮箱格式
    /// </summary>
    /// <param name="email">邮箱地址</param>
    /// <returns>是否有效</returns>
    private static bool IsValidEmail(string email)
    {
        try
        {
            var addr = new System.Net.Mail.MailAddress(email);
            return addr.Address == email;
        }
        catch
        {
            return false;
        }
    }
}
