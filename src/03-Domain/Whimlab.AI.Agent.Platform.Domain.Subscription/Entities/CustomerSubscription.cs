using Whimlab.AI.Agent.Platform.Domain.Core.Common;
using Whimlab.AI.Agent.Platform.Domain.Subscription.ValueObjects;
using Whimlab.AI.Agent.Platform.Domain.Subscription.Events;
using Whimlab.AI.Agent.Platform.Shared.Common.Enums;

namespace Whimlab.AI.Agent.Platform.Domain.Subscription.Entities;

/// <summary>
/// 客户订阅聚合根
/// </summary>
public class CustomerSubscription : AggregateRoot
{
    private readonly List<QuotaUsage> _quotaUsages = new();
    private readonly List<SubscriptionHistory> _history = new();

    /// <summary>
    /// 客户ID
    /// </summary>
    public Guid CustomerId { get; private set; }

    /// <summary>
    /// 订阅计划ID
    /// </summary>
    public Guid SubscriptionPlanId { get; private set; }

    /// <summary>
    /// 订阅状态
    /// </summary>
    public SubscriptionStatus Status { get; private set; }

    /// <summary>
    /// 计费周期
    /// </summary>
    public BillingCycle BillingCycle { get; private set; }

    /// <summary>
    /// 开始日期
    /// </summary>
    public DateTime StartDate { get; private set; }

    /// <summary>
    /// 结束日期
    /// </summary>
    public DateTime EndDate { get; private set; }

    /// <summary>
    /// 试用结束日期
    /// </summary>
    public DateTime? TrialEndDate { get; private set; }

    /// <summary>
    /// 取消日期
    /// </summary>
    public DateTime? CancelledAt { get; private set; }

    /// <summary>
    /// 取消原因
    /// </summary>
    public string? CancelReason { get; private set; }

    /// <summary>
    /// 是否自动续费
    /// </summary>
    public bool AutoRenew { get; private set; }

    /// <summary>
    /// 当前周期开始日期
    /// </summary>
    public DateTime CurrentPeriodStart { get; private set; }

    /// <summary>
    /// 当前周期结束日期
    /// </summary>
    public DateTime CurrentPeriodEnd { get; private set; }

    /// <summary>
    /// 订阅计划导航属性
    /// </summary>
    public SubscriptionPlan? SubscriptionPlan { get; private set; }

    /// <summary>
    /// 配额使用记录（只读）
    /// </summary>
    public IReadOnlyList<QuotaUsage> QuotaUsages => _quotaUsages.AsReadOnly();

    /// <summary>
    /// 订阅历史记录（只读）
    /// </summary>
    public IReadOnlyList<SubscriptionHistory> History => _history.AsReadOnly();

    /// <summary>
    /// 私有构造函数（用于EF Core）
    /// </summary>
    private CustomerSubscription() { }

    /// <summary>
    /// 创建新的客户订阅
    /// </summary>
    /// <param name="customerId">客户ID</param>
    /// <param name="subscriptionPlanId">订阅计划ID</param>
    /// <param name="billingCycle">计费周期</param>
    /// <param name="startDate">开始日期</param>
    /// <param name="trialDays">试用天数</param>
    /// <param name="autoRenew">是否自动续费</param>
    /// <returns>客户订阅实体</returns>
    public static CustomerSubscription Create(
        Guid customerId,
        Guid subscriptionPlanId,
        BillingCycle billingCycle,
        DateTime startDate,
        int trialDays = 0,
        bool autoRenew = true)
    {
        if (customerId == Guid.Empty)
            throw new ArgumentException("客户ID不能为空", nameof(customerId));

        if (subscriptionPlanId == Guid.Empty)
            throw new ArgumentException("订阅计划ID不能为空", nameof(subscriptionPlanId));

        var endDate = billingCycle switch
        {
            BillingCycle.Monthly => startDate.AddMonths(1),
            BillingCycle.Yearly => startDate.AddYears(1),
            _ => throw new ArgumentException($"不支持的计费周期: {billingCycle}")
        };

        var subscription = new CustomerSubscription
        {
            CustomerId = customerId,
            SubscriptionPlanId = subscriptionPlanId,
            BillingCycle = billingCycle,
            StartDate = startDate,
            EndDate = endDate,
            Status = trialDays > 0 ? SubscriptionStatus.Trialing : SubscriptionStatus.Active,
            TrialEndDate = trialDays > 0 ? startDate.AddDays(trialDays) : null,
            AutoRenew = autoRenew,
            CurrentPeriodStart = startDate,
            CurrentPeriodEnd = endDate
        };

        // 添加历史记录
        var historyEntry = SubscriptionHistory.Create(
            subscription.Id,
            SubscriptionStatus.Active,
            trialDays > 0 ? "试用期开始" : "订阅激活",
            startDate);
        subscription._history.Add(historyEntry);

        subscription.AddDomainEvent(new CustomerSubscriptionCreatedEvent(
            subscription.Id, customerId, subscriptionPlanId, billingCycle));

        return subscription;
    }

    /// <summary>
    /// 激活订阅
    /// </summary>
    public void Activate()
    {
        if (Status == SubscriptionStatus.Active)
            return;

        if (Status == SubscriptionStatus.Canceled || Status == SubscriptionStatus.Expired)
            throw new InvalidOperationException("已取消或过期的订阅不能激活");

        Status = SubscriptionStatus.Active;
        MarkAsModified();

        var historyEntry = SubscriptionHistory.Create(Id, Status, "订阅激活", DateTime.UtcNow);
        _history.Add(historyEntry);

        AddDomainEvent(new CustomerSubscriptionActivatedEvent(Id, CustomerId));
    }

    /// <summary>
    /// 取消订阅
    /// </summary>
    /// <param name="reason">取消原因</param>
    /// <param name="cancelledAt">取消时间</param>
    public void Cancel(string? reason = null, DateTime? cancelledAt = null)
    {
        if (Status == SubscriptionStatus.Canceled)
            return;

        Status = SubscriptionStatus.Canceled;
        CancelReason = reason;
        CancelledAt = cancelledAt ?? DateTime.UtcNow;
        AutoRenew = false;
        MarkAsModified();

        var historyEntry = SubscriptionHistory.Create(Id, Status, $"订阅取消: {reason}", CancelledAt.Value);
        _history.Add(historyEntry);

        AddDomainEvent(new CustomerSubscriptionCancelledEvent(Id, CustomerId, reason));
    }

    /// <summary>
    /// 续费订阅
    /// </summary>
    /// <param name="newEndDate">新的结束日期</param>
    public void Renew(DateTime? newEndDate = null)
    {
        if (Status != SubscriptionStatus.Active && Status != SubscriptionStatus.Trialing)
            throw new InvalidOperationException("只有活跃或试用状态的订阅才能续费");

        var renewalDate = newEndDate ?? (BillingCycle switch
        {
            BillingCycle.Monthly => EndDate.AddMonths(1),
            BillingCycle.Yearly => EndDate.AddYears(1),
            _ => throw new InvalidOperationException($"不支持的计费周期: {BillingCycle}")
        });

        CurrentPeriodStart = EndDate;
        CurrentPeriodEnd = renewalDate;
        EndDate = renewalDate;
        
        // 如果是试用期结束，转为正式订阅
        if (Status == SubscriptionStatus.Trialing)
        {
            Status = SubscriptionStatus.Active;
            TrialEndDate = null;
        }

        MarkAsModified();

        var historyEntry = SubscriptionHistory.Create(Id, Status, "订阅续费", DateTime.UtcNow);
        _history.Add(historyEntry);

        AddDomainEvent(new CustomerSubscriptionRenewedEvent(Id, CustomerId, renewalDate));
    }

    /// <summary>
    /// 标记为过期
    /// </summary>
    public void MarkAsExpired()
    {
        if (Status == SubscriptionStatus.Expired)
            return;

        Status = SubscriptionStatus.Expired;
        AutoRenew = false;
        MarkAsModified();

        var historyEntry = SubscriptionHistory.Create(Id, Status, "订阅过期", DateTime.UtcNow);
        _history.Add(historyEntry);

        AddDomainEvent(new CustomerSubscriptionExpiredEvent(Id, CustomerId));
    }

    /// <summary>
    /// 使用配额
    /// </summary>
    /// <param name="quotaType">配额类型</param>
    /// <param name="amount">使用量</param>
    /// <param name="description">描述</param>
    /// <returns>是否成功使用</returns>
    public bool UseQuota(string quotaType, long amount, string? description = null)
    {
        if (!IsActive())
            throw new InvalidOperationException("非活跃状态的订阅不能使用配额");

        var currentUsage = GetCurrentQuotaUsage(quotaType);
        var newUsage = currentUsage + amount;

        // 这里需要从订阅计划中获取配额限制，暂时返回true
        // 实际实现中需要注入订阅计划或通过领域服务检查
        
        var quotaUsage = QuotaUsage.Create(Id, quotaType, amount, newUsage, description);
        _quotaUsages.Add(quotaUsage);
        MarkAsModified();

        AddDomainEvent(new QuotaUsedEvent(CustomerId, quotaType, amount, newUsage));

        return true;
    }

    /// <summary>
    /// 重置配额
    /// </summary>
    /// <param name="quotaType">配额类型</param>
    public void ResetQuota(string quotaType)
    {
        var currentPeriodUsages = _quotaUsages
            .Where(q => q.QuotaType == quotaType && 
                       q.UsedAt >= CurrentPeriodStart && 
                       q.UsedAt <= CurrentPeriodEnd)
            .ToList();

        foreach (var usage in currentPeriodUsages)
        {
            _quotaUsages.Remove(usage);
        }

        MarkAsModified();

        AddDomainEvent(new QuotaResetEvent(CustomerId, quotaType, "monthly"));
    }

    /// <summary>
    /// 获取当前配额使用量
    /// </summary>
    /// <param name="quotaType">配额类型</param>
    /// <returns>当前使用量</returns>
    public long GetCurrentQuotaUsage(string quotaType)
    {
        return _quotaUsages
            .Where(q => q.QuotaType == quotaType && 
                       q.UsedAt >= CurrentPeriodStart && 
                       q.UsedAt <= CurrentPeriodEnd)
            .Sum(q => q.Amount);
    }

    /// <summary>
    /// 检查是否为活跃状态
    /// </summary>
    /// <returns>是否活跃</returns>
    public bool IsActive()
    {
        return Status == SubscriptionStatus.Active || Status == SubscriptionStatus.Trialing;
    }

    /// <summary>
    /// 检查是否在试用期
    /// </summary>
    /// <returns>是否在试用期</returns>
    public bool IsInTrial()
    {
        return Status == SubscriptionStatus.Trialing &&
               TrialEndDate.HasValue && 
               DateTime.UtcNow <= TrialEndDate.Value;
    }

    /// <summary>
    /// 检查是否即将过期
    /// </summary>
    /// <param name="warningDays">预警天数</param>
    /// <returns>是否即将过期</returns>
    public bool IsExpiringSoon(int warningDays = 7)
    {
        return IsActive() && DateTime.UtcNow.AddDays(warningDays) >= EndDate;
    }

    /// <summary>
    /// 检查是否需要续费
    /// </summary>
    /// <returns>是否需要续费</returns>
    public bool ShouldRenew()
    {
        return IsActive() && AutoRenew && DateTime.UtcNow >= EndDate;
    }
}

/// <summary>
/// 配额使用记录实体
/// </summary>
public class QuotaUsage : Entity
{
    /// <summary>
    /// 客户订阅ID
    /// </summary>
    public Guid CustomerSubscriptionId { get; private set; }

    /// <summary>
    /// 配额类型
    /// </summary>
    public string QuotaType { get; private set; } = string.Empty;

    /// <summary>
    /// 使用量
    /// </summary>
    public long Amount { get; private set; }

    /// <summary>
    /// 累计使用量
    /// </summary>
    public long CumulativeAmount { get; private set; }

    /// <summary>
    /// 使用时间
    /// </summary>
    public DateTime UsedAt { get; private set; }

    /// <summary>
    /// 描述
    /// </summary>
    public string? Description { get; private set; }

    /// <summary>
    /// 客户订阅导航属性
    /// </summary>
    public CustomerSubscription? CustomerSubscription { get; private set; }

    /// <summary>
    /// 私有构造函数（用于EF Core）
    /// </summary>
    private QuotaUsage() { }

    /// <summary>
    /// 创建配额使用记录
    /// </summary>
    /// <param name="customerSubscriptionId">客户订阅ID</param>
    /// <param name="quotaType">配额类型</param>
    /// <param name="amount">使用量</param>
    /// <param name="cumulativeAmount">累计使用量</param>
    /// <param name="description">描述</param>
    /// <returns>配额使用记录实体</returns>
    public static QuotaUsage Create(
        Guid customerSubscriptionId,
        string quotaType,
        long amount,
        long cumulativeAmount,
        string? description = null)
    {
        if (customerSubscriptionId == Guid.Empty)
            throw new ArgumentException("客户订阅ID不能为空", nameof(customerSubscriptionId));

        if (string.IsNullOrWhiteSpace(quotaType))
            throw new ArgumentException("配额类型不能为空", nameof(quotaType));

        if (amount <= 0)
            throw new ArgumentException("使用量必须大于0", nameof(amount));

        return new QuotaUsage
        {
            CustomerSubscriptionId = customerSubscriptionId,
            QuotaType = quotaType.Trim(),
            Amount = amount,
            CumulativeAmount = cumulativeAmount,
            UsedAt = DateTime.UtcNow,
            Description = description?.Trim()
        };
    }
}

/// <summary>
/// 订阅历史记录实体
/// </summary>
public class SubscriptionHistory : Entity
{
    /// <summary>
    /// 客户订阅ID
    /// </summary>
    public Guid CustomerSubscriptionId { get; private set; }

    /// <summary>
    /// 状态
    /// </summary>
    public SubscriptionStatus Status { get; private set; }

    /// <summary>
    /// 变更描述
    /// </summary>
    public string Description { get; private set; } = string.Empty;

    /// <summary>
    /// 变更时间
    /// </summary>
    public DateTime ChangedAt { get; private set; }

    /// <summary>
    /// 客户订阅导航属性
    /// </summary>
    public CustomerSubscription? CustomerSubscription { get; private set; }

    /// <summary>
    /// 私有构造函数（用于EF Core）
    /// </summary>
    private SubscriptionHistory() { }

    /// <summary>
    /// 创建订阅历史记录
    /// </summary>
    /// <param name="customerSubscriptionId">客户订阅ID</param>
    /// <param name="status">状态</param>
    /// <param name="description">变更描述</param>
    /// <param name="changedAt">变更时间</param>
    /// <returns>订阅历史记录实体</returns>
    public static SubscriptionHistory Create(
        Guid customerSubscriptionId,
        SubscriptionStatus status,
        string description,
        DateTime changedAt)
    {
        if (customerSubscriptionId == Guid.Empty)
            throw new ArgumentException("客户订阅ID不能为空", nameof(customerSubscriptionId));

        if (string.IsNullOrWhiteSpace(description))
            throw new ArgumentException("变更描述不能为空", nameof(description));

        return new SubscriptionHistory
        {
            CustomerSubscriptionId = customerSubscriptionId,
            Status = status,
            Description = description.Trim(),
            ChangedAt = changedAt
        };
    }
}
