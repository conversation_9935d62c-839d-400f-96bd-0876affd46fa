using Whimlab.AI.Agent.Platform.Domain.Agent.Entities;
using Whimlab.AI.Agent.Platform.Domain.Agent.ValueObjects;
using Whimlab.AI.Agent.Platform.Shared.Common.Enums;

namespace Whimlab.AI.Agent.Platform.Domain.Agent.Services;

/// <summary>
/// 智能体验证服务接口
/// </summary>
public interface IAgentValidationService
{
    /// <summary>
    /// 验证智能体名称是否可用
    /// </summary>
    /// <param name="name">智能体名称</param>
    /// <param name="createdBy">创建者ID</param>
    /// <param name="excludeAgentId">排除的智能体ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>是否可用</returns>
    Task<bool> IsAgentNameAvailableAsync(string name, Guid createdBy, Guid? excludeAgentId = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// 验证智能体配置
    /// </summary>
    /// <param name="configuration">智能体配置</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>验证结果</returns>
    Task<AgentConfigurationValidationResult> ValidateConfigurationAsync(AgentConfiguration configuration, CancellationToken cancellationToken = default);

    /// <summary>
    /// 验证模型配置
    /// </summary>
    /// <param name="modelConfig">模型配置</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>验证结果</returns>
    Task<ModelConfigurationValidationResult> ValidateModelConfigurationAsync(ModelConfiguration modelConfig, CancellationToken cancellationToken = default);

    /// <summary>
    /// 验证工具配置
    /// </summary>
    /// <param name="toolConfig">工具配置</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>验证结果</returns>
    Task<ToolConfigurationValidationResult> ValidateToolConfigurationAsync(ToolConfiguration toolConfig, CancellationToken cancellationToken = default);

    /// <summary>
    /// 验证系统提示词
    /// </summary>
    /// <param name="systemPrompt">系统提示词</param>
    /// <returns>验证结果</returns>
    SystemPromptValidationResult ValidateSystemPrompt(string systemPrompt);

    /// <summary>
    /// 验证智能体发布条件
    /// </summary>
    /// <param name="agent">智能体实体</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>验证结果</returns>
    Task<AgentPublishValidationResult> ValidatePublishConditionsAsync(Entities.Agent agent, CancellationToken cancellationToken = default);

    /// <summary>
    /// 检查用户是否有权限操作智能体
    /// </summary>
    /// <param name="agent">智能体实体</param>
    /// <param name="userId">用户ID</param>
    /// <param name="operation">操作类型</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>是否有权限</returns>
    Task<bool> HasPermissionAsync(Entities.Agent agent, Guid userId, AgentOperation operation, CancellationToken cancellationToken = default);

    /// <summary>
    /// 验证智能体标签
    /// </summary>
    /// <param name="tags">标签列表</param>
    /// <returns>验证结果</returns>
    TagValidationResult ValidateTags(IEnumerable<string> tags);

    /// <summary>
    /// 检查智能体是否可以删除
    /// </summary>
    /// <param name="agent">智能体实体</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>是否可以删除</returns>
    Task<bool> CanDeleteAgentAsync(Entities.Agent agent, CancellationToken cancellationToken = default);

    /// <summary>
    /// 验证智能体使用配额
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="agentType">智能体类型</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>配额验证结果</returns>
    Task<QuotaValidationResult> ValidateAgentQuotaAsync(Guid userId, AgentType agentType, CancellationToken cancellationToken = default);

    /// <summary>
    /// 测试模型连接
    /// </summary>
    /// <param name="modelConfig">模型配置</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>连接测试结果</returns>
    Task<ModelConnectionTestResult> TestModelConnectionAsync(ModelConfiguration modelConfig, CancellationToken cancellationToken = default);
}

/// <summary>
/// 智能体配置验证结果
/// </summary>
public class AgentConfigurationValidationResult
{
    /// <summary>
    /// 是否有效
    /// </summary>
    public bool IsValid { get; set; }

    /// <summary>
    /// 错误消息
    /// </summary>
    public List<string> Errors { get; set; } = new();

    /// <summary>
    /// 警告消息
    /// </summary>
    public List<string> Warnings { get; set; } = new();

    /// <summary>
    /// 建议
    /// </summary>
    public List<string> Suggestions { get; set; } = new();
}

/// <summary>
/// 模型配置验证结果
/// </summary>
public class ModelConfigurationValidationResult
{
    /// <summary>
    /// 是否有效
    /// </summary>
    public bool IsValid { get; set; }

    /// <summary>
    /// 错误消息
    /// </summary>
    public List<string> Errors { get; set; } = new();

    /// <summary>
    /// 警告消息
    /// </summary>
    public List<string> Warnings { get; set; } = new();

    /// <summary>
    /// 是否支持该模型
    /// </summary>
    public bool IsModelSupported { get; set; }

    /// <summary>
    /// 模型能力信息
    /// </summary>
    public ModelCapabilities? Capabilities { get; set; }
}

/// <summary>
/// 工具配置验证结果
/// </summary>
public class ToolConfigurationValidationResult
{
    /// <summary>
    /// 是否有效
    /// </summary>
    public bool IsValid { get; set; }

    /// <summary>
    /// 错误消息
    /// </summary>
    public List<string> Errors { get; set; } = new();

    /// <summary>
    /// 警告消息
    /// </summary>
    public List<string> Warnings { get; set; } = new();

    /// <summary>
    /// 是否支持该工具
    /// </summary>
    public bool IsToolSupported { get; set; }

    /// <summary>
    /// 工具依赖项
    /// </summary>
    public List<string> Dependencies { get; set; } = new();
}

/// <summary>
/// 系统提示词验证结果
/// </summary>
public class SystemPromptValidationResult
{
    /// <summary>
    /// 是否有效
    /// </summary>
    public bool IsValid { get; set; }

    /// <summary>
    /// 错误消息
    /// </summary>
    public List<string> Errors { get; set; } = new();

    /// <summary>
    /// 警告消息
    /// </summary>
    public List<string> Warnings { get; set; } = new();

    /// <summary>
    /// 字符数
    /// </summary>
    public int CharacterCount { get; set; }

    /// <summary>
    /// 估计Token数
    /// </summary>
    public int EstimatedTokenCount { get; set; }

    /// <summary>
    /// 复杂度评分
    /// </summary>
    public int ComplexityScore { get; set; }
}

/// <summary>
/// 智能体发布验证结果
/// </summary>
public class AgentPublishValidationResult
{
    /// <summary>
    /// 是否可以发布
    /// </summary>
    public bool CanPublish { get; set; }

    /// <summary>
    /// 阻止发布的错误
    /// </summary>
    public List<string> BlockingErrors { get; set; } = new();

    /// <summary>
    /// 警告消息
    /// </summary>
    public List<string> Warnings { get; set; } = new();

    /// <summary>
    /// 发布前检查项
    /// </summary>
    public List<PublishCheckItem> CheckItems { get; set; } = new();
}

/// <summary>
/// 标签验证结果
/// </summary>
public class TagValidationResult
{
    /// <summary>
    /// 是否有效
    /// </summary>
    public bool IsValid { get; set; }

    /// <summary>
    /// 有效的标签
    /// </summary>
    public List<string> ValidTags { get; set; } = new();

    /// <summary>
    /// 无效的标签
    /// </summary>
    public List<string> InvalidTags { get; set; } = new();

    /// <summary>
    /// 错误消息
    /// </summary>
    public List<string> Errors { get; set; } = new();
}

/// <summary>
/// 配额验证结果
/// </summary>
public class QuotaValidationResult
{
    /// <summary>
    /// 是否通过验证
    /// </summary>
    public bool IsValid { get; set; }

    /// <summary>
    /// 当前使用量
    /// </summary>
    public int CurrentUsage { get; set; }

    /// <summary>
    /// 配额限制
    /// </summary>
    public int QuotaLimit { get; set; }

    /// <summary>
    /// 剩余配额
    /// </summary>
    public int RemainingQuota { get; set; }

    /// <summary>
    /// 错误消息
    /// </summary>
    public string? ErrorMessage { get; set; }
}

/// <summary>
/// 模型连接测试结果
/// </summary>
public class ModelConnectionTestResult
{
    /// <summary>
    /// 是否连接成功
    /// </summary>
    public bool IsSuccessful { get; set; }

    /// <summary>
    /// 响应时间（毫秒）
    /// </summary>
    public long ResponseTimeMs { get; set; }

    /// <summary>
    /// 错误消息
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// 模型信息
    /// </summary>
    public string? ModelInfo { get; set; }

    /// <summary>
    /// 测试时间
    /// </summary>
    public DateTime TestedAt { get; set; }
}

/// <summary>
/// 模型能力信息
/// </summary>
public class ModelCapabilities
{
    /// <summary>
    /// 最大上下文长度
    /// </summary>
    public int MaxContextLength { get; set; }

    /// <summary>
    /// 支持的功能
    /// </summary>
    public List<string> SupportedFeatures { get; set; } = new();

    /// <summary>
    /// 支持的语言
    /// </summary>
    public List<string> SupportedLanguages { get; set; } = new();

    /// <summary>
    /// 是否支持流式输出
    /// </summary>
    public bool SupportsStreaming { get; set; }

    /// <summary>
    /// 是否支持函数调用
    /// </summary>
    public bool SupportsFunctionCalling { get; set; }
}

/// <summary>
/// 发布检查项
/// </summary>
public class PublishCheckItem
{
    /// <summary>
    /// 检查项名称
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 是否通过
    /// </summary>
    public bool IsPassed { get; set; }

    /// <summary>
    /// 描述
    /// </summary>
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// 是否必需
    /// </summary>
    public bool IsRequired { get; set; }
}

/// <summary>
/// 智能体操作类型
/// </summary>
public enum AgentOperation
{
    /// <summary>
    /// 查看
    /// </summary>
    View = 1,

    /// <summary>
    /// 编辑
    /// </summary>
    Edit = 2,

    /// <summary>
    /// 删除
    /// </summary>
    Delete = 3,

    /// <summary>
    /// 发布
    /// </summary>
    Publish = 4,

    /// <summary>
    /// 归档
    /// </summary>
    Archive = 5,

    /// <summary>
    /// 使用
    /// </summary>
    Use = 6
}
