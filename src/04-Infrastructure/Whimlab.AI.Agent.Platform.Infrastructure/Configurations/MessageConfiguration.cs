using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Whimlab.AI.Agent.Platform.Domain.Conversation.Entities;
using Whimlab.AI.Agent.Platform.Shared.Common.Enums;
using System.Text.Json;

namespace Whimlab.AI.Agent.Platform.Infrastructure.Configurations;

/// <summary>
/// 消息实体配置
/// </summary>
public class MessageConfiguration : IEntityTypeConfiguration<Message>
{
    /// <summary>
    /// 配置消息实体
    /// </summary>
    /// <param name="builder">实体类型构建器</param>
    public void Configure(EntityTypeBuilder<Message> builder)
    {
        // 表名
        builder.ToTable("Messages", "Conversation");

        // 主键
        builder.HasKey(m => m.Id);

        // 属性配置
        builder.Property(m => m.Id)
            .IsRequired()
            .HasComment("消息ID");

        builder.Property(m => m.ConversationId)
            .IsRequired()
            .HasComment("对话ID");

        builder.Property(m => m.SenderType)
            .IsRequired()
            .HasConversion<string>()
            .HasMaxLength(50)
            .HasComment("发送者类型");

        builder.Property(m => m.Content)
            .IsRequired()
            .HasColumnType("nvarchar(max)")
            .HasComment("消息内容");

        builder.Property(m => m.MessageType)
            .IsRequired()
            .HasConversion<string>()
            .HasMaxLength(50)
            .HasComment("消息类型");

        builder.Property(m => m.TokensUsed)
            .HasDefaultValue(0)
            .HasComment("Token使用量");

        builder.Property(m => m.IsDeleted)
            .IsRequired()
            .HasDefaultValue(false)
            .HasComment("是否已删除");

        builder.Property(m => m.DeletedAt)
            .HasComment("删除时间");

        builder.Property(m => m.CreatedAt)
            .IsRequired()
            .HasDefaultValueSql("GETUTCDATE()")
            .HasComment("创建时间");

        // 索引
        builder.HasIndex(m => m.ConversationId)
            .HasDatabaseName("IX_Messages_ConversationId");

        builder.HasIndex(m => m.SenderType)
            .HasDatabaseName("IX_Messages_SenderType");

        builder.HasIndex(m => m.MessageType)
            .HasDatabaseName("IX_Messages_MessageType");

        builder.HasIndex(m => m.IsDeleted)
            .HasDatabaseName("IX_Messages_IsDeleted");

        builder.HasIndex(m => m.CreatedAt)
            .HasDatabaseName("IX_Messages_CreatedAt");

        // 复合索引
        builder.HasIndex(m => new { m.ConversationId, m.CreatedAt })
            .HasDatabaseName("IX_Messages_ConversationId_CreatedAt");

        builder.HasIndex(m => new { m.ConversationId, m.IsDeleted })
            .HasDatabaseName("IX_Messages_ConversationId_IsDeleted");

        // 查询过滤器 - 默认不查询已删除的消息
        builder.HasQueryFilter(m => !m.IsDeleted);
    }
}
