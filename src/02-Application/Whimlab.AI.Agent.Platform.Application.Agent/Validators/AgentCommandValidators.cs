using FluentValidation;
using Whimlab.AI.Agent.Platform.Application.Agent.Commands;
using Whimlab.AI.Agent.Platform.Application.Agent.DTOs;
using Whimlab.AI.Agent.Platform.Shared.Common.Enums;

namespace Whimlab.AI.Agent.Platform.Application.Agent.Validators;

/// <summary>
/// 创建智能体命令验证器
/// </summary>
public class CreateAgentCommandValidator : AbstractValidator<CreateAgentCommand>
{
    /// <summary>
    /// 初始化验证器
    /// </summary>
    public CreateAgentCommandValidator()
    {
        RuleFor(x => x.Name)
            .NotEmpty().WithMessage("智能体名称不能为空")
            .Length(2, 100).WithMessage("智能体名称长度必须在2-100个字符之间")
            .Matches("^[a-zA-Z0-9\\u4e00-\\u9fa5_\\-\\s]+$").WithMessage("智能体名称只能包含字母、数字、中文、下划线、连字符和空格");

        RuleFor(x => x.Description)
            .NotEmpty().WithMessage("智能体描述不能为空")
            .Length(10, 1000).WithMessage("智能体描述长度必须在10-1000个字符之间");

        RuleFor(x => x.IconUrl)
            .Must(BeValidUrl).WithMessage("图标URL格式不正确")
            .When(x => !string.IsNullOrWhiteSpace(x.IconUrl));

        RuleFor(x => x.AgentType)
            .IsInEnum().WithMessage("智能体类型无效");

        RuleFor(x => x.Category)
            .MaximumLength(50).WithMessage("分类长度不能超过50个字符")
            .When(x => !string.IsNullOrWhiteSpace(x.Category));

        RuleFor(x => x.CreatedBy)
            .NotEmpty().WithMessage("创建者ID不能为空");

        RuleFor(x => x.Configuration)
            .NotNull().WithMessage("配置信息不能为空")
            .SetValidator(new AgentConfigurationDtoValidator());

        RuleFor(x => x.Tags)
            .Must(tags => tags.Count <= 10).WithMessage("标签数量不能超过10个")
            .Must(tags => tags.All(tag => !string.IsNullOrWhiteSpace(tag) && tag.Length <= 20))
            .WithMessage("每个标签长度不能超过20个字符且不能为空");
    }

    /// <summary>
    /// 验证URL格式
    /// </summary>
    private static bool BeValidUrl(string? url)
    {
        if (string.IsNullOrWhiteSpace(url))
            return true;

        return Uri.TryCreate(url, UriKind.Absolute, out var result) &&
               (result.Scheme == Uri.UriSchemeHttp || result.Scheme == Uri.UriSchemeHttps);
    }
}

/// <summary>
/// 更新智能体命令验证器
/// </summary>
public class UpdateAgentCommandValidator : AbstractValidator<UpdateAgentCommand>
{
    /// <summary>
    /// 初始化验证器
    /// </summary>
    public UpdateAgentCommandValidator()
    {
        RuleFor(x => x.Id)
            .NotEmpty().WithMessage("智能体ID不能为空");

        RuleFor(x => x.Name)
            .NotEmpty().WithMessage("智能体名称不能为空")
            .Length(2, 100).WithMessage("智能体名称长度必须在2-100个字符之间")
            .Matches("^[a-zA-Z0-9\\u4e00-\\u9fa5_\\-\\s]+$").WithMessage("智能体名称只能包含字母、数字、中文、下划线、连字符和空格");

        RuleFor(x => x.Description)
            .NotEmpty().WithMessage("智能体描述不能为空")
            .Length(10, 1000).WithMessage("智能体描述长度必须在10-1000个字符之间");

        RuleFor(x => x.IconUrl)
            .Must(BeValidUrl).WithMessage("图标URL格式不正确")
            .When(x => !string.IsNullOrWhiteSpace(x.IconUrl));

        RuleFor(x => x.Category)
            .MaximumLength(50).WithMessage("分类长度不能超过50个字符")
            .When(x => !string.IsNullOrWhiteSpace(x.Category));

        RuleFor(x => x.OperatorId)
            .NotEmpty().WithMessage("操作者ID不能为空");
    }

    /// <summary>
    /// 验证URL格式
    /// </summary>
    private static bool BeValidUrl(string? url)
    {
        if (string.IsNullOrWhiteSpace(url))
            return true;

        return Uri.TryCreate(url, UriKind.Absolute, out var result) &&
               (result.Scheme == Uri.UriSchemeHttp || result.Scheme == Uri.UriSchemeHttps);
    }
}

/// <summary>
/// 更新智能体配置命令验证器
/// </summary>
public class UpdateAgentConfigurationCommandValidator : AbstractValidator<UpdateAgentConfigurationCommand>
{
    /// <summary>
    /// 初始化验证器
    /// </summary>
    public UpdateAgentConfigurationCommandValidator()
    {
        RuleFor(x => x.Id)
            .NotEmpty().WithMessage("智能体ID不能为空");

        RuleFor(x => x.Configuration)
            .NotNull().WithMessage("配置信息不能为空")
            .SetValidator(new AgentConfigurationDtoValidator());

        RuleFor(x => x.VersionNotes)
            .MaximumLength(500).WithMessage("版本说明长度不能超过500个字符")
            .When(x => !string.IsNullOrWhiteSpace(x.VersionNotes));

        RuleFor(x => x.OperatorId)
            .NotEmpty().WithMessage("操作者ID不能为空");
    }
}

/// <summary>
/// 智能体配置DTO验证器
/// </summary>
public class AgentConfigurationDtoValidator : AbstractValidator<AgentConfigurationDto>
{
    /// <summary>
    /// 初始化验证器
    /// </summary>
    public AgentConfigurationDtoValidator()
    {
        RuleFor(x => x.SystemPrompt)
            .NotEmpty().WithMessage("系统提示词不能为空")
            .Length(10, 5000).WithMessage("系统提示词长度必须在10-5000个字符之间");

        RuleFor(x => x.ModelConfig)
            .NotNull().WithMessage("模型配置不能为空")
            .SetValidator(new ModelConfigurationDtoValidator());

        RuleFor(x => x.ToolConfigs)
            .Must(configs => configs.Count <= 20).WithMessage("工具配置数量不能超过20个");

        RuleForEach(x => x.ToolConfigs)
            .SetValidator(new ToolConfigurationDtoValidator());

        RuleFor(x => x.KnowledgeBaseConfig)
            .SetValidator(new KnowledgeBaseConfigDtoValidator()!)
            .When(x => x.KnowledgeBaseConfig != null);
    }
}

/// <summary>
/// 模型配置DTO验证器
/// </summary>
public class ModelConfigurationDtoValidator : AbstractValidator<ModelConfigurationDto>
{
    /// <summary>
    /// 初始化验证器
    /// </summary>
    public ModelConfigurationDtoValidator()
    {
        RuleFor(x => x.Provider)
            .NotEmpty().WithMessage("模型提供商不能为空")
            .MaximumLength(50).WithMessage("模型提供商长度不能超过50个字符");

        RuleFor(x => x.ModelName)
            .NotEmpty().WithMessage("模型名称不能为空")
            .MaximumLength(100).WithMessage("模型名称长度不能超过100个字符");

        RuleFor(x => x.Temperature)
            .InclusiveBetween(0.0, 2.0).WithMessage("温度参数必须在0.0-2.0之间");

        RuleFor(x => x.MaxTokens)
            .InclusiveBetween(1, 32000).WithMessage("最大Token数必须在1-32000之间");

        RuleFor(x => x.TopP)
            .InclusiveBetween(0.0, 1.0).WithMessage("Top P参数必须在0.0-1.0之间");

        RuleFor(x => x.FrequencyPenalty)
            .InclusiveBetween(-2.0, 2.0).WithMessage("频率惩罚必须在-2.0-2.0之间");

        RuleFor(x => x.PresencePenalty)
            .InclusiveBetween(-2.0, 2.0).WithMessage("存在惩罚必须在-2.0-2.0之间");
    }
}

/// <summary>
/// 工具配置DTO验证器
/// </summary>
public class ToolConfigurationDtoValidator : AbstractValidator<ToolConfigurationDto>
{
    /// <summary>
    /// 初始化验证器
    /// </summary>
    public ToolConfigurationDtoValidator()
    {
        RuleFor(x => x.ToolName)
            .NotEmpty().WithMessage("工具名称不能为空")
            .MaximumLength(50).WithMessage("工具名称长度不能超过50个字符");

        RuleFor(x => x.ToolType)
            .NotEmpty().WithMessage("工具类型不能为空")
            .MaximumLength(50).WithMessage("工具类型长度不能超过50个字符");

        RuleFor(x => x.Parameters)
            .Must(BeValidJson).WithMessage("配置参数必须是有效的JSON格式")
            .When(x => !string.IsNullOrWhiteSpace(x.Parameters));
    }

    /// <summary>
    /// 验证JSON格式
    /// </summary>
    private static bool BeValidJson(string? json)
    {
        if (string.IsNullOrWhiteSpace(json))
            return true;

        try
        {
            System.Text.Json.JsonDocument.Parse(json);
            return true;
        }
        catch
        {
            return false;
        }
    }
}

/// <summary>
/// 知识库配置DTO验证器
/// </summary>
public class KnowledgeBaseConfigDtoValidator : AbstractValidator<KnowledgeBaseConfigDto>
{
    /// <summary>
    /// 初始化验证器
    /// </summary>
    public KnowledgeBaseConfigDtoValidator()
    {
        RuleFor(x => x.KnowledgeBaseId)
            .MaximumLength(100).WithMessage("知识库ID长度不能超过100个字符")
            .When(x => !string.IsNullOrWhiteSpace(x.KnowledgeBaseId));

        RuleFor(x => x.RetrievalConfig)
            .Must(BeValidJson).WithMessage("检索配置必须是有效的JSON格式")
            .When(x => !string.IsNullOrWhiteSpace(x.RetrievalConfig));
    }

    /// <summary>
    /// 验证JSON格式
    /// </summary>
    private static bool BeValidJson(string? json)
    {
        if (string.IsNullOrWhiteSpace(json))
            return true;

        try
        {
            System.Text.Json.JsonDocument.Parse(json);
            return true;
        }
        catch
        {
            return false;
        }
    }
}
