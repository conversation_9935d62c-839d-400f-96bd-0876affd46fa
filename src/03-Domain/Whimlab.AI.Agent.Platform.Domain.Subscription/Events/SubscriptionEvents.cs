using MediatR;
using Whimlab.AI.Agent.Platform.Domain.Subscription.ValueObjects;
using Whimlab.AI.Agent.Platform.Shared.Common.Enums;

namespace Whimlab.AI.Agent.Platform.Domain.Subscription.Events;

/// <summary>
/// 订阅计划创建事件
/// </summary>
/// <param name="PlanId">计划ID</param>
/// <param name="Name">计划名称</param>
/// <param name="PlanCode">计划代码</param>
public record SubscriptionPlanCreatedEvent(Guid PlanId, string Name, string PlanCode) : INotification;

/// <summary>
/// 订阅计划更新事件
/// </summary>
/// <param name="PlanId">计划ID</param>
/// <param name="Name">计划名称</param>
public record SubscriptionPlanUpdatedEvent(Guid PlanId, string Name) : INotification;

/// <summary>
/// 订阅计划价格更新事件
/// </summary>
/// <param name="PlanId">计划ID</param>
/// <param name="MonthlyPrice">月度价格</param>
/// <param name="YearlyPrice">年度价格</param>
public record SubscriptionPlanPriceUpdatedEvent(Guid PlanId, Money MonthlyPrice, Money YearlyPrice) : INotification;

/// <summary>
/// 订阅计划激活事件
/// </summary>
/// <param name="PlanId">计划ID</param>
/// <param name="Name">计划名称</param>
public record SubscriptionPlanActivatedEvent(Guid PlanId, string Name) : INotification;

/// <summary>
/// 订阅计划停用事件
/// </summary>
/// <param name="PlanId">计划ID</param>
/// <param name="Name">计划名称</param>
public record SubscriptionPlanDeactivatedEvent(Guid PlanId, string Name) : INotification;

/// <summary>
/// 客户订阅创建事件
/// </summary>
/// <param name="SubscriptionId">订阅ID</param>
/// <param name="CustomerId">客户ID</param>
/// <param name="PlanId">计划ID</param>
/// <param name="BillingCycle">计费周期</param>
public record CustomerSubscriptionCreatedEvent(Guid SubscriptionId, Guid CustomerId, Guid PlanId, BillingCycle BillingCycle) : INotification;

/// <summary>
/// 客户订阅激活事件
/// </summary>
/// <param name="SubscriptionId">订阅ID</param>
/// <param name="CustomerId">客户ID</param>
public record CustomerSubscriptionActivatedEvent(Guid SubscriptionId, Guid CustomerId) : INotification;

/// <summary>
/// 客户订阅取消事件
/// </summary>
/// <param name="SubscriptionId">订阅ID</param>
/// <param name="CustomerId">客户ID</param>
/// <param name="CancelReason">取消原因</param>
public record CustomerSubscriptionCancelledEvent(Guid SubscriptionId, Guid CustomerId, string? CancelReason) : INotification;

/// <summary>
/// 客户订阅过期事件
/// </summary>
/// <param name="SubscriptionId">订阅ID</param>
/// <param name="CustomerId">客户ID</param>
public record CustomerSubscriptionExpiredEvent(Guid SubscriptionId, Guid CustomerId) : INotification;

/// <summary>
/// 客户订阅续费事件
/// </summary>
/// <param name="SubscriptionId">订阅ID</param>
/// <param name="CustomerId">客户ID</param>
/// <param name="NewEndDate">新的结束日期</param>
public record CustomerSubscriptionRenewedEvent(Guid SubscriptionId, Guid CustomerId, DateTime NewEndDate) : INotification;

/// <summary>
/// 配额使用事件
/// </summary>
/// <param name="CustomerId">客户ID</param>
/// <param name="QuotaType">配额类型</param>
/// <param name="UsedAmount">使用量</param>
/// <param name="RemainingAmount">剩余量</param>
public record QuotaUsedEvent(Guid CustomerId, string QuotaType, long UsedAmount, long RemainingAmount) : INotification;

/// <summary>
/// 配额超限事件
/// </summary>
/// <param name="CustomerId">客户ID</param>
/// <param name="QuotaType">配额类型</param>
/// <param name="Limit">配额限制</param>
/// <param name="UsedAmount">使用量</param>
public record QuotaExceededEvent(Guid CustomerId, string QuotaType, long Limit, long UsedAmount) : INotification;

/// <summary>
/// 配额重置事件
/// </summary>
/// <param name="CustomerId">客户ID</param>
/// <param name="QuotaType">配额类型</param>
/// <param name="Period">重置周期</param>
public record QuotaResetEvent(Guid CustomerId, string QuotaType, string Period) : INotification;
