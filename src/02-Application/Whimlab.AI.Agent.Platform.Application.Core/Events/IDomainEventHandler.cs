using MediatR;

namespace Whimlab.AI.Agent.Platform.Application.Core.Events;

/// <summary>
/// 领域事件接口
/// </summary>
public interface IDomainEvent : INotification
{
    /// <summary>
    /// 事件发生时间
    /// </summary>
    DateTime OccurredOn { get; }

    /// <summary>
    /// 事件ID
    /// </summary>
    Guid EventId { get; }
}

/// <summary>
/// 领域事件基类
/// </summary>
public abstract class DomainEvent : IDomainEvent
{
    /// <summary>
    /// 事件发生时间
    /// </summary>
    public DateTime OccurredOn { get; }

    /// <summary>
    /// 事件ID
    /// </summary>
    public Guid EventId { get; }

    /// <summary>
    /// 初始化领域事件
    /// </summary>
    protected DomainEvent()
    {
        EventId = Guid.NewGuid();
        OccurredOn = DateTime.UtcNow;
    }
}

/// <summary>
/// 领域事件处理器接口
/// </summary>
/// <typeparam name="TDomainEvent">领域事件类型</typeparam>
public interface IDomainEventHandler<in TDomainEvent> : INotificationHandler<TDomainEvent>
    where TDomainEvent : IDomainEvent
{
}
