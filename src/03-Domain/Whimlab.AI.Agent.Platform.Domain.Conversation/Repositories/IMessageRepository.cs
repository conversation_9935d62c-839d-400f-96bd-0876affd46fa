using Whimlab.AI.Agent.Platform.Domain.Conversation.Entities;
using Whimlab.AI.Agent.Platform.Domain.Core.Common;
using Whimlab.AI.Agent.Platform.Shared.Common.Enums;

namespace Whimlab.AI.Agent.Platform.Domain.Conversation.Repositories;

/// <summary>
/// 消息仓储接口
/// </summary>
public interface IMessageRepository
{
    /// <summary>
    /// 根据ID获取消息
    /// </summary>
    /// <param name="id">消息ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>消息实体</returns>
    Task<Message?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default);

    /// <summary>
    /// 根据ID获取消息（包含附件）
    /// </summary>
    /// <param name="id">消息ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>消息实体</returns>
    Task<Message?> GetByIdWithAttachmentsAsync(Guid id, CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取对话的消息列表
    /// </summary>
    /// <param name="conversationId">对话ID</param>
    /// <param name="pageNumber">页码</param>
    /// <param name="pageSize">页大小</param>
    /// <param name="senderType">发送者类型过滤</param>
    /// <param name="includeDeleted">是否包含已删除的消息</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>分页的消息列表</returns>
    Task<PagedResult<Message>> GetConversationMessagesAsync(
        Guid conversationId,
        int pageNumber = 1,
        int pageSize = 50,
        SenderType? senderType = null,
        bool includeDeleted = false,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取对话的最近消息
    /// </summary>
    /// <param name="conversationId">对话ID</param>
    /// <param name="count">消息数量</param>
    /// <param name="includeDeleted">是否包含已删除的消息</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>最近的消息列表</returns>
    Task<IEnumerable<Message>> GetRecentMessagesAsync(
        Guid conversationId,
        int count = 10,
        bool includeDeleted = false,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 搜索消息
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="searchTerm">搜索关键词</param>
    /// <param name="conversationId">对话ID过滤（可选）</param>
    /// <param name="senderType">发送者类型过滤（可选）</param>
    /// <param name="startDate">开始日期过滤（可选）</param>
    /// <param name="endDate">结束日期过滤（可选）</param>
    /// <param name="pageNumber">页码</param>
    /// <param name="pageSize">页大小</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>分页的搜索结果</returns>
    Task<PagedResult<Message>> SearchMessagesAsync(
        Guid userId,
        string searchTerm,
        Guid? conversationId = null,
        SenderType? senderType = null,
        DateTime? startDate = null,
        DateTime? endDate = null,
        int pageNumber = 1,
        int pageSize = 20,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取用户的消息统计
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="startDate">开始日期</param>
    /// <param name="endDate">结束日期</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>消息统计</returns>
    Task<MessageStatistics> GetUserMessageStatisticsAsync(
        Guid userId,
        DateTime startDate,
        DateTime endDate,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取智能体的消息统计
    /// </summary>
    /// <param name="agentId">智能体ID</param>
    /// <param name="startDate">开始日期</param>
    /// <param name="endDate">结束日期</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>消息统计</returns>
    Task<MessageStatistics> GetAgentMessageStatisticsAsync(
        Guid agentId,
        DateTime startDate,
        DateTime endDate,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取包含附件的消息
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="fileType">文件类型过滤（可选）</param>
    /// <param name="pageNumber">页码</param>
    /// <param name="pageSize">页大小</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>分页的消息列表</returns>
    Task<PagedResult<Message>> GetMessagesWithAttachmentsAsync(
        Guid userId,
        string? fileType = null,
        int pageNumber = 1,
        int pageSize = 20,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 批量删除过期的已删除消息
    /// </summary>
    /// <param name="deletedBefore">删除时间阈值</param>
    /// <param name="batchSize">批次大小</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>删除的消息数量</returns>
    Task<int> PurgeDeletedMessagesAsync(
        DateTime deletedBefore,
        int batchSize = 1000,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 添加消息
    /// </summary>
    /// <param name="message">消息实体</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>任务</returns>
    Task AddAsync(Message message, CancellationToken cancellationToken = default);

    /// <summary>
    /// 批量添加消息
    /// </summary>
    /// <param name="messages">消息实体列表</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>任务</returns>
    Task AddRangeAsync(IEnumerable<Message> messages, CancellationToken cancellationToken = default);

    /// <summary>
    /// 更新消息
    /// </summary>
    /// <param name="message">消息实体</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>任务</returns>
    Task UpdateAsync(Message message, CancellationToken cancellationToken = default);

    /// <summary>
    /// 删除消息
    /// </summary>
    /// <param name="message">消息实体</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>任务</returns>
    Task DeleteAsync(Message message, CancellationToken cancellationToken = default);

    /// <summary>
    /// 保存更改
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>受影响的行数</returns>
    Task<int> SaveChangesAsync(CancellationToken cancellationToken = default);
}

/// <summary>
/// 消息统计信息
/// </summary>
public class MessageStatistics
{
    /// <summary>
    /// 总消息数
    /// </summary>
    public int TotalMessages { get; set; }

    /// <summary>
    /// 用户消息数
    /// </summary>
    public int UserMessages { get; set; }

    /// <summary>
    /// 智能体消息数
    /// </summary>
    public int AgentMessages { get; set; }

    /// <summary>
    /// 系统消息数
    /// </summary>
    public int SystemMessages { get; set; }

    /// <summary>
    /// 包含附件的消息数
    /// </summary>
    public int MessagesWithAttachments { get; set; }

    /// <summary>
    /// 已删除的消息数
    /// </summary>
    public int DeletedMessages { get; set; }

    /// <summary>
    /// 总字符数
    /// </summary>
    public long TotalCharacters { get; set; }

    /// <summary>
    /// 总Token数
    /// </summary>
    public long TotalTokens { get; set; }

    /// <summary>
    /// 平均消息长度
    /// </summary>
    public double AverageMessageLength { get; set; }

    /// <summary>
    /// 按日期分组的消息数
    /// </summary>
    public Dictionary<DateTime, int> MessagesByDate { get; set; } = new();

    /// <summary>
    /// 按发送者类型分组的消息数
    /// </summary>
    public Dictionary<SenderType, int> MessagesBySenderType { get; set; } = new();
}
