using Whimlab.AI.Agent.Platform.Domain.Core.Common;

namespace Whimlab.AI.Agent.Platform.Domain.Subscription.ValueObjects;

/// <summary>
/// 货币值对象
/// </summary>
public sealed class Money : ValueObject
{
    /// <summary>
    /// 金额
    /// </summary>
    public decimal Amount { get; }

    /// <summary>
    /// 货币代码
    /// </summary>
    public string Currency { get; }

    /// <summary>
    /// 初始化货币
    /// </summary>
    /// <param name="amount">金额</param>
    /// <param name="currency">货币代码</param>
    private Money(decimal amount, string currency)
    {
        Amount = amount;
        Currency = currency;
    }

    /// <summary>
    /// 创建货币
    /// </summary>
    /// <param name="amount">金额</param>
    /// <param name="currency">货币代码</param>
    /// <returns>货币值对象</returns>
    public static Money Create(decimal amount, string currency = "CNY")
    {
        if (amount < 0)
            throw new ArgumentException("金额不能为负数", nameof(amount));

        if (string.IsNullOrWhiteSpace(currency))
            throw new ArgumentException("货币代码不能为空", nameof(currency));

        var normalizedCurrency = currency.Trim().ToUpperInvariant();
        
        if (!IsValidCurrency(normalizedCurrency))
            throw new ArgumentException($"不支持的货币代码: {currency}", nameof(currency));

        return new Money(amount, normalizedCurrency);
    }

    /// <summary>
    /// 创建人民币
    /// </summary>
    /// <param name="amount">金额</param>
    /// <returns>人民币货币对象</returns>
    public static Money CNY(decimal amount) => Create(amount, "CNY");

    /// <summary>
    /// 创建美元
    /// </summary>
    /// <param name="amount">金额</param>
    /// <returns>美元货币对象</returns>
    public static Money USD(decimal amount) => Create(amount, "USD");

    /// <summary>
    /// 创建欧元
    /// </summary>
    /// <param name="amount">金额</param>
    /// <returns>欧元货币对象</returns>
    public static Money EUR(decimal amount) => Create(amount, "EUR");

    /// <summary>
    /// 零值
    /// </summary>
    /// <param name="currency">货币代码</param>
    /// <returns>零值货币对象</returns>
    public static Money Zero(string currency = "CNY") => Create(0, currency);

    /// <summary>
    /// 加法运算
    /// </summary>
    /// <param name="other">另一个货币对象</param>
    /// <returns>相加后的货币对象</returns>
    public Money Add(Money other)
    {
        if (Currency != other.Currency)
            throw new InvalidOperationException($"不能对不同货币进行运算: {Currency} 和 {other.Currency}");

        return Create(Amount + other.Amount, Currency);
    }

    /// <summary>
    /// 减法运算
    /// </summary>
    /// <param name="other">另一个货币对象</param>
    /// <returns>相减后的货币对象</returns>
    public Money Subtract(Money other)
    {
        if (Currency != other.Currency)
            throw new InvalidOperationException($"不能对不同货币进行运算: {Currency} 和 {other.Currency}");

        var result = Amount - other.Amount;
        if (result < 0)
            throw new InvalidOperationException("运算结果不能为负数");

        return Create(result, Currency);
    }

    /// <summary>
    /// 乘法运算
    /// </summary>
    /// <param name="multiplier">乘数</param>
    /// <returns>相乘后的货币对象</returns>
    public Money Multiply(decimal multiplier)
    {
        if (multiplier < 0)
            throw new ArgumentException("乘数不能为负数", nameof(multiplier));

        return Create(Amount * multiplier, Currency);
    }

    /// <summary>
    /// 除法运算
    /// </summary>
    /// <param name="divisor">除数</param>
    /// <returns>相除后的货币对象</returns>
    public Money Divide(decimal divisor)
    {
        if (divisor <= 0)
            throw new ArgumentException("除数必须大于0", nameof(divisor));

        return Create(Amount / divisor, Currency);
    }

    /// <summary>
    /// 比较大小
    /// </summary>
    /// <param name="other">另一个货币对象</param>
    /// <returns>比较结果</returns>
    public int CompareTo(Money other)
    {
        if (Currency != other.Currency)
            throw new InvalidOperationException($"不能比较不同货币: {Currency} 和 {other.Currency}");

        return Amount.CompareTo(other.Amount);
    }

    /// <summary>
    /// 检查是否为零
    /// </summary>
    /// <returns>是否为零</returns>
    public bool IsZero() => Amount == 0;

    /// <summary>
    /// 检查是否为正数
    /// </summary>
    /// <returns>是否为正数</returns>
    public bool IsPositive() => Amount > 0;

    /// <summary>
    /// 格式化显示
    /// </summary>
    /// <returns>格式化后的字符串</returns>
    public string ToDisplayString()
    {
        return Currency switch
        {
            "CNY" => $"¥{Amount:F2}",
            "USD" => $"${Amount:F2}",
            "EUR" => $"€{Amount:F2}",
            _ => $"{Amount:F2} {Currency}"
        };
    }

    /// <summary>
    /// 转换为分（整数）
    /// </summary>
    /// <returns>分为单位的整数</returns>
    public long ToCents()
    {
        return (long)(Amount * 100);
    }

    /// <summary>
    /// 从分创建货币对象
    /// </summary>
    /// <param name="cents">分为单位的金额</param>
    /// <param name="currency">货币代码</param>
    /// <returns>货币对象</returns>
    public static Money FromCents(long cents, string currency = "CNY")
    {
        return Create(cents / 100m, currency);
    }

    /// <summary>
    /// 验证货币代码
    /// </summary>
    /// <param name="currency">货币代码</param>
    /// <returns>是否有效</returns>
    private static bool IsValidCurrency(string currency)
    {
        var supportedCurrencies = new[] { "CNY", "USD", "EUR", "GBP", "JPY", "HKD" };
        return supportedCurrencies.Contains(currency);
    }

    /// <summary>
    /// 获取相等性组件
    /// </summary>
    /// <returns>相等性组件集合</returns>
    protected override IEnumerable<object?> GetEqualityComponents()
    {
        yield return Amount;
        yield return Currency;
    }

    /// <summary>
    /// 转换为字符串
    /// </summary>
    /// <returns>字符串表示</returns>
    public override string ToString() => ToDisplayString();

    // 运算符重载
    public static Money operator +(Money left, Money right) => left.Add(right);
    public static Money operator -(Money left, Money right) => left.Subtract(right);
    public static Money operator *(Money left, decimal right) => left.Multiply(right);
    public static Money operator /(Money left, decimal right) => left.Divide(right);
    public static bool operator >(Money left, Money right) => left.CompareTo(right) > 0;
    public static bool operator <(Money left, Money right) => left.CompareTo(right) < 0;
    public static bool operator >=(Money left, Money right) => left.CompareTo(right) >= 0;
    public static bool operator <=(Money left, Money right) => left.CompareTo(right) <= 0;
}
