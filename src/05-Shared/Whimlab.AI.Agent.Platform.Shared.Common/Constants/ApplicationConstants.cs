namespace Whimlab.AI.Agent.Platform.Shared.Common.Constants;

/// <summary>
/// 应用程序常量定义
/// </summary>
public static class ApplicationConstants
{
    /// <summary>
    /// 应用程序名称
    /// </summary>
    public const string ApplicationName = "Whimlab AI Agent Platform";

    /// <summary>
    /// 应用程序版本
    /// </summary>
    public const string ApplicationVersion = "1.0.0";

    /// <summary>
    /// 默认文化信息
    /// </summary>
    public const string DefaultCulture = "zh-CN";

    /// <summary>
    /// 支持的文化信息
    /// </summary>
    public static readonly string[] SupportedCultures = { "zh-CN", "en-US" };

    /// <summary>
    /// 默认时区
    /// </summary>
    public const string DefaultTimeZone = "Asia/Shanghai";

    /// <summary>
    /// 默认分页大小
    /// </summary>
    public const int DefaultPageSize = 20;

    /// <summary>
    /// 最大分页大小
    /// </summary>
    public const int MaxPageSize = 100;

    /// <summary>
    /// 默认缓存过期时间（分钟）
    /// </summary>
    public const int DefaultCacheExpirationMinutes = 30;

    /// <summary>
    /// JWT Token 默认过期时间（分钟）
    /// </summary>
    public const int DefaultJwtExpirationMinutes = 60;

    /// <summary>
    /// Refresh Token 默认过期时间（天）
    /// </summary>
    public const int DefaultRefreshTokenExpirationDays = 7;

    /// <summary>
    /// 试用期默认天数
    /// </summary>
    public const int DefaultTrialDays = 3;

    /// <summary>
    /// 试用期默认Token配额
    /// </summary>
    public const long DefaultTrialTokenQuota = 10000;

    /// <summary>
    /// 最大文件上传大小（字节）
    /// </summary>
    public const long MaxFileUploadSize = 10 * 1024 * 1024; // 10MB

    /// <summary>
    /// 支持的图片文件扩展名
    /// </summary>
    public static readonly string[] SupportedImageExtensions = { ".jpg", ".jpeg", ".png", ".gif", ".webp" };

    /// <summary>
    /// 支持的文档文件扩展名
    /// </summary>
    public static readonly string[] SupportedDocumentExtensions = { ".pdf", ".doc", ".docx", ".txt", ".md" };
}

/// <summary>
/// 缓存键常量
/// </summary>
public static class CacheKeys
{
    /// <summary>
    /// 用户权限缓存键前缀
    /// </summary>
    public const string UserPermissions = "user_permissions";

    /// <summary>
    /// 智能体配置缓存键前缀
    /// </summary>
    public const string AgentConfiguration = "agent_config";

    /// <summary>
    /// 订阅计划缓存键前缀
    /// </summary>
    public const string SubscriptionPlans = "subscription_plans";

    /// <summary>
    /// 配额使用缓存键前缀
    /// </summary>
    public const string QuotaUsage = "quota_usage";

    /// <summary>
    /// 对话上下文缓存键前缀
    /// </summary>
    public const string ConversationContext = "conversation_context";

    /// <summary>
    /// 生成用户权限缓存键
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="userType">用户类型</param>
    /// <returns>缓存键</returns>
    public static string GetUserPermissionsKey(string userId, string userType) =>
        $"{UserPermissions}:{userType}:{userId}";

    /// <summary>
    /// 生成智能体配置缓存键
    /// </summary>
    /// <param name="agentId">智能体ID</param>
    /// <returns>缓存键</returns>
    public static string GetAgentConfigurationKey(string agentId) =>
        $"{AgentConfiguration}:{agentId}";

    /// <summary>
    /// 生成配额使用缓存键
    /// </summary>
    /// <param name="customerId">客户ID</param>
    /// <param name="cycleIdentifier">周期标识符</param>
    /// <returns>缓存键</returns>
    public static string GetQuotaUsageKey(string customerId, string cycleIdentifier) =>
        $"{QuotaUsage}:{customerId}:{cycleIdentifier}";

    /// <summary>
    /// 生成对话上下文缓存键
    /// </summary>
    /// <param name="conversationId">对话ID</param>
    /// <returns>缓存键</returns>
    public static string GetConversationContextKey(string conversationId) =>
        $"{ConversationContext}:{conversationId}";
}

/// <summary>
/// 权限常量
/// </summary>
public static class Permissions
{
    /// <summary>
    /// 用户管理权限
    /// </summary>
    public static class Users
    {
        public const string ViewCustomers = "Users.ViewCustomers";
        public const string ManageCustomers = "Users.ManageCustomers";
        public const string ViewAdmins = "Users.ViewAdmins";
        public const string ManageAdmins = "Users.ManageAdmins";
        public const string AssignRoles = "Users.AssignRoles";
    }

    /// <summary>
    /// 智能体管理权限
    /// </summary>
    public static class Agents
    {
        public const string View = "Agents.View";
        public const string Create = "Agents.Create";
        public const string Edit = "Agents.Edit";
        public const string Delete = "Agents.Delete";
        public const string Publish = "Agents.Publish";
        public const string Archive = "Agents.Archive";
        public const string ManageVersions = "Agents.ManageVersions";
    }

    /// <summary>
    /// 订阅管理权限
    /// </summary>
    public static class Subscriptions
    {
        public const string ViewPlans = "Subscriptions.ViewPlans";
        public const string ManagePlans = "Subscriptions.ManagePlans";
        public const string ViewCustomerSubscriptions = "Subscriptions.ViewCustomerSubscriptions";
        public const string ManageCustomerSubscriptions = "Subscriptions.ManageCustomerSubscriptions";
    }

    /// <summary>
    /// 支付管理权限
    /// </summary>
    public static class Payments
    {
        public const string ViewPayments = "Payments.ViewPayments";
        public const string ProcessRefunds = "Payments.ProcessRefunds";
        public const string ViewTransactions = "Payments.ViewTransactions";
    }

    /// <summary>
    /// 分析报告权限
    /// </summary>
    public static class Analytics
    {
        public const string ViewDashboard = "Analytics.ViewDashboard";
        public const string ViewReports = "Analytics.ViewReports";
        public const string ExportData = "Analytics.ExportData";
    }

    /// <summary>
    /// 系统管理权限
    /// </summary>
    public static class System
    {
        public const string ViewLogs = "System.ViewLogs";
        public const string ManageConfiguration = "System.ManageConfiguration";
        public const string ViewHealthChecks = "System.ViewHealthChecks";
        public const string ManageBackups = "System.ManageBackups";
    }
}

/// <summary>
/// 角色常量
/// </summary>
public static class Roles
{
    /// <summary>
    /// 超级管理员
    /// </summary>
    public const string SuperAdmin = "SuperAdmin";

    /// <summary>
    /// 管理员
    /// </summary>
    public const string Admin = "Admin";

    /// <summary>
    /// 只读管理员
    /// </summary>
    public const string ReadOnlyAdmin = "ReadOnlyAdmin";

    /// <summary>
    /// 客户
    /// </summary>
    public const string Customer = "Customer";

    /// <summary>
    /// 所有角色
    /// </summary>
    public static readonly string[] AllRoles = { SuperAdmin, Admin, ReadOnlyAdmin, Customer };

    /// <summary>
    /// 管理员角色
    /// </summary>
    public static readonly string[] AdminRoles = { SuperAdmin, Admin, ReadOnlyAdmin };

    /// <summary>
    /// 可写管理员角色
    /// </summary>
    public static readonly string[] WriteAdminRoles = { SuperAdmin, Admin };
}
