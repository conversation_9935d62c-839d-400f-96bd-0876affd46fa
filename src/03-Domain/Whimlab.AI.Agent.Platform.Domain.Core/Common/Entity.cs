using MediatR;

namespace Whimlab.AI.Agent.Platform.Domain.Core.Common;

/// <summary>
/// 实体基类
/// </summary>
/// <typeparam name="TId">主键类型</typeparam>
public abstract class Entity<TId> : IEquatable<Entity<TId>>
    where TId : notnull
{
    private readonly List<INotification> _domainEvents = new();

    /// <summary>
    /// 实体标识符
    /// </summary>
    public TId Id { get; protected set; } = default!;

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedAt { get; protected set; } = DateTime.UtcNow;

    /// <summary>
    /// 最后修改时间
    /// </summary>
    public DateTime LastModifiedAt { get; protected set; } = DateTime.UtcNow;

    /// <summary>
    /// 领域事件集合（只读）
    /// </summary>
    public IReadOnlyList<INotification> DomainEvents => _domainEvents.AsReadOnly();

    /// <summary>
    /// 添加领域事件
    /// </summary>
    /// <param name="domainEvent">领域事件</param>
    protected void AddDomainEvent(INotification domainEvent)
    {
        _domainEvents.Add(domainEvent);
    }

    /// <summary>
    /// 移除领域事件
    /// </summary>
    /// <param name="domainEvent">领域事件</param>
    protected void RemoveDomainEvent(INotification domainEvent)
    {
        _domainEvents.Remove(domainEvent);
    }

    /// <summary>
    /// 清除所有领域事件
    /// </summary>
    public void ClearDomainEvents()
    {
        _domainEvents.Clear();
    }

    /// <summary>
    /// 标记为已修改
    /// </summary>
    protected void MarkAsModified()
    {
        LastModifiedAt = DateTime.UtcNow;
    }

    /// <summary>
    /// 判断是否相等
    /// </summary>
    /// <param name="other">其他实体</param>
    /// <returns>是否相等</returns>
    public bool Equals(Entity<TId>? other)
    {
        if (other is null) return false;
        if (ReferenceEquals(this, other)) return true;
        return EqualityComparer<TId>.Default.Equals(Id, other.Id);
    }

    /// <summary>
    /// 判断是否相等
    /// </summary>
    /// <param name="obj">其他对象</param>
    /// <returns>是否相等</returns>
    public override bool Equals(object? obj)
    {
        return obj is Entity<TId> entity && Equals(entity);
    }

    /// <summary>
    /// 获取哈希码
    /// </summary>
    /// <returns>哈希码</returns>
    public override int GetHashCode()
    {
        return EqualityComparer<TId>.Default.GetHashCode(Id);
    }

    /// <summary>
    /// 相等运算符
    /// </summary>
    /// <param name="left">左操作数</param>
    /// <param name="right">右操作数</param>
    /// <returns>是否相等</returns>
    public static bool operator ==(Entity<TId>? left, Entity<TId>? right)
    {
        return Equals(left, right);
    }

    /// <summary>
    /// 不等运算符
    /// </summary>
    /// <param name="left">左操作数</param>
    /// <param name="right">右操作数</param>
    /// <returns>是否不等</returns>
    public static bool operator !=(Entity<TId>? left, Entity<TId>? right)
    {
        return !Equals(left, right);
    }
}

/// <summary>
/// 实体基类（Guid主键）
/// </summary>
public abstract class Entity : Entity<Guid>
{
    /// <summary>
    /// 初始化实体
    /// </summary>
    protected Entity()
    {
        Id = Guid.NewGuid();
    }

    /// <summary>
    /// 初始化实体
    /// </summary>
    /// <param name="id">实体标识符</param>
    protected Entity(Guid id)
    {
        Id = id;
    }
}
