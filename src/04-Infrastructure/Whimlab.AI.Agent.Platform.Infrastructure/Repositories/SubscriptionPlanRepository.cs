using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Whimlab.AI.Agent.Platform.Domain.Core.Common;
using Whimlab.AI.Agent.Platform.Domain.Subscription.Entities;
using Whimlab.AI.Agent.Platform.Domain.Subscription.Repositories;
using Whimlab.AI.Agent.Platform.Infrastructure.Data;
using Whimlab.AI.Agent.Platform.Infrastructure.Models.Statistics;
using Whimlab.AI.Agent.Platform.Shared.Common.Enums;

namespace Whimlab.AI.Agent.Platform.Infrastructure.Repositories;

/// <summary>
/// 订阅计划仓储实现
/// </summary>
public class SubscriptionPlanRepository : BaseRepository<SubscriptionPlan, Guid>, ISubscriptionPlanRepository
{
    /// <summary>
    /// 初始化订阅计划仓储
    /// </summary>
    /// <param name="context">数据库上下文</param>
    /// <param name="logger">日志记录器</param>
    public SubscriptionPlanRepository(
        AgentPlatformDbContext context,
        ILogger<SubscriptionPlanRepository> logger) : base(context, logger)
    {
    }

    /// <summary>
    /// 根据名称获取订阅计划
    /// </summary>
    /// <param name="name">计划名称</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>订阅计划</returns>
    public async Task<SubscriptionPlan?> GetByNameAsync(string name, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("根据名称获取订阅计划: {Name}", name);

            return await _dbSet
                .FirstOrDefaultAsync(sp => sp.Name == name, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "根据名称获取订阅计划时发生错误: {Name}", name);
            throw;
        }
    }

    /// <summary>
    /// 根据代码获取订阅计划
    /// </summary>
    /// <param name="code">计划代码</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>订阅计划</returns>
    public async Task<SubscriptionPlan?> GetByCodeAsync(string code, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("根据代码获取订阅计划: {Code}", code);

            return await _dbSet
                .FirstOrDefaultAsync(sp => sp.Code == code, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "根据代码获取订阅计划时发生错误: {Code}", code);
            throw;
        }
    }

    /// <summary>
    /// 获取可用的订阅计划列表
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>可用的订阅计划列表</returns>
    public async Task<IEnumerable<SubscriptionPlan>> GetAvailablePlansAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("获取可用的订阅计划列表");

            return await _dbSet
                .Where(sp => sp.IsActive && sp.IsPublic)
                .OrderBy(sp => sp.SortOrder)
                .ThenBy(sp => sp.Price)
                .ToListAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取可用的订阅计划列表时发生错误");
            throw;
        }
    }

    /// <summary>
    /// 根据计划类型获取订阅计划列表
    /// </summary>
    /// <param name="planType">计划类型</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>订阅计划列表</returns>
    public async Task<IEnumerable<SubscriptionPlan>> GetByPlanTypeAsync(PlanType planType, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("根据计划类型获取订阅计划列表: {PlanType}", planType);

            return await _dbSet
                .Where(sp => sp.PlanType == planType && sp.IsActive)
                .OrderBy(sp => sp.SortOrder)
                .ThenBy(sp => sp.Price)
                .ToListAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "根据计划类型获取订阅计划列表时发生错误: {PlanType}", planType);
            throw;
        }
    }

    /// <summary>
    /// 根据价格范围获取订阅计划列表
    /// </summary>
    /// <param name="minPrice">最低价格</param>
    /// <param name="maxPrice">最高价格</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>订阅计划列表</returns>
    public async Task<IEnumerable<SubscriptionPlan>> GetByPriceRangeAsync(
        decimal? minPrice = null,
        decimal? maxPrice = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("根据价格范围获取订阅计划列表: {MinPrice} - {MaxPrice}", minPrice, maxPrice);

            var query = _dbSet.Where(sp => sp.IsActive);

            if (minPrice.HasValue)
            {
                query = query.Where(sp => sp.Price >= minPrice.Value);
            }

            if (maxPrice.HasValue)
            {
                query = query.Where(sp => sp.Price <= maxPrice.Value);
            }

            return await query
                .OrderBy(sp => sp.Price)
                .ToListAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "根据价格范围获取订阅计划列表时发生错误: {MinPrice} - {MaxPrice}", minPrice, maxPrice);
            throw;
        }
    }

    /// <summary>
    /// 检查计划名称是否存在
    /// </summary>
    /// <param name="name">计划名称</param>
    /// <param name="excludePlanId">排除的计划ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>是否存在</returns>
    public async Task<bool> IsNameExistsAsync(string name, Guid? excludePlanId = null, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("检查计划名称是否存在: {Name}, 排除计划ID: {ExcludePlanId}", name, excludePlanId);

            var query = _dbSet.Where(sp => sp.Name == name);

            if (excludePlanId.HasValue)
            {
                query = query.Where(sp => sp.Id != excludePlanId.Value);
            }

            return await query.AnyAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "检查计划名称是否存在时发生错误: {Name}", name);
            throw;
        }
    }

    /// <summary>
    /// 检查计划代码是否存在
    /// </summary>
    /// <param name="code">计划代码</param>
    /// <param name="excludePlanId">排除的计划ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>是否存在</returns>
    public async Task<bool> IsCodeExistsAsync(string code, Guid? excludePlanId = null, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("检查计划代码是否存在: {Code}, 排除计划ID: {ExcludePlanId}", code, excludePlanId);

            var query = _dbSet.Where(sp => sp.Code == code);

            if (excludePlanId.HasValue)
            {
                query = query.Where(sp => sp.Id != excludePlanId.Value);
            }

            return await query.AnyAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "检查计划代码是否存在时发生错误: {Code}", code);
            throw;
        }
    }

    /// <summary>
    /// 获取订阅计划列表
    /// </summary>
    /// <param name="isActive">是否激活</param>
    /// <param name="isPublic">是否公开</param>
    /// <param name="planType">计划类型</param>
    /// <param name="searchTerm">搜索关键词</param>
    /// <param name="pageNumber">页码</param>
    /// <param name="pageSize">页大小</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>订阅计划列表</returns>
    public async Task<(IEnumerable<SubscriptionPlan> Plans, int TotalCount)> GetPlansAsync(
        bool? isActive = null,
        bool? isPublic = null,
        PlanType? planType = null,
        string? searchTerm = null,
        int pageNumber = 1,
        int pageSize = 20,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("获取订阅计划列表，是否激活: {IsActive}, 是否公开: {IsPublic}, 计划类型: {PlanType}, 搜索词: {SearchTerm}, 页码: {PageNumber}, 页大小: {PageSize}",
                isActive, isPublic, planType, searchTerm, pageNumber, pageSize);

            var query = _dbSet.AsQueryable();

            if (isActive.HasValue)
            {
                query = query.Where(sp => sp.IsActive == isActive.Value);
            }

            if (isPublic.HasValue)
            {
                query = query.Where(sp => sp.IsPublic == isPublic.Value);
            }

            if (planType.HasValue)
            {
                query = query.Where(sp => sp.PlanType == planType.Value);
            }

            if (!string.IsNullOrWhiteSpace(searchTerm))
            {
                var searchTermLower = searchTerm.ToLower();
                query = query.Where(sp => 
                    sp.Name.ToLower().Contains(searchTermLower) ||
                    sp.Code.ToLower().Contains(searchTermLower) ||
                    (sp.Description != null && sp.Description.ToLower().Contains(searchTermLower)));
            }

            var totalCount = await query.CountAsync(cancellationToken);

            var plans = await query
                .OrderBy(sp => sp.SortOrder)
                .ThenBy(sp => sp.Price)
                .Skip((pageNumber - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync(cancellationToken);

            return (plans, totalCount);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取订阅计划列表时发生错误");
            throw;
        }
    }

    /// <summary>
    /// 获取热门订阅计划
    /// </summary>
    /// <param name="count">数量</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>热门订阅计划列表</returns>
    public async Task<IEnumerable<SubscriptionPlan>> GetPopularPlansAsync(int count = 5, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("获取热门订阅计划，数量: {Count}", count);

            // 这里应该根据订阅数量来排序，暂时按创建时间排序
            return await _dbSet
                .Where(sp => sp.IsActive && sp.IsPublic)
                .OrderByDescending(sp => sp.CreatedAt)
                .Take(count)
                .ToListAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取热门订阅计划时发生错误，数量: {Count}", count);
            throw;
        }
    }

    /// <summary>
    /// 获取订阅计划的订阅数量
    /// </summary>
    /// <param name="planId">计划ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>订阅数量</returns>
    public async Task<int> GetSubscriptionCountAsync(Guid planId, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("获取订阅计划的订阅数量: {PlanId}", planId);

            return await _context.CustomerSubscriptions
                .Where(cs => cs.SubscriptionPlanId == planId)
                .CountAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取订阅计划的订阅数量时发生错误: {PlanId}", planId);
            throw;
        }
    }

    /// <summary>
    /// 获取订阅计划的活跃订阅数量
    /// </summary>
    /// <param name="planId">计划ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>活跃订阅数量</returns>
    public async Task<int> GetActiveSubscriptionCountAsync(Guid planId, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("获取订阅计划的活跃订阅数量: {PlanId}", planId);

            return await _context.CustomerSubscriptions
                .Where(cs => cs.SubscriptionPlanId == planId && cs.Status == SubscriptionStatus.Active)
                .CountAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取订阅计划的活跃订阅数量时发生错误: {PlanId}", planId);
            throw;
        }
    }

    // 添加缺失的接口方法的简化实现
    public Task<SubscriptionPlan?> GetByIdWithDetailsAsync(Guid id, CancellationToken cancellationToken = default)
    {
        return GetByIdAsync(id, cancellationToken);
    }

    public Task<SubscriptionPlan?> GetByPlanCodeAsync(string planCode, CancellationToken cancellationToken = default)
    {
        return GetByCodeAsync(planCode, cancellationToken);
    }

    public Task<IEnumerable<SubscriptionPlan>> GetActiveAsync(bool includeDetails = false, CancellationToken cancellationToken = default)
    {
        return GetAvailablePlansAsync(cancellationToken);
    }

    public Task<IEnumerable<SubscriptionPlan>> GetPublicPlansAsync(bool includeDetails = false, CancellationToken cancellationToken = default)
    {
        return GetAvailablePlansAsync(cancellationToken);
    }

    public Task<PagedResult<SubscriptionPlan>> GetAllAsync(int pageNumber = 1, int pageSize = 20, string? searchTerm = null, bool? isActive = null, CancellationToken cancellationToken = default)
    {
        return Task.FromResult(PagedResult<SubscriptionPlan>.Empty(pageNumber, pageSize));
    }

    public Task<IEnumerable<SubscriptionPlan>> GetPopularPlansAsync(CancellationToken cancellationToken = default)
    {
        return GetPopularPlansAsync(5, cancellationToken);
    }

    public Task<bool> ExistsByPlanCodeAsync(string planCode, Guid? excludeId = null, CancellationToken cancellationToken = default)
    {
        return IsCodeExistsAsync(planCode, excludeId, cancellationToken);
    }

    public Task<SubscriptionPlanStatistics> GetStatisticsAsync(CancellationToken cancellationToken = default)
    {
        return Task.FromResult(new SubscriptionPlanStatistics());
    }
}
