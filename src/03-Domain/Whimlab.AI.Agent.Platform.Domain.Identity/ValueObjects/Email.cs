using System.Text.RegularExpressions;
using Whimlab.AI.Agent.Platform.Domain.Core.Common;
using Whimlab.AI.Agent.Platform.Shared.Common.Extensions;

namespace Whimlab.AI.Agent.Platform.Domain.Identity.ValueObjects;

/// <summary>
/// 邮箱地址值对象
/// </summary>
public sealed class Email : SingleValueObject<string>
{
    private static readonly Regex EmailRegex = new(
        @"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$",
        RegexOptions.Compiled | RegexOptions.IgnoreCase);

    /// <summary>
    /// 初始化邮箱地址
    /// </summary>
    /// <param name="value">邮箱地址</param>
    private Email(string value) : base(value)
    {
    }

    /// <summary>
    /// 创建邮箱地址
    /// </summary>
    /// <param name="email">邮箱地址</param>
    /// <returns>邮箱地址值对象</returns>
    /// <exception cref="ArgumentException">邮箱地址格式无效</exception>
    public static Email Create(string email)
    {
        if (email.IsNullOrWhiteSpace())
            throw new ArgumentException("邮箱地址不能为空", nameof(email));

        var normalizedEmail = email.Trim().ToLowerInvariant();

        if (!EmailRegex.IsMatch(normalizedEmail))
            throw new ArgumentException($"邮箱地址格式无效: {email}", nameof(email));

        if (normalizedEmail.Length > 254) // RFC 5321 限制
            throw new ArgumentException("邮箱地址长度不能超过254个字符", nameof(email));

        return new Email(normalizedEmail);
    }

    /// <summary>
    /// 尝试创建邮箱地址
    /// </summary>
    /// <param name="email">邮箱地址</param>
    /// <param name="result">创建结果</param>
    /// <returns>是否创建成功</returns>
    public static bool TryCreate(string email, out Email? result)
    {
        result = null;
        
        try
        {
            result = Create(email);
            return true;
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// 获取域名部分
    /// </summary>
    /// <returns>域名</returns>
    public string GetDomain()
    {
        var atIndex = Value.LastIndexOf('@');
        return atIndex >= 0 ? Value[(atIndex + 1)..] : string.Empty;
    }

    /// <summary>
    /// 获取用户名部分
    /// </summary>
    /// <returns>用户名</returns>
    public string GetLocalPart()
    {
        var atIndex = Value.LastIndexOf('@');
        return atIndex >= 0 ? Value[..atIndex] : Value;
    }

    /// <summary>
    /// 检查是否为企业邮箱
    /// </summary>
    /// <returns>是否为企业邮箱</returns>
    public bool IsBusinessEmail()
    {
        var domain = GetDomain();
        var personalDomains = new[]
        {
            "gmail.com", "yahoo.com", "hotmail.com", "outlook.com", "live.com",
            "qq.com", "163.com", "126.com", "sina.com", "sohu.com"
        };

        return !personalDomains.Contains(domain, StringComparer.OrdinalIgnoreCase);
    }

    /// <summary>
    /// 掩码邮箱地址
    /// </summary>
    /// <returns>掩码后的邮箱地址</returns>
    public string ToMaskedString()
    {
        var localPart = GetLocalPart();
        var domain = GetDomain();

        if (localPart.Length <= 2)
            return $"{localPart[0]}***@{domain}";

        var visibleStart = Math.Min(2, localPart.Length / 3);
        var visibleEnd = Math.Min(1, localPart.Length / 3);
        
        return $"{localPart.Mask(visibleStart, visibleEnd)}@{domain}";
    }

    /// <summary>
    /// 隐式转换为字符串
    /// </summary>
    /// <param name="email">邮箱地址</param>
    /// <returns>邮箱地址字符串</returns>
    public static implicit operator string(Email email) => email.Value;

    /// <summary>
    /// 显式转换为邮箱地址
    /// </summary>
    /// <param name="email">邮箱地址字符串</param>
    /// <returns>邮箱地址值对象</returns>
    public static explicit operator Email(string email) => Create(email);
}
