using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Whimlab.AI.Agent.Platform.Domain.Core.Common;
using Whimlab.AI.Agent.Platform.Domain.Identity.Entities;
using Whimlab.AI.Agent.Platform.Domain.Identity.Repositories;
using Whimlab.AI.Agent.Platform.Infrastructure.Data;

namespace Whimlab.AI.Agent.Platform.Infrastructure.Repositories;

/// <summary>
/// 角色仓储实现
/// </summary>
public class RoleRepository : BaseRepository<Role, Guid>, IRoleRepository
{
    /// <summary>
    /// 初始化角色仓储
    /// </summary>
    /// <param name="context">数据库上下文</param>
    /// <param name="logger">日志记录器</param>
    public RoleRepository(
        AgentPlatformDbContext context,
        ILogger<RoleRepository> logger) : base(context, logger)
    {
    }

    /// <summary>
    /// 根据名称获取角色
    /// </summary>
    /// <param name="name">角色名称</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>角色实体</returns>
    public async Task<Role?> GetByNameAsync(string name, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("根据名称获取角色: {Name}", name);

            return await _dbSet
                .FirstOrDefaultAsync(r => r.Name == name, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "根据名称获取角色时发生错误: {Name}", name);
            throw;
        }
    }

    /// <summary>
    /// 检查角色名称是否存在
    /// </summary>
    /// <param name="name">角色名称</param>
    /// <param name="excludeRoleId">排除的角色ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>是否存在</returns>
    public async Task<bool> IsNameExistsAsync(string name, Guid? excludeRoleId = null, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("检查角色名称是否存在: {Name}, 排除角色ID: {ExcludeRoleId}", name, excludeRoleId);

            var query = _dbSet.Where(r => r.Name == name);

            if (excludeRoleId.HasValue)
            {
                query = query.Where(r => r.Id != excludeRoleId.Value);
            }

            return await query.AnyAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "检查角色名称是否存在时发生错误: {Name}", name);
            throw;
        }
    }

    /// <summary>
    /// 获取系统角色列表
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>系统角色列表</returns>
    public async Task<IEnumerable<Role>> GetSystemRolesAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("获取系统角色列表");

            return await _dbSet
                .Where(r => r.IsSystemRole)
                .OrderBy(r => r.Name)
                .ToListAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取系统角色列表时发生错误");
            throw;
        }
    }

    /// <summary>
    /// 获取角色列表
    /// </summary>
    /// <param name="isActive">是否激活</param>
    /// <param name="searchTerm">搜索关键词</param>
    /// <param name="pageNumber">页码</param>
    /// <param name="pageSize">页大小</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>角色列表</returns>
    public async Task<(IEnumerable<Role> Roles, int TotalCount)> GetRolesAsync(
        bool? isActive = null,
        string? searchTerm = null,
        int pageNumber = 1,
        int pageSize = 20,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("获取角色列表，是否激活: {IsActive}, 搜索词: {SearchTerm}, 页码: {PageNumber}, 页大小: {PageSize}",
                isActive, searchTerm, pageNumber, pageSize);

            var query = _dbSet.AsQueryable();

            if (isActive.HasValue)
            {
                query = query.Where(r => r.IsActive == isActive.Value);
            }

            if (!string.IsNullOrWhiteSpace(searchTerm))
            {
                var searchTermLower = searchTerm.ToLower();
                query = query.Where(r => 
                    r.Name.ToLower().Contains(searchTermLower) ||
                    r.DisplayName.ToLower().Contains(searchTermLower) ||
                    (r.Description != null && r.Description.ToLower().Contains(searchTermLower)));
            }

            var totalCount = await query.CountAsync(cancellationToken);

            var roles = await query
                .OrderBy(r => r.Name)
                .Skip((pageNumber - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync(cancellationToken);

            return (roles, totalCount);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取角色列表时发生错误");
            throw;
        }
    }

    /// <summary>
    /// 获取角色权限
    /// </summary>
    /// <param name="roleId">角色ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>权限列表</returns>
    public async Task<IEnumerable<Permission>> GetRolePermissionsAsync(Guid roleId, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("获取角色权限: {RoleId}", roleId);

            return await _context.RolePermissions
                .Where(rp => rp.RoleId == roleId)
                .Include(rp => rp.Permission)
                .Select(rp => rp.Permission)
                .ToListAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取角色权限时发生错误: {RoleId}", roleId);
            throw;
        }
    }

    /// <summary>
    /// 分配权限给角色
    /// </summary>
    /// <param name="roleId">角色ID</param>
    /// <param name="permissionId">权限ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>任务</returns>
    public async Task AssignPermissionAsync(Guid roleId, Guid permissionId, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("分配权限给角色: {RoleId}, 权限: {PermissionId}", roleId, permissionId);

            // 检查是否已存在
            var existingRolePermission = await _context.RolePermissions
                .FirstOrDefaultAsync(rp => rp.RoleId == roleId && rp.PermissionId == permissionId, cancellationToken);

            if (existingRolePermission == null)
            {
                var rolePermission = RolePermission.Create(roleId, permissionId);
                await _context.RolePermissions.AddAsync(rolePermission, cancellationToken);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "分配权限给角色时发生错误: {RoleId}, 权限: {PermissionId}", roleId, permissionId);
            throw;
        }
    }

    /// <summary>
    /// 移除角色权限
    /// </summary>
    /// <param name="roleId">角色ID</param>
    /// <param name="permissionId">权限ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>任务</returns>
    public async Task RemovePermissionAsync(Guid roleId, Guid permissionId, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("移除角色权限: {RoleId}, 权限: {PermissionId}", roleId, permissionId);

            var rolePermission = await _context.RolePermissions
                .FirstOrDefaultAsync(rp => rp.RoleId == roleId && rp.PermissionId == permissionId, cancellationToken);

            if (rolePermission != null)
            {
                _context.RolePermissions.Remove(rolePermission);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "移除角色权限时发生错误: {RoleId}, 权限: {PermissionId}", roleId, permissionId);
            throw;
        }
    }

    /// <summary>
    /// 获取角色的用户数量
    /// </summary>
    /// <param name="roleId">角色ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>用户数量</returns>
    public async Task<int> GetUserCountAsync(Guid roleId, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("获取角色的用户数量: {RoleId}", roleId);

            return await _context.UserRoles
                .Where(ur => ur.RoleId == roleId && ur.IsActive)
                .CountAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取角色的用户数量时发生错误: {RoleId}", roleId);
            throw;
        }
    }

    // 添加缺失的接口方法的简化实现
    public async Task<(IEnumerable<Role> Roles, int TotalCount)> GetRolesAsync(bool? isActive = null, bool includePermissions = false, string? searchTerm = null, int pageNumber = 1, int pageSize = 20, CancellationToken cancellationToken = default)
    {
        return await GetRolesAsync(isActive, searchTerm, pageNumber, pageSize, cancellationToken);
    }

    public async Task<IEnumerable<User>> GetUsersInRoleAsync(Guid roleId, CancellationToken cancellationToken = default)
    {
        return await _context.UserRoles
            .Where(ur => ur.RoleId == roleId && ur.IsActive)
            .Include(ur => ur.User)
            .Select(ur => ur.User)
            .ToListAsync(cancellationToken);
    }
}
