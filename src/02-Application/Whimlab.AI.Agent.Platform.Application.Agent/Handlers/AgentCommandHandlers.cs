using AutoMapper;
using Microsoft.Extensions.Logging;
using Whimlab.AI.Agent.Platform.Application.Core.Common;
using Whimlab.AI.Agent.Platform.Application.Core.Services;
using Whimlab.AI.Agent.Platform.Application.Agent.Commands;
using Whimlab.AI.Agent.Platform.Application.Agent.DTOs;
using Whimlab.AI.Agent.Platform.Application.Identity.Services;
using Whimlab.AI.Agent.Platform.Domain.Subscription.Services;
using Whimlab.AI.Agent.Platform.Domain.Agent.Entities;
using Whimlab.AI.Agent.Platform.Domain.Agent.Repositories;
using Whimlab.AI.Agent.Platform.Domain.Agent.Services;
using Whimlab.AI.Agent.Platform.Domain.Agent.ValueObjects;

namespace Whimlab.AI.Agent.Platform.Application.Agent.Handlers;

/// <summary>
/// 创建智能体命令处理器
/// </summary>
public class CreateAgentCommandHandler : ICommandHandler<CreateAgentCommand, AgentDto>
{
    private readonly IAgentRepository _agentRepository;
    private readonly IAgentValidationService _agentValidationService;
    private readonly IAuthorizationService _authorizationService;
    private readonly IQuotaManagementService _quotaManagementService;
    private readonly IMapper _mapper;
    private readonly ILogger<CreateAgentCommandHandler> _logger;

    /// <summary>
    /// 初始化创建智能体命令处理器
    /// </summary>
    public CreateAgentCommandHandler(
        IAgentRepository agentRepository,
        IAgentValidationService agentValidationService,
        IAuthorizationService authorizationService,
        IQuotaManagementService quotaManagementService,
        IMapper mapper,
        ILogger<CreateAgentCommandHandler> logger)
    {
        _agentRepository = agentRepository;
        _agentValidationService = agentValidationService;
        _authorizationService = authorizationService;
        _quotaManagementService = quotaManagementService;
        _mapper = mapper;
        _logger = logger;
    }

    /// <summary>
    /// 处理创建智能体命令
    /// </summary>
    public async Task<Result<AgentDto>> Handle(CreateAgentCommand request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("开始创建智能体: {Name}，用户: {UserId}", request.Name, request.CreatedBy);

            // 1. 验证权限
            var hasPermission = await _authorizationService.HasPermissionAsync(
                request.CreatedBy,
                ResourcePermissions.AgentPermissions.CreateAgent,
                cancellationToken);

            if (!hasPermission)
            {
                _logger.LogWarning("用户 {UserId} 没有创建智能体的权限", request.CreatedBy);
                return Result.Failure<AgentDto>("没有创建智能体的权限", "PERMISSION_DENIED");
            }

            // 2. 验证配额
            var quotaCheckResult = await _quotaManagementService.CheckQuotaAsync(
                request.CreatedBy,
                QuotaTypes.Agent.CreateAgent,
                1,
                cancellationToken);

            if (!quotaCheckResult.IsAllowed)
            {
                _logger.LogWarning("用户 {UserId} 智能体创建配额不足: {Reason}",
                    request.CreatedBy, quotaCheckResult.DenialReason);
                return Result.Failure<AgentDto>(
                    quotaCheckResult.DenialReason ?? "智能体创建配额不足",
                    "QUOTA_EXCEEDED");
            }

            // 3. 验证智能体名称是否可用
            var nameExists = await _agentRepository.ExistsAsync(request.Name, request.CreatedBy, cancellationToken: cancellationToken);
            if (nameExists)
            {
                return Result.Failure<AgentDto>("智能体名称已存在", "AGENT_NAME_EXISTS");
            }

            // 验证配置（简化版本，实际验证逻辑可以在领域服务中实现）
            if (string.IsNullOrWhiteSpace(request.Configuration.SystemPrompt))
            {
                return Result.Failure<AgentDto>("系统提示词不能为空", "INVALID_CONFIGURATION");
            }

            // 创建智能体配置
            var modelConfig = ModelConfiguration.Create(
                request.Configuration.ModelConfig.Provider,
                request.Configuration.ModelConfig.ModelName,
                request.Configuration.ModelConfig.ApiKey,
                request.Configuration.ModelConfig.ApiEndpoint);

            var tools = request.Configuration.ToolConfigs.Select(tc =>
                ToolConfiguration.Create(tc.ToolName, "工具描述", tc.ToolType, null, tc.Parameters, tc.IsEnabled)).ToList();

            var configuration = AgentConfiguration.Create(
                request.AgentType,
                modelConfig,
                request.Configuration.SystemPrompt,
                request.Configuration.ModelConfig.Temperature,
                request.Configuration.ModelConfig.MaxTokens,
                request.Configuration.ModelConfig.TopP,
                request.Configuration.ModelConfig.FrequencyPenalty,
                request.Configuration.ModelConfig.PresencePenalty,
                null, // stopSequences
                tools);

            // 创建智能体实体
            var agent = Domain.Agent.Entities.Agent.Create(
                request.Name,
                request.Description,
                request.AgentType,
                configuration,
                request.CreatedBy,
                request.IconUrl,
                request.Category,
                request.IsPublic);

            // 添加标签
            foreach (var tagName in request.Tags)
            {
                agent.AddTag(tagName);
            }

            // 保存智能体
            await _agentRepository.AddAsync(agent, cancellationToken);
            await _agentRepository.SaveChangesAsync(cancellationToken);

            // 4. 使用配额
            var quotaUsageResult = await _quotaManagementService.UseQuotaAsync(
                request.CreatedBy,
                QuotaTypes.Agent.CreateAgent,
                1,
                $"创建智能体: {request.Name}",
                cancellationToken);

            if (!quotaUsageResult.IsSuccess)
            {
                _logger.LogError("使用智能体创建配额失败，用户 {UserId}: {ErrorMessage}",
                    request.CreatedBy, quotaUsageResult.ErrorMessage);
                // 注意：这里智能体已经创建，但配额使用失败，可能需要回滚或记录异常
            }

            // 重新获取智能体
            var createdAgent = await _agentRepository.GetByIdAsync(agent.Id, cancellationToken);
            if (createdAgent == null)
            {
                return Result.Failure<AgentDto>("智能体创建失败", "AGENT_CREATION_FAILED");
            }

            var agentDto = _mapper.Map<AgentDto>(createdAgent);

            _logger.LogInformation("智能体创建成功: {AgentId} - {Name}，剩余配额: {RemainingQuota}",
                agent.Id, request.Name, quotaUsageResult.RemainingQuota);

            return Result.Success(agentDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "创建智能体时发生错误: {Name}", request.Name);
            return Result.Failure<AgentDto>("创建智能体时发生内部错误", "INTERNAL_ERROR");
        }
    }
}

/// <summary>
/// 更新智能体命令处理器
/// </summary>
public class UpdateAgentCommandHandler : ICommandHandler<UpdateAgentCommand, AgentDto>
{
    private readonly IAgentRepository _agentRepository;
    private readonly IAgentValidationService _agentValidationService;
    private readonly IAuthorizationService _authorizationService;
    private readonly IMapper _mapper;
    private readonly ILogger<UpdateAgentCommandHandler> _logger;

    /// <summary>
    /// 初始化更新智能体命令处理器
    /// </summary>
    public UpdateAgentCommandHandler(
        IAgentRepository agentRepository,
        IAgentValidationService agentValidationService,
        IAuthorizationService authorizationService,
        IMapper mapper,
        ILogger<UpdateAgentCommandHandler> logger)
    {
        _agentRepository = agentRepository;
        _agentValidationService = agentValidationService;
        _authorizationService = authorizationService;
        _mapper = mapper;
        _logger = logger;
    }

    /// <summary>
    /// 处理更新智能体命令
    /// </summary>
    public async Task<Result<AgentDto>> Handle(UpdateAgentCommand request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("开始更新智能体: {AgentId}，操作用户: {UserId}", request.Id, request.OperatorId);

            // 1. 获取智能体
            var agent = await _agentRepository.GetByIdAsync(request.Id, cancellationToken);
            if (agent == null)
            {
                return Result.Failure<AgentDto>("智能体不存在", "AGENT_NOT_FOUND");
            }

            // 2. 验证权限 - 检查资源访问权限
            var canAccess = await _authorizationService.CanAccessResourceAsync(
                request.OperatorId,
                ResourcePermissions.ResourceTypes.Agent,
                request.Id,
                ResourcePermissions.Operations.Update,
                cancellationToken);

            if (!canAccess)
            {
                _logger.LogWarning("用户 {UserId} 没有权限修改智能体 {AgentId}",
                    request.OperatorId, request.Id);
                return Result.Failure<AgentDto>("没有权限修改此智能体", "PERMISSION_DENIED");
            }

            // 检查名称是否可用（如果名称有变化）
            if (agent.Name != request.Name)
            {
                var nameExists = await _agentRepository.ExistsAsync(request.Name, agent.CreatedBy, request.Id, cancellationToken);
                if (nameExists)
                {
                    return Result.Failure<AgentDto>("智能体名称已存在", "AGENT_NAME_EXISTS");
                }
            }

            // 更新智能体信息
            agent.UpdateInfo(request.Name, request.Description, request.IconUrl, request.Category, request.IsPublic);

            // 保存更改
            await _agentRepository.UpdateAsync(agent, cancellationToken);
            await _agentRepository.SaveChangesAsync(cancellationToken);

            // 重新获取智能体
            var updatedAgent = await _agentRepository.GetByIdAsync(agent.Id, cancellationToken);
            var agentDto = _mapper.Map<AgentDto>(updatedAgent);

            _logger.LogInformation("智能体更新成功: {AgentId}", request.Id);

            return Result.Success(agentDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新智能体时发生错误: {AgentId}", request.Id);
            return Result.Failure<AgentDto>("更新智能体时发生内部错误", "INTERNAL_ERROR");
        }
    }
}

/// <summary>
/// 发布智能体命令处理器
/// </summary>
public class PublishAgentCommandHandler : ICommandHandler<PublishAgentCommand>
{
    private readonly IAgentRepository _agentRepository;
    private readonly IAgentValidationService _agentValidationService;
    private readonly IAuthorizationService _authorizationService;
    private readonly IQuotaManagementService _quotaManagementService;
    private readonly ILogger<PublishAgentCommandHandler> _logger;

    /// <summary>
    /// 初始化发布智能体命令处理器
    /// </summary>
    public PublishAgentCommandHandler(
        IAgentRepository agentRepository,
        IAgentValidationService agentValidationService,
        IAuthorizationService authorizationService,
        IQuotaManagementService quotaManagementService,
        ILogger<PublishAgentCommandHandler> logger)
    {
        _agentRepository = agentRepository;
        _agentValidationService = agentValidationService;
        _authorizationService = authorizationService;
        _quotaManagementService = quotaManagementService;
        _logger = logger;
    }

    /// <summary>
    /// 处理发布智能体命令
    /// </summary>
    public async Task<Result> Handle(PublishAgentCommand request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("开始发布智能体: {AgentId}，操作用户: {UserId}", request.Id, request.OperatorId);

            // 1. 获取智能体
            var agent = await _agentRepository.GetByIdAsync(request.Id, cancellationToken);
            if (agent == null)
            {
                return Result.Failure("智能体不存在", "AGENT_NOT_FOUND");
            }

            // 2. 验证权限 - 检查资源访问权限
            var canAccess = await _authorizationService.CanAccessResourceAsync(
                request.OperatorId,
                ResourcePermissions.ResourceTypes.Agent,
                request.Id,
                ResourcePermissions.Operations.Update,
                cancellationToken);

            if (!canAccess)
            {
                _logger.LogWarning("用户 {UserId} 没有权限发布智能体 {AgentId}",
                    request.OperatorId, request.Id);
                return Result.Failure("没有权限发布此智能体", "PERMISSION_DENIED");
            }

            // 3. 验证发布配额
            var quotaCheckResult = await _quotaManagementService.CheckQuotaAsync(
                request.OperatorId,
                QuotaTypes.Agent.PublishAgent,
                1,
                cancellationToken);

            if (!quotaCheckResult.IsAllowed)
            {
                _logger.LogWarning("用户 {UserId} 智能体发布配额不足: {Reason}",
                    request.OperatorId, quotaCheckResult.DenialReason);
                return Result.Failure(
                    quotaCheckResult.DenialReason ?? "智能体发布配额不足",
                    "QUOTA_EXCEEDED");
            }

            // 4. 验证发布条件（简化版本）
            if (string.IsNullOrWhiteSpace(agent.Configuration.SystemPrompt))
            {
                return Result.Failure("系统提示词不能为空，无法发布", "PUBLISH_VALIDATION_FAILED");
            }

            // 5. 发布智能体
            agent.Publish();

            // 6. 保存更改
            await _agentRepository.UpdateAsync(agent, cancellationToken);
            await _agentRepository.SaveChangesAsync(cancellationToken);

            // 7. 使用发布配额
            var quotaUsageResult = await _quotaManagementService.UseQuotaAsync(
                request.OperatorId,
                QuotaTypes.Agent.PublishAgent,
                1,
                $"发布智能体: {agent.Name}",
                cancellationToken);

            if (!quotaUsageResult.IsSuccess)
            {
                _logger.LogError("使用智能体发布配额失败，用户 {UserId}: {ErrorMessage}",
                    request.OperatorId, quotaUsageResult.ErrorMessage);
            }

            _logger.LogInformation("智能体发布成功: {AgentId}，剩余发布配额: {RemainingQuota}",
                request.Id, quotaUsageResult.RemainingQuota);

            return Result.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "发布智能体时发生错误: {AgentId}", request.Id);
            return Result.Failure("发布智能体时发生内部错误", "INTERNAL_ERROR");
        }
    }
}
