using AutoMapper;
using Microsoft.Extensions.Logging;
using Whimlab.AI.Agent.Platform.Application.Core.Common;
using Whimlab.AI.Agent.Platform.Application.Subscription.DTOs;
using Whimlab.AI.Agent.Platform.Application.Subscription.Queries;
using Whimlab.AI.Agent.Platform.Domain.Core.Common;
using Whimlab.AI.Agent.Platform.Domain.Subscription.Repositories;

namespace Whimlab.AI.Agent.Platform.Application.Subscription.Handlers;

/// <summary>
/// 根据ID获取订阅计划查询处理器
/// </summary>
public class GetSubscriptionPlanByIdQueryHandler : IQueryHandler<GetSubscriptionPlanByIdQuery, SubscriptionPlanDto?>
{
    private readonly ISubscriptionPlanRepository _subscriptionPlanRepository;
    private readonly IMapper _mapper;
    private readonly ILogger<GetSubscriptionPlanByIdQueryHandler> _logger;

    /// <summary>
    /// 初始化查询处理器
    /// </summary>
    public GetSubscriptionPlanByIdQueryHandler(
        ISubscriptionPlanRepository subscriptionPlanRepository,
        IMapper mapper,
        ILogger<GetSubscriptionPlanByIdQueryHandler> logger)
    {
        _subscriptionPlanRepository = subscriptionPlanRepository;
        _mapper = mapper;
        _logger = logger;
    }

    /// <summary>
    /// 处理查询
    /// </summary>
    public async Task<Result<SubscriptionPlanDto?>> Handle(GetSubscriptionPlanByIdQuery request, CancellationToken cancellationToken)
    {
        try
        {
            var subscriptionPlan = await _subscriptionPlanRepository.GetByIdAsync(request.Id, cancellationToken);

            if (subscriptionPlan == null)
            {
                return Result.Success<SubscriptionPlanDto?>(null);
            }

            var planDto = _mapper.Map<SubscriptionPlanDto>(subscriptionPlan);
            return Result.Success<SubscriptionPlanDto?>(planDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取订阅计划时发生错误: {PlanId}", request.Id);
            return Result.Failure<SubscriptionPlanDto?>("获取订阅计划时发生内部错误", "INTERNAL_ERROR");
        }
    }
}

/// <summary>
/// 获取订阅计划列表查询处理器
/// </summary>
public class GetSubscriptionPlansQueryHandler : IQueryHandler<GetSubscriptionPlansQuery, PagedResult<SubscriptionPlanDto>>
{
    private readonly ISubscriptionPlanRepository _subscriptionPlanRepository;
    private readonly IMapper _mapper;
    private readonly ILogger<GetSubscriptionPlansQueryHandler> _logger;

    /// <summary>
    /// 初始化查询处理器
    /// </summary>
    public GetSubscriptionPlansQueryHandler(
        ISubscriptionPlanRepository subscriptionPlanRepository,
        IMapper mapper,
        ILogger<GetSubscriptionPlansQueryHandler> logger)
    {
        _subscriptionPlanRepository = subscriptionPlanRepository;
        _mapper = mapper;
        _logger = logger;
    }

    /// <summary>
    /// 处理查询
    /// </summary>
    public async Task<Result<PagedResult<SubscriptionPlanDto>>> Handle(GetSubscriptionPlansQuery request, CancellationToken cancellationToken)
    {
        try
        {
            request.Validate();

            var subscriptionPlans = await _subscriptionPlanRepository.GetAllAsync(
                request.PageNumber,
                request.PageSize,
                null, // searchTerm
                request.IsActive,
                cancellationToken);

            var planDtos = subscriptionPlans.Items.Select(p => _mapper.Map<SubscriptionPlanDto>(p));
            var result = new PagedResult<SubscriptionPlanDto>(planDtos, subscriptionPlans.TotalCount, request.PageNumber, request.PageSize);

            return Result.Success(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取订阅计划列表时发生错误");
            return Result.Failure<PagedResult<SubscriptionPlanDto>>("获取订阅计划列表时发生内部错误", "INTERNAL_ERROR");
        }
    }
}

/// <summary>
/// 获取公开订阅计划查询处理器
/// </summary>
public class GetPublicSubscriptionPlansQueryHandler : IQueryHandler<GetPublicSubscriptionPlansQuery, IEnumerable<SubscriptionPlanDto>>
{
    private readonly ISubscriptionPlanRepository _subscriptionPlanRepository;
    private readonly IMapper _mapper;
    private readonly ILogger<GetPublicSubscriptionPlansQueryHandler> _logger;

    /// <summary>
    /// 初始化查询处理器
    /// </summary>
    public GetPublicSubscriptionPlansQueryHandler(
        ISubscriptionPlanRepository subscriptionPlanRepository,
        IMapper mapper,
        ILogger<GetPublicSubscriptionPlansQueryHandler> logger)
    {
        _subscriptionPlanRepository = subscriptionPlanRepository;
        _mapper = mapper;
        _logger = logger;
    }

    /// <summary>
    /// 处理查询
    /// </summary>
    public async Task<Result<IEnumerable<SubscriptionPlanDto>>> Handle(GetPublicSubscriptionPlansQuery request, CancellationToken cancellationToken)
    {
        try
        {
            var subscriptionPlans = await _subscriptionPlanRepository.GetPublicPlansAsync(true, cancellationToken);

            var planDtos = subscriptionPlans.Select(p => _mapper.Map<SubscriptionPlanDto>(p));

            if (request.SortByPrice)
            {
                planDtos = planDtos.OrderBy(p => p.MonthlyPrice);
            }

            return Result.Success(planDtos);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取公开订阅计划时发生错误");
            return Result.Failure<IEnumerable<SubscriptionPlanDto>>("获取公开订阅计划时发生内部错误", "INTERNAL_ERROR");
        }
    }
}

/// <summary>
/// 根据ID获取客户订阅查询处理器
/// </summary>
public class GetCustomerSubscriptionByIdQueryHandler : IQueryHandler<GetCustomerSubscriptionByIdQuery, CustomerSubscriptionDto?>
{
    private readonly ICustomerSubscriptionRepository _customerSubscriptionRepository;
    private readonly IMapper _mapper;
    private readonly ILogger<GetCustomerSubscriptionByIdQueryHandler> _logger;

    /// <summary>
    /// 初始化查询处理器
    /// </summary>
    public GetCustomerSubscriptionByIdQueryHandler(
        ICustomerSubscriptionRepository customerSubscriptionRepository,
        IMapper mapper,
        ILogger<GetCustomerSubscriptionByIdQueryHandler> logger)
    {
        _customerSubscriptionRepository = customerSubscriptionRepository;
        _mapper = mapper;
        _logger = logger;
    }

    /// <summary>
    /// 处理查询
    /// </summary>
    public async Task<Result<CustomerSubscriptionDto?>> Handle(GetCustomerSubscriptionByIdQuery request, CancellationToken cancellationToken)
    {
        try
        {
            var customerSubscription = await _customerSubscriptionRepository.GetByIdAsync(request.Id, cancellationToken);

            if (customerSubscription == null)
            {
                return Result.Success<CustomerSubscriptionDto?>(null);
            }

            var subscriptionDto = _mapper.Map<CustomerSubscriptionDto>(customerSubscription);
            return Result.Success<CustomerSubscriptionDto?>(subscriptionDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取客户订阅时发生错误: {SubscriptionId}", request.Id);
            return Result.Failure<CustomerSubscriptionDto?>("获取客户订阅时发生内部错误", "INTERNAL_ERROR");
        }
    }
}

/// <summary>
/// 获取客户当前订阅查询处理器
/// </summary>
public class GetCustomerCurrentSubscriptionQueryHandler : IQueryHandler<GetCustomerCurrentSubscriptionQuery, CustomerSubscriptionDto?>
{
    private readonly ICustomerSubscriptionRepository _customerSubscriptionRepository;
    private readonly IMapper _mapper;
    private readonly ILogger<GetCustomerCurrentSubscriptionQueryHandler> _logger;

    /// <summary>
    /// 初始化查询处理器
    /// </summary>
    public GetCustomerCurrentSubscriptionQueryHandler(
        ICustomerSubscriptionRepository customerSubscriptionRepository,
        IMapper mapper,
        ILogger<GetCustomerCurrentSubscriptionQueryHandler> logger)
    {
        _customerSubscriptionRepository = customerSubscriptionRepository;
        _mapper = mapper;
        _logger = logger;
    }

    /// <summary>
    /// 处理查询
    /// </summary>
    public async Task<Result<CustomerSubscriptionDto?>> Handle(GetCustomerCurrentSubscriptionQuery request, CancellationToken cancellationToken)
    {
        try
        {
            var customerSubscription = await _customerSubscriptionRepository.GetActiveByCustomerIdAsync(request.CustomerId, cancellationToken);

            if (customerSubscription == null)
            {
                return Result.Success<CustomerSubscriptionDto?>(null);
            }

            var subscriptionDto = _mapper.Map<CustomerSubscriptionDto>(customerSubscription);
            return Result.Success<CustomerSubscriptionDto?>(subscriptionDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取客户当前订阅时发生错误: {CustomerId}", request.CustomerId);
            return Result.Failure<CustomerSubscriptionDto?>("获取客户当前订阅时发生内部错误", "INTERNAL_ERROR");
        }
    }
}

/// <summary>
/// 获取客户配额使用情况查询处理器
/// </summary>
public class GetCustomerQuotaUsageQueryHandler : IQueryHandler<GetCustomerQuotaUsageQuery, IEnumerable<QuotaUsageDto>>
{
    private readonly ICustomerSubscriptionRepository _customerSubscriptionRepository;
    private readonly IMapper _mapper;
    private readonly ILogger<GetCustomerQuotaUsageQueryHandler> _logger;

    /// <summary>
    /// 初始化查询处理器
    /// </summary>
    public GetCustomerQuotaUsageQueryHandler(
        ICustomerSubscriptionRepository customerSubscriptionRepository,
        IMapper mapper,
        ILogger<GetCustomerQuotaUsageQueryHandler> logger)
    {
        _customerSubscriptionRepository = customerSubscriptionRepository;
        _mapper = mapper;
        _logger = logger;
    }

    /// <summary>
    /// 处理查询
    /// </summary>
    public async Task<Result<IEnumerable<QuotaUsageDto>>> Handle(GetCustomerQuotaUsageQuery request, CancellationToken cancellationToken)
    {
        try
        {
            var customerSubscription = await _customerSubscriptionRepository.GetActiveByCustomerIdAsync(request.CustomerId, cancellationToken);

            if (customerSubscription == null)
            {
                return Result.Success(Enumerable.Empty<QuotaUsageDto>());
            }

            var quotaUsages = customerSubscription.QuotaUsages;

            if (!string.IsNullOrWhiteSpace(request.QuotaType))
            {
                quotaUsages = quotaUsages.Where(q => q.QuotaType == request.QuotaType).ToList();
            }

            var quotaUsageDtos = quotaUsages.Select(q => _mapper.Map<QuotaUsageDto>(q));
            return Result.Success(quotaUsageDtos);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取客户配额使用情况时发生错误: {CustomerId}", request.CustomerId);
            return Result.Failure<IEnumerable<QuotaUsageDto>>("获取配额使用情况时发生内部错误", "INTERNAL_ERROR");
        }
    }
}

/// <summary>
/// 检查配额可用性查询处理器
/// </summary>
public class CheckQuotaAvailabilityQueryHandler : IQueryHandler<CheckQuotaAvailabilityQuery, bool>
{
    private readonly ICustomerSubscriptionRepository _customerSubscriptionRepository;
    private readonly ILogger<CheckQuotaAvailabilityQueryHandler> _logger;

    /// <summary>
    /// 初始化查询处理器
    /// </summary>
    public CheckQuotaAvailabilityQueryHandler(
        ICustomerSubscriptionRepository customerSubscriptionRepository,
        ILogger<CheckQuotaAvailabilityQueryHandler> logger)
    {
        _customerSubscriptionRepository = customerSubscriptionRepository;
        _logger = logger;
    }

    /// <summary>
    /// 处理查询
    /// </summary>
    public async Task<Result<bool>> Handle(CheckQuotaAvailabilityQuery request, CancellationToken cancellationToken)
    {
        try
        {
            var customerSubscription = await _customerSubscriptionRepository.GetActiveByCustomerIdAsync(request.CustomerId, cancellationToken);

            if (customerSubscription == null)
            {
                return Result.Success(false); // 没有活跃订阅
            }

            // 检查配额可用性 - 简化实现，实际需要从订阅计划中获取配额限制
            var currentUsage = customerSubscription.GetCurrentQuotaUsage(request.QuotaType);
            var canUse = currentUsage + request.RequestedAmount <= long.MaxValue; // 简化逻辑
            return Result.Success(canUse);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "检查配额可用性时发生错误: {CustomerId}", request.CustomerId);
            return Result.Failure<bool>("检查配额可用性时发生内部错误", "INTERNAL_ERROR");
        }
    }
}
