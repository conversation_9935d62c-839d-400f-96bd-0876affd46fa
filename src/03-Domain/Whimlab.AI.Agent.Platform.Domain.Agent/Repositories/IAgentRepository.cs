using Whimlab.AI.Agent.Platform.Domain.Agent.Entities;
using Whimlab.AI.Agent.Platform.Domain.Core.Common;
using Whimlab.AI.Agent.Platform.Shared.Common.Enums;

namespace Whimlab.AI.Agent.Platform.Domain.Agent.Repositories;

/// <summary>
/// 智能体仓储接口
/// </summary>
public interface IAgentRepository
{
    /// <summary>
    /// 根据ID获取智能体
    /// </summary>
    /// <param name="id">智能体ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>智能体实体</returns>
    Task<Entities.Agent?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default);

    /// <summary>
    /// 根据ID获取智能体（包含版本信息）
    /// </summary>
    /// <param name="id">智能体ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>智能体实体</returns>
    Task<Entities.Agent?> GetByIdWithVersionsAsync(Guid id, CancellationToken cancellationToken = default);

    /// <summary>
    /// 根据名称和创建者获取智能体
    /// </summary>
    /// <param name="name">智能体名称</param>
    /// <param name="createdBy">创建者ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>智能体实体</returns>
    Task<Entities.Agent?> GetByNameAndCreatorAsync(string name, Guid createdBy, CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取用户的智能体列表
    /// </summary>
    /// <param name="createdBy">创建者ID</param>
    /// <param name="pageNumber">页码</param>
    /// <param name="pageSize">页大小</param>
    /// <param name="searchTerm">搜索关键词</param>
    /// <param name="agentType">智能体类型过滤</param>
    /// <param name="status">状态过滤</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>分页的智能体列表</returns>
    Task<PagedResult<Entities.Agent>> GetUserAgentsAsync(
        Guid createdBy,
        int pageNumber = 1,
        int pageSize = 20,
        string? searchTerm = null,
        AgentType? agentType = null,
        AgentStatus? status = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取公开的智能体列表
    /// </summary>
    /// <param name="pageNumber">页码</param>
    /// <param name="pageSize">页大小</param>
    /// <param name="searchTerm">搜索关键词</param>
    /// <param name="category">分类过滤</param>
    /// <param name="agentType">智能体类型过滤</param>
    /// <param name="sortBy">排序字段</param>
    /// <param name="sortDirection">排序方向</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>分页的公开智能体列表</returns>
    Task<PagedResult<Entities.Agent>> GetPublicAgentsAsync(
        int pageNumber = 1,
        int pageSize = 20,
        string? searchTerm = null,
        string? category = null,
        AgentType? agentType = null,
        string? sortBy = null,
        string? sortDirection = "asc",
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取热门智能体列表
    /// </summary>
    /// <param name="count">数量</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>热门智能体列表</returns>
    Task<IEnumerable<Entities.Agent>> GetPopularAgentsAsync(int count = 10, CancellationToken cancellationToken = default);

    /// <summary>
    /// 根据标签获取智能体列表
    /// </summary>
    /// <param name="tags">标签列表</param>
    /// <param name="pageNumber">页码</param>
    /// <param name="pageSize">页大小</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>分页的智能体列表</returns>
    Task<PagedResult<Entities.Agent>> GetAgentsByTagsAsync(
        IEnumerable<string> tags,
        int pageNumber = 1,
        int pageSize = 20,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 检查智能体名称是否已存在
    /// </summary>
    /// <param name="name">智能体名称</param>
    /// <param name="createdBy">创建者ID</param>
    /// <param name="excludeId">排除的智能体ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>是否存在</returns>
    Task<bool> ExistsAsync(string name, Guid createdBy, Guid? excludeId = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取智能体统计信息
    /// </summary>
    /// <param name="createdBy">创建者ID（可选）</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>统计信息</returns>
    Task<AgentStatistics> GetStatisticsAsync(Guid? createdBy = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// 添加智能体
    /// </summary>
    /// <param name="agent">智能体实体</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>任务</returns>
    Task AddAsync(Entities.Agent agent, CancellationToken cancellationToken = default);

    /// <summary>
    /// 更新智能体
    /// </summary>
    /// <param name="agent">智能体实体</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>任务</returns>
    Task UpdateAsync(Entities.Agent agent, CancellationToken cancellationToken = default);

    /// <summary>
    /// 删除智能体
    /// </summary>
    /// <param name="agent">智能体实体</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>任务</returns>
    Task DeleteAsync(Entities.Agent agent, CancellationToken cancellationToken = default);

    /// <summary>
    /// 保存更改
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>受影响的行数</returns>
    Task<int> SaveChangesAsync(CancellationToken cancellationToken = default);
}

/// <summary>
/// 智能体统计信息
/// </summary>
public class AgentStatistics
{
    /// <summary>
    /// 总数量
    /// </summary>
    public int TotalCount { get; set; }

    /// <summary>
    /// 已发布数量
    /// </summary>
    public int PublishedCount { get; set; }

    /// <summary>
    /// 草稿数量
    /// </summary>
    public int DraftCount { get; set; }

    /// <summary>
    /// 已归档数量
    /// </summary>
    public int ArchivedCount { get; set; }

    /// <summary>
    /// 总使用次数
    /// </summary>
    public long TotalUsageCount { get; set; }

    /// <summary>
    /// 平均评分
    /// </summary>
    public double AverageRating { get; set; }

    /// <summary>
    /// 按类型分组的统计
    /// </summary>
    public Dictionary<AgentType, int> CountByType { get; set; } = new();

    /// <summary>
    /// 按分类分组的统计
    /// </summary>
    public Dictionary<string, int> CountByCategory { get; set; } = new();
}
