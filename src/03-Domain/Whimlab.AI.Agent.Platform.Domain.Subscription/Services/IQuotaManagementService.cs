using Whimlab.AI.Agent.Platform.Domain.Subscription.Entities;

namespace Whimlab.AI.Agent.Platform.Domain.Subscription.Services;

/// <summary>
/// 配额管理服务接口
/// </summary>
public interface IQuotaManagementService
{
    /// <summary>
    /// 检查配额是否足够
    /// </summary>
    /// <param name="customerId">客户ID</param>
    /// <param name="quotaType">配额类型</param>
    /// <param name="requestedAmount">请求使用量</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>配额检查结果</returns>
    Task<QuotaCheckResult> CheckQuotaAsync(Guid customerId, string quotaType, long requestedAmount, CancellationToken cancellationToken = default);

    /// <summary>
    /// 使用配额
    /// </summary>
    /// <param name="customerId">客户ID</param>
    /// <param name="quotaType">配额类型</param>
    /// <param name="amount">使用量</param>
    /// <param name="description">使用描述</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>配额使用结果</returns>
    Task<QuotaUsageResult> UseQuotaAsync(Guid customerId, string quotaType, long amount, string? description = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取客户的配额状态
    /// </summary>
    /// <param name="customerId">客户ID</param>
    /// <param name="quotaType">配额类型（可选）</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>配额状态</returns>
    Task<CustomerQuotaStatus> GetQuotaStatusAsync(Guid customerId, string? quotaType = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// 重置客户配额
    /// </summary>
    /// <param name="customerId">客户ID</param>
    /// <param name="quotaType">配额类型</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>任务</returns>
    Task ResetQuotaAsync(Guid customerId, string quotaType, CancellationToken cancellationToken = default);

    /// <summary>
    /// 批量重置配额（用于定时任务）
    /// </summary>
    /// <param name="quotaType">配额类型</param>
    /// <param name="resetPeriod">重置周期</param>
    /// <param name="batchSize">批次大小</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>重置的客户数量</returns>
    Task<int> BatchResetQuotaAsync(string quotaType, string resetPeriod, int batchSize = 1000, CancellationToken cancellationToken = default);

    /// <summary>
    /// 计算配额使用率
    /// </summary>
    /// <param name="customerId">客户ID</param>
    /// <param name="quotaType">配额类型</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>使用率（0-1）</returns>
    Task<double> CalculateUsageRateAsync(Guid customerId, string quotaType, CancellationToken cancellationToken = default);

    /// <summary>
    /// 预测配额耗尽时间
    /// </summary>
    /// <param name="customerId">客户ID</param>
    /// <param name="quotaType">配额类型</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>预计耗尽时间</returns>
    Task<DateTime?> PredictQuotaExhaustionAsync(Guid customerId, string quotaType, CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取配额使用趋势
    /// </summary>
    /// <param name="customerId">客户ID</param>
    /// <param name="quotaType">配额类型</param>
    /// <param name="days">天数</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>使用趋势</returns>
    Task<QuotaUsageTrend> GetUsageTrendAsync(Guid customerId, string quotaType, int days = 30, CancellationToken cancellationToken = default);

    /// <summary>
    /// 检查是否需要配额警告
    /// </summary>
    /// <param name="customerId">客户ID</param>
    /// <param name="quotaType">配额类型</param>
    /// <param name="warningThreshold">警告阈值（0-1）</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>是否需要警告</returns>
    Task<bool> ShouldWarnAboutQuotaAsync(Guid customerId, string quotaType, double warningThreshold = 0.8, CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取超限的客户列表
    /// </summary>
    /// <param name="quotaType">配额类型</param>
    /// <param name="batchSize">批次大小</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>超限客户列表</returns>
    Task<IEnumerable<Guid>> GetOverLimitCustomersAsync(string quotaType, int batchSize = 100, CancellationToken cancellationToken = default);

    /// <summary>
    /// 升级客户订阅计划
    /// </summary>
    /// <param name="customerId">客户ID</param>
    /// <param name="newPlanId">新计划ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>升级结果</returns>
    Task<SubscriptionUpgradeResult> UpgradeSubscriptionAsync(Guid customerId, Guid newPlanId, CancellationToken cancellationToken = default);

    /// <summary>
    /// 临时增加配额
    /// </summary>
    /// <param name="customerId">客户ID</param>
    /// <param name="quotaType">配额类型</param>
    /// <param name="additionalAmount">增加量</param>
    /// <param name="expiresAt">过期时间</param>
    /// <param name="reason">增加原因</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>任务</returns>
    Task AddTemporaryQuotaAsync(Guid customerId, string quotaType, long additionalAmount, DateTime expiresAt, string reason, CancellationToken cancellationToken = default);
}

/// <summary>
/// 配额检查结果
/// </summary>
public class QuotaCheckResult
{
    /// <summary>
    /// 是否允许使用
    /// </summary>
    public bool IsAllowed { get; set; }

    /// <summary>
    /// 当前使用量
    /// </summary>
    public long CurrentUsage { get; set; }

    /// <summary>
    /// 配额限制
    /// </summary>
    public long QuotaLimit { get; set; }

    /// <summary>
    /// 剩余配额
    /// </summary>
    public long RemainingQuota { get; set; }

    /// <summary>
    /// 拒绝原因
    /// </summary>
    public string? DenialReason { get; set; }

    /// <summary>
    /// 建议操作
    /// </summary>
    public List<string> SuggestedActions { get; set; } = new();
}

/// <summary>
/// 配额使用结果
/// </summary>
public class QuotaUsageResult
{
    /// <summary>
    /// 是否成功
    /// </summary>
    public bool IsSuccess { get; set; }

    /// <summary>
    /// 使用后的累计量
    /// </summary>
    public long NewCumulativeUsage { get; set; }

    /// <summary>
    /// 剩余配额
    /// </summary>
    public long RemainingQuota { get; set; }

    /// <summary>
    /// 错误消息
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// 是否触发警告
    /// </summary>
    public bool TriggeredWarning { get; set; }

    /// <summary>
    /// 警告消息
    /// </summary>
    public string? WarningMessage { get; set; }
}

/// <summary>
/// 客户配额状态
/// </summary>
public class CustomerQuotaStatus
{
    /// <summary>
    /// 客户ID
    /// </summary>
    public Guid CustomerId { get; set; }

    /// <summary>
    /// 订阅计划ID
    /// </summary>
    public Guid SubscriptionPlanId { get; set; }

    /// <summary>
    /// 配额详情
    /// </summary>
    public List<QuotaDetail> QuotaDetails { get; set; } = new();

    /// <summary>
    /// 当前周期开始时间
    /// </summary>
    public DateTime CurrentPeriodStart { get; set; }

    /// <summary>
    /// 当前周期结束时间
    /// </summary>
    public DateTime CurrentPeriodEnd { get; set; }

    /// <summary>
    /// 订阅状态
    /// </summary>
    public string SubscriptionStatus { get; set; } = string.Empty;
}

/// <summary>
/// 配额详情
/// </summary>
public class QuotaDetail
{
    /// <summary>
    /// 配额类型
    /// </summary>
    public string QuotaType { get; set; } = string.Empty;

    /// <summary>
    /// 配额限制
    /// </summary>
    public long Limit { get; set; }

    /// <summary>
    /// 当前使用量
    /// </summary>
    public long CurrentUsage { get; set; }

    /// <summary>
    /// 剩余配额
    /// </summary>
    public long RemainingQuota { get; set; }

    /// <summary>
    /// 使用率
    /// </summary>
    public double UsageRate { get; set; }

    /// <summary>
    /// 配额周期
    /// </summary>
    public string Period { get; set; } = string.Empty;

    /// <summary>
    /// 是否无限制
    /// </summary>
    public bool IsUnlimited { get; set; }

    /// <summary>
    /// 下次重置时间
    /// </summary>
    public DateTime? NextResetAt { get; set; }
}

/// <summary>
/// 配额使用趋势
/// </summary>
public class QuotaUsageTrend
{
    /// <summary>
    /// 客户ID
    /// </summary>
    public Guid CustomerId { get; set; }

    /// <summary>
    /// 配额类型
    /// </summary>
    public string QuotaType { get; set; } = string.Empty;

    /// <summary>
    /// 每日使用量
    /// </summary>
    public Dictionary<DateTime, long> DailyUsage { get; set; } = new();

    /// <summary>
    /// 平均每日使用量
    /// </summary>
    public double AverageDailyUsage { get; set; }

    /// <summary>
    /// 使用量趋势（增长/下降）
    /// </summary>
    public TrendDirection Trend { get; set; }

    /// <summary>
    /// 趋势变化率
    /// </summary>
    public double TrendRate { get; set; }

    /// <summary>
    /// 峰值使用日期
    /// </summary>
    public DateTime PeakUsageDate { get; set; }

    /// <summary>
    /// 峰值使用量
    /// </summary>
    public long PeakUsageAmount { get; set; }
}

/// <summary>
/// 订阅升级结果
/// </summary>
public class SubscriptionUpgradeResult
{
    /// <summary>
    /// 是否成功
    /// </summary>
    public bool IsSuccess { get; set; }

    /// <summary>
    /// 新订阅ID
    /// </summary>
    public Guid? NewSubscriptionId { get; set; }

    /// <summary>
    /// 错误消息
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// 升级详情
    /// </summary>
    public UpgradeDetails? Details { get; set; }
}

/// <summary>
/// 升级详情
/// </summary>
public class UpgradeDetails
{
    /// <summary>
    /// 原计划名称
    /// </summary>
    public string OldPlanName { get; set; } = string.Empty;

    /// <summary>
    /// 新计划名称
    /// </summary>
    public string NewPlanName { get; set; } = string.Empty;

    /// <summary>
    /// 价格差异
    /// </summary>
    public decimal PriceDifference { get; set; }

    /// <summary>
    /// 配额变化
    /// </summary>
    public Dictionary<string, QuotaChange> QuotaChanges { get; set; } = new();

    /// <summary>
    /// 生效时间
    /// </summary>
    public DateTime EffectiveDate { get; set; }
}

/// <summary>
/// 配额变化
/// </summary>
public class QuotaChange
{
    /// <summary>
    /// 原配额
    /// </summary>
    public long OldLimit { get; set; }

    /// <summary>
    /// 新配额
    /// </summary>
    public long NewLimit { get; set; }

    /// <summary>
    /// 变化量
    /// </summary>
    public long Change { get; set; }

    /// <summary>
    /// 变化百分比
    /// </summary>
    public double ChangePercentage { get; set; }
}

/// <summary>
/// 趋势方向
/// </summary>
public enum TrendDirection
{
    /// <summary>
    /// 稳定
    /// </summary>
    Stable = 0,

    /// <summary>
    /// 上升
    /// </summary>
    Increasing = 1,

    /// <summary>
    /// 下降
    /// </summary>
    Decreasing = -1
}
