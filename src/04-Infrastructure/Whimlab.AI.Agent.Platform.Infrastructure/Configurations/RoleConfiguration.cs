using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Whimlab.AI.Agent.Platform.Domain.Identity.Entities;

namespace Whimlab.AI.Agent.Platform.Infrastructure.Configurations;

/// <summary>
/// 角色实体配置
/// </summary>
public class RoleConfiguration : IEntityTypeConfiguration<Role>
{
    /// <summary>
    /// 配置角色实体
    /// </summary>
    /// <param name="builder">实体类型构建器</param>
    public void Configure(EntityTypeBuilder<Role> builder)
    {
        // 表名
        builder.ToTable("Roles", "Identity");

        // 主键
        builder.HasKey(r => r.Id);

        // 属性配置
        builder.Property(r => r.Id)
            .IsRequired()
            .HasComment("角色ID");

        builder.Property(r => r.Name)
            .IsRequired()
            .HasMaxLength(50)
            .HasComment("角色名称");

        builder.Property(r => r.DisplayName)
            .IsRequired()
            .HasMaxLength(100)
            .HasComment("显示名称");

        builder.Property(r => r.Description)
            .HasMaxLength(500)
            .HasComment("角色描述");

        builder.Property(r => r.IsSystemRole)
            .IsRequired()
            .HasDefaultValue(false)
            .HasComment("是否系统角色");

        builder.Property(r => r.IsActive)
            .IsRequired()
            .HasDefaultValue(true)
            .HasComment("是否激活");

        builder.Property(r => r.CreatedAt)
            .IsRequired()
            .HasDefaultValueSql("GETUTCDATE()")
            .HasComment("创建时间");

        builder.Property(r => r.UpdatedAt)
            .HasComment("更新时间");

        // 索引
        builder.HasIndex(r => r.Name)
            .IsUnique()
            .HasDatabaseName("IX_Roles_Name");

        builder.HasIndex(r => r.IsSystemRole)
            .HasDatabaseName("IX_Roles_IsSystemRole");

        builder.HasIndex(r => r.IsActive)
            .HasDatabaseName("IX_Roles_IsActive");

        // 关系配置
        builder.HasMany(r => r.UserRoles)
            .WithOne(ur => ur.Role)
            .HasForeignKey(ur => ur.RoleId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasMany(r => r.RolePermissions)
            .WithOne(rp => rp.Role)
            .HasForeignKey(rp => rp.RoleId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}
