using Microsoft.Extensions.Logging;
using Whimlab.AI.Agent.Platform.Domain.Subscription.Entities;
using Whimlab.AI.Agent.Platform.Domain.Subscription.Repositories;
using Whimlab.AI.Agent.Platform.Domain.Subscription.Services;
using Whimlab.AI.Agent.Platform.Shared.Common.Enums;

namespace Whimlab.AI.Agent.Platform.Application.Subscription.Services;

/// <summary>
/// 配额管理服务实现
/// 负责配额检查、使用、重置等核心业务逻辑
/// </summary>
public class QuotaManagementService : IQuotaManagementService
{
    private readonly ICustomerSubscriptionRepository _subscriptionRepository;
    private readonly ISubscriptionPlanRepository _planRepository;
    private readonly ILogger<QuotaManagementService> _logger;

    /// <summary>
    /// 配额警告阈值常量
    /// </summary>
    private const double DefaultWarningThreshold = 0.8; // 80%
    private const double CriticalWarningThreshold = 0.95; // 95%

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="subscriptionRepository">客户订阅仓储</param>
    /// <param name="planRepository">订阅计划仓储</param>
    /// <param name="logger">日志记录器</param>
    public QuotaManagementService(
        ICustomerSubscriptionRepository subscriptionRepository,
        ISubscriptionPlanRepository planRepository,
        ILogger<QuotaManagementService> logger)
    {
        _subscriptionRepository = subscriptionRepository ?? throw new ArgumentNullException(nameof(subscriptionRepository));
        _planRepository = planRepository ?? throw new ArgumentNullException(nameof(planRepository));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// 检查配额是否足够
    /// </summary>
    /// <param name="customerId">客户ID</param>
    /// <param name="quotaType">配额类型</param>
    /// <param name="requestedAmount">请求使用量</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>配额检查结果</returns>
    public async Task<QuotaCheckResult> CheckQuotaAsync(Guid customerId, string quotaType, long requestedAmount, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("开始检查客户 {CustomerId} 的配额 {QuotaType}，请求使用量: {RequestedAmount}", 
                customerId, quotaType, requestedAmount);

            // 获取客户的活跃订阅
            var subscription = await _subscriptionRepository.GetActiveSubscriptionByCustomerIdAsync(customerId, cancellationToken);
            if (subscription == null)
            {
                _logger.LogWarning("客户 {CustomerId} 没有活跃的订阅", customerId);
                return new QuotaCheckResult
                {
                    IsAllowed = false,
                    DenialReason = "客户没有活跃的订阅",
                    SuggestedActions = { "请先购买订阅计划" }
                };
            }

            // 获取订阅计划
            var plan = await _planRepository.GetByIdAsync(subscription.SubscriptionPlanId, cancellationToken);
            if (plan == null)
            {
                _logger.LogError("找不到订阅计划 {PlanId}", subscription.SubscriptionPlanId);
                return new QuotaCheckResult
                {
                    IsAllowed = false,
                    DenialReason = "订阅计划不存在",
                    SuggestedActions = { "请联系客服" }
                };
            }

            // 获取配额限制
            var quotaLimit = plan.Quotas.FirstOrDefault(q => q.QuotaType == quotaType);
            if (quotaLimit == null)
            {
                _logger.LogWarning("订阅计划 {PlanId} 中没有配额类型 {QuotaType}", plan.Id, quotaType);
                return new QuotaCheckResult
                {
                    IsAllowed = false,
                    DenialReason = $"当前订阅计划不支持 {quotaType} 配额",
                    SuggestedActions = { "请升级到支持此功能的计划" }
                };
            }

            // 检查是否无限制配额
            if (quotaLimit.Limit == 0) // 0表示无限制
            {
                return new QuotaCheckResult
                {
                    IsAllowed = true,
                    CurrentUsage = 0,
                    QuotaLimit = long.MaxValue,
                    RemainingQuota = long.MaxValue
                };
            }

            // 计算当前使用量
            var currentUsage = subscription.GetCurrentQuotaUsage(quotaType);
            var remainingQuota = quotaLimit.Limit - currentUsage;

            // 检查是否有足够的配额
            var isAllowed = remainingQuota >= requestedAmount;
            var result = new QuotaCheckResult
            {
                IsAllowed = isAllowed,
                CurrentUsage = currentUsage,
                QuotaLimit = quotaLimit.Limit,
                RemainingQuota = remainingQuota
            };

            if (!isAllowed)
            {
                result.DenialReason = $"配额不足，当前剩余: {remainingQuota}，请求: {requestedAmount}";
                result.SuggestedActions.Add("请等待配额重置或升级订阅计划");
                
                // 如果接近配额限制，建议升级
                if (currentUsage > quotaLimit.Limit * 0.9)
                {
                    result.SuggestedActions.Add("建议升级到更高级的订阅计划");
                }
            }
            else
            {
                // 检查是否需要警告
                var usageAfterRequest = currentUsage + requestedAmount;
                var usageRate = (double)usageAfterRequest / quotaLimit.Limit;
                
                if (usageRate >= CriticalWarningThreshold)
                {
                    result.SuggestedActions.Add("配额即将耗尽，建议升级订阅计划");
                }
                else if (usageRate >= DefaultWarningThreshold)
                {
                    result.SuggestedActions.Add("配额使用量较高，请注意监控");
                }
            }

            _logger.LogInformation("配额检查完成，客户 {CustomerId}，配额类型 {QuotaType}，结果: {IsAllowed}", 
                customerId, quotaType, isAllowed);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "检查配额时发生错误，客户 {CustomerId}，配额类型 {QuotaType}", customerId, quotaType);
            return new QuotaCheckResult
            {
                IsAllowed = false,
                DenialReason = "系统错误，请稍后重试",
                SuggestedActions = { "请联系技术支持" }
            };
        }
    }

    /// <summary>
    /// 使用配额
    /// </summary>
    /// <param name="customerId">客户ID</param>
    /// <param name="quotaType">配额类型</param>
    /// <param name="amount">使用量</param>
    /// <param name="description">使用描述</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>配额使用结果</returns>
    public async Task<QuotaUsageResult> UseQuotaAsync(Guid customerId, string quotaType, long amount, string? description = null, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("开始使用配额，客户 {CustomerId}，配额类型 {QuotaType}，使用量: {Amount}", 
                customerId, quotaType, amount);

            // 先检查配额是否足够
            var checkResult = await CheckQuotaAsync(customerId, quotaType, amount, cancellationToken);
            if (!checkResult.IsAllowed)
            {
                return new QuotaUsageResult
                {
                    IsSuccess = false,
                    ErrorMessage = checkResult.DenialReason,
                    RemainingQuota = checkResult.RemainingQuota
                };
            }

            // 获取客户订阅
            var subscription = await _subscriptionRepository.GetActiveSubscriptionByCustomerIdAsync(customerId, cancellationToken);
            if (subscription == null)
            {
                return new QuotaUsageResult
                {
                    IsSuccess = false,
                    ErrorMessage = "客户没有活跃的订阅"
                };
            }

            // 使用配额
            var success = subscription.UseQuota(quotaType, amount, description);
            if (!success)
            {
                return new QuotaUsageResult
                {
                    IsSuccess = false,
                    ErrorMessage = "配额使用失败"
                };
            }

            // 保存更改
            await _subscriptionRepository.UpdateAsync(subscription, cancellationToken);

            // 计算新的累计使用量和剩余配额
            var newCumulativeUsage = subscription.GetCurrentQuotaUsage(quotaType);
            var newRemainingQuota = checkResult.QuotaLimit - newCumulativeUsage;

            // 检查是否需要警告
            var usageRate = (double)newCumulativeUsage / checkResult.QuotaLimit;
            var result = new QuotaUsageResult
            {
                IsSuccess = true,
                NewCumulativeUsage = newCumulativeUsage,
                RemainingQuota = newRemainingQuota
            };

            if (usageRate >= CriticalWarningThreshold)
            {
                result.TriggeredWarning = true;
                result.WarningMessage = "配额使用量已达到95%，即将耗尽";
            }
            else if (usageRate >= DefaultWarningThreshold)
            {
                result.TriggeredWarning = true;
                result.WarningMessage = "配额使用量已达到80%，请注意监控";
            }

            _logger.LogInformation("配额使用成功，客户 {CustomerId}，配额类型 {QuotaType}，新累计使用量: {NewUsage}", 
                customerId, quotaType, newCumulativeUsage);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "使用配额时发生错误，客户 {CustomerId}，配额类型 {QuotaType}", customerId, quotaType);
            return new QuotaUsageResult
            {
                IsSuccess = false,
                ErrorMessage = "系统错误，请稍后重试"
            };
        }
    }

    /// <summary>
    /// 获取客户的配额状态
    /// </summary>
    /// <param name="customerId">客户ID</param>
    /// <param name="quotaType">配额类型（可选）</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>配额状态</returns>
    public async Task<CustomerQuotaStatus> GetQuotaStatusAsync(Guid customerId, string? quotaType = null, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("获取客户 {CustomerId} 的配额状态", customerId);

            // 获取客户的活跃订阅
            var subscription = await _subscriptionRepository.GetActiveSubscriptionByCustomerIdAsync(customerId, cancellationToken);
            if (subscription == null)
            {
                throw new InvalidOperationException($"客户 {customerId} 没有活跃的订阅");
            }

            // 获取订阅计划
            var plan = await _planRepository.GetByIdAsync(subscription.SubscriptionPlanId, cancellationToken);
            if (plan == null)
            {
                throw new InvalidOperationException($"找不到订阅计划 {subscription.SubscriptionPlanId}");
            }

            var quotaStatus = new CustomerQuotaStatus
            {
                CustomerId = customerId,
                SubscriptionPlanId = subscription.SubscriptionPlanId,
                CurrentPeriodStart = subscription.CurrentPeriodStart,
                CurrentPeriodEnd = subscription.CurrentPeriodEnd,
                SubscriptionStatus = subscription.Status.ToString()
            };

            // 获取配额详情
            var quotasToProcess = string.IsNullOrEmpty(quotaType) 
                ? plan.Quotas 
                : plan.Quotas.Where(q => q.QuotaType == quotaType);

            foreach (var planQuota in quotasToProcess)
            {
                var currentUsage = subscription.GetCurrentQuotaUsage(planQuota.QuotaType);
                var isUnlimited = planQuota.Limit == 0;
                var remainingQuota = isUnlimited ? long.MaxValue : planQuota.Limit - currentUsage;
                var usageRate = isUnlimited ? 0 : (double)currentUsage / planQuota.Limit;

                var quotaDetail = new QuotaDetail
                {
                    QuotaType = planQuota.QuotaType,
                    Limit = isUnlimited ? long.MaxValue : planQuota.Limit,
                    CurrentUsage = currentUsage,
                    RemainingQuota = remainingQuota,
                    UsageRate = usageRate,
                    Period = planQuota.Period,
                    IsUnlimited = isUnlimited,
                    NextResetAt = CalculateNextResetTime(subscription.CurrentPeriodStart, planQuota.Period)
                };

                quotaStatus.QuotaDetails.Add(quotaDetail);
            }

            return quotaStatus;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取配额状态时发生错误，客户 {CustomerId}", customerId);
            throw;
        }
    }

    /// <summary>
    /// 计算下次重置时间
    /// </summary>
    /// <param name="periodStart">周期开始时间</param>
    /// <param name="period">重置周期</param>
    /// <returns>下次重置时间</returns>
    private static DateTime? CalculateNextResetTime(DateTime periodStart, string period)
    {
        return period.ToLowerInvariant() switch
        {
            "daily" => periodStart.AddDays(1),
            "weekly" => periodStart.AddDays(7),
            "monthly" => periodStart.AddMonths(1),
            "yearly" => periodStart.AddYears(1),
            _ => null
        };
    }
}
