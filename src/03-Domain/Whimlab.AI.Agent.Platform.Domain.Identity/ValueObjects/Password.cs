using System.Security.Cryptography;
using System.Text;
using Whimlab.AI.Agent.Platform.Domain.Core.Common;
using Whimlab.AI.Agent.Platform.Shared.Common.Extensions;
using Whimlab.AI.Agent.Platform.Shared.Common.Helpers;

namespace Whimlab.AI.Agent.Platform.Domain.Identity.ValueObjects;

/// <summary>
/// 密码值对象
/// </summary>
public sealed class Password : ValueObject
{
    /// <summary>
    /// 密码哈希值
    /// </summary>
    public string Hash { get; }

    /// <summary>
    /// 密码盐值
    /// </summary>
    public string Salt { get; }

    /// <summary>
    /// 密码强度分数
    /// </summary>
    public int StrengthScore { get; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedAt { get; }

    /// <summary>
    /// 初始化密码
    /// </summary>
    /// <param name="hash">密码哈希值</param>
    /// <param name="salt">密码盐值</param>
    /// <param name="strengthScore">密码强度分数</param>
    /// <param name="createdAt">创建时间</param>
    private Password(string hash, string salt, int strengthScore, DateTime createdAt)
    {
        Hash = hash;
        Salt = salt;
        StrengthScore = strengthScore;
        CreatedAt = createdAt;
    }

    /// <summary>
    /// 从明文密码创建
    /// </summary>
    /// <param name="plainPassword">明文密码</param>
    /// <returns>密码值对象</returns>
    /// <exception cref="ArgumentException">密码格式无效</exception>
    public static Password Create(string plainPassword)
    {
        if (plainPassword.IsNullOrWhiteSpace())
            throw new ArgumentException("密码不能为空", nameof(plainPassword));

        if (plainPassword.Length < 8)
            throw new ArgumentException("密码长度不能少于8位", nameof(plainPassword));

        if (plainPassword.Length > 128)
            throw new ArgumentException("密码长度不能超过128位", nameof(plainPassword));

        if (!plainPassword.IsStrongPassword())
            throw new ArgumentException("密码强度不足，必须包含大小写字母、数字和特殊字符", nameof(plainPassword));

        var salt = GenerateSalt();
        var hash = HashPassword(plainPassword, salt);
        var strengthScore = SecurityHelper.CalculatePasswordStrength(plainPassword);

        return new Password(hash, salt, strengthScore, DateTime.UtcNow);
    }

    /// <summary>
    /// 从已有的哈希值和盐值创建
    /// </summary>
    /// <param name="hash">密码哈希值</param>
    /// <param name="salt">密码盐值</param>
    /// <param name="strengthScore">密码强度分数</param>
    /// <param name="createdAt">创建时间</param>
    /// <returns>密码值对象</returns>
    public static Password FromHash(string hash, string salt, int strengthScore, DateTime createdAt)
    {
        if (hash.IsNullOrWhiteSpace())
            throw new ArgumentException("密码哈希值不能为空", nameof(hash));

        if (salt.IsNullOrWhiteSpace())
            throw new ArgumentException("密码盐值不能为空", nameof(salt));

        return new Password(hash, salt, strengthScore, createdAt);
    }

    /// <summary>
    /// 验证密码
    /// </summary>
    /// <param name="plainPassword">明文密码</param>
    /// <returns>是否验证成功</returns>
    public bool Verify(string plainPassword)
    {
        if (plainPassword.IsNullOrWhiteSpace())
            return false;

        var hash = HashPassword(plainPassword, Salt);
        return string.Equals(Hash, hash, StringComparison.Ordinal);
    }

    /// <summary>
    /// 检查密码是否需要更新（基于创建时间）
    /// </summary>
    /// <param name="maxAge">最大有效期</param>
    /// <returns>是否需要更新</returns>
    public bool RequiresUpdate(TimeSpan maxAge)
    {
        return DateTime.UtcNow - CreatedAt > maxAge;
    }

    /// <summary>
    /// 检查密码强度是否足够
    /// </summary>
    /// <param name="minimumScore">最低强度分数</param>
    /// <returns>强度是否足够</returns>
    public bool IsStrongEnough(int minimumScore = 80)
    {
        return StrengthScore >= minimumScore;
    }

    /// <summary>
    /// 获取密码强度等级
    /// </summary>
    /// <returns>强度等级</returns>
    public string GetStrengthLevel()
    {
        return StrengthScore switch
        {
            >= 90 => "非常强",
            >= 80 => "强",
            >= 60 => "中等",
            >= 40 => "弱",
            _ => "非常弱"
        };
    }

    /// <summary>
    /// 生成密码盐值
    /// </summary>
    /// <returns>盐值</returns>
    private static string GenerateSalt()
    {
        using var rng = RandomNumberGenerator.Create();
        var saltBytes = new byte[32];
        rng.GetBytes(saltBytes);
        return Convert.ToBase64String(saltBytes);
    }

    /// <summary>
    /// 计算密码哈希值
    /// </summary>
    /// <param name="password">明文密码</param>
    /// <param name="salt">盐值</param>
    /// <returns>哈希值</returns>
    private static string HashPassword(string password, string salt)
    {
        using var pbkdf2 = new Rfc2898DeriveBytes(
            password,
            Convert.FromBase64String(salt),
            100000, // 迭代次数
            HashAlgorithmName.SHA256);

        var hashBytes = pbkdf2.GetBytes(32);
        return Convert.ToBase64String(hashBytes);
    }

    /// <summary>
    /// 获取相等性组件
    /// </summary>
    /// <returns>相等性组件集合</returns>
    protected override IEnumerable<object?> GetEqualityComponents()
    {
        yield return Hash;
        yield return Salt;
    }

    /// <summary>
    /// 转换为字符串
    /// </summary>
    /// <returns>字符串表示</returns>
    public override string ToString()
    {
        return $"Password(Strength: {GetStrengthLevel()}, Created: {CreatedAt:yyyy-MM-dd})";
    }
}
