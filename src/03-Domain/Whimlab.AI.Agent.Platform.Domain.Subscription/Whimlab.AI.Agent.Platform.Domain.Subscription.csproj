<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <RootNamespace>Whimlab.AI.Agent.Platform.Domain.Subscription</RootNamespace>
    <AssemblyName>Whimlab.AI.Agent.Platform.Domain.Subscription</AssemblyName>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="../Whimlab.AI.Agent.Platform.Domain.Core/Whimlab.AI.Agent.Platform.Domain.Core.csproj" />
    <ProjectReference Include="../Whimlab.AI.Agent.Platform.Domain.Identity/Whimlab.AI.Agent.Platform.Domain.Identity.csproj" />
  </ItemGroup>

</Project>
