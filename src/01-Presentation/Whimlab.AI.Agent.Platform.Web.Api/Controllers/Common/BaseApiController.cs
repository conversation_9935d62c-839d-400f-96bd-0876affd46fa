using System.Security.Claims;
using AutoMapper;
using MediatR;
using Microsoft.AspNetCore.Mvc;
using Whimlab.AI.Agent.Platform.Web.Api.Models.Responses;
using Whimlab.AI.Agent.Platform.Application.Core.Common;
using Whimlab.AI.Agent.Platform.Domain.Core.Common;

namespace Whimlab.AI.Agent.Platform.Web.Api.Controllers.Common;

/// <summary>
/// API控制器基类
/// 提供通用的功能和响应处理
/// </summary>
[ApiController]
[Route("api/v{version:apiVersion}/[controller]")]
[Produces("application/json")]
public abstract class BaseApiController : ControllerBase
{
    private IMediator? _mediator;
    private IMapper? _mapper;

    /// <summary>
    /// MediatR实例
    /// </summary>
    protected IMediator Mediator => _mediator ??= HttpContext.RequestServices.GetRequiredService<IMediator>();

    /// <summary>
    /// AutoMapper实例
    /// </summary>
    protected IMapper Mapper => _mapper ??= HttpContext.RequestServices.GetRequiredService<IMapper>();

    /// <summary>
    /// 当前用户ID
    /// </summary>
    protected Guid? CurrentUserId
    {
        get
        {
            var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            return Guid.TryParse(userIdClaim, out var userId) ? userId : null;
        }
    }

    /// <summary>
    /// 当前用户邮箱
    /// </summary>
    protected string? CurrentUserEmail => User.FindFirst(ClaimTypes.Email)?.Value;

    /// <summary>
    /// 当前用户显示名称
    /// </summary>
    protected string? CurrentUserName => User.FindFirst(ClaimTypes.Name)?.Value;

    /// <summary>
    /// 检查当前用户是否具有指定权限
    /// </summary>
    /// <param name="permission">权限名称</param>
    /// <returns>是否具有权限</returns>
    protected bool HasPermission(string permission)
    {
        return User.Claims.Any(c => c.Type == "permission" && c.Value == permission) ||
               User.IsInRole("SuperAdmin");
    }

    /// <summary>
    /// 检查当前用户是否具有指定角色
    /// </summary>
    /// <param name="role">角色名称</param>
    /// <returns>是否具有角色</returns>
    protected bool HasRole(string role)
    {
        return User.IsInRole(role);
    }

    /// <summary>
    /// 创建成功响应
    /// </summary>
    /// <typeparam name="T">数据类型</typeparam>
    /// <param name="data">响应数据</param>
    /// <param name="message">成功消息</param>
    /// <returns>成功响应</returns>
    protected ActionResult<ApiResponse<T>> Success<T>(T data, string? message = null)
    {
        var response = ApiResponse<T>.CreateSuccess(data, message);
        response.TraceId = HttpContext.TraceIdentifier;
        return Ok(response);
    }

    /// <summary>
    /// 创建成功响应（无数据）
    /// </summary>
    /// <param name="message">成功消息</param>
    /// <returns>成功响应</returns>
    protected ActionResult<ApiResponse> Success(string? message = null)
    {
        var response = ApiResponse.CreateSuccess(message);
        response.TraceId = HttpContext.TraceIdentifier;
        return Ok(response);
    }

    /// <summary>
    /// 创建分页成功响应
    /// </summary>
    /// <typeparam name="T">数据类型</typeparam>
    /// <param name="data">响应数据</param>
    /// <param name="pagination">分页信息</param>
    /// <param name="message">成功消息</param>
    /// <returns>分页成功响应</returns>
    protected ActionResult<PagedApiResponse<T>> PagedSuccess<T>(
        IEnumerable<T> data, 
        PaginationMetadata pagination, 
        string? message = null)
    {
        var response = PagedApiResponse<T>.Success(data, pagination, message);
        response.TraceId = HttpContext.TraceIdentifier;
        return Ok(response);
    }

    /// <summary>
    /// 根据Result创建响应
    /// </summary>
    /// <typeparam name="T">数据类型</typeparam>
    /// <param name="result">操作结果</param>
    /// <param name="successMessage">成功消息</param>
    /// <returns>API响应</returns>
    protected ActionResult<ApiResponse<T>> HandleResult<T>(Result<T?> result, string? successMessage = null) where T : class
    {
        if (result.IsSuccess)
        {
            return Success(result.Value, successMessage);
        }

        return HandleFailure<T>(result);
    }

    /// <summary>
    /// 根据Result创建响应（无数据）
    /// </summary>
    /// <param name="result">操作结果</param>
    /// <param name="successMessage">成功消息</param>
    /// <returns>API响应</returns>
    protected ActionResult<ApiResponse> HandleResult(Result result, string? successMessage = null)
    {
        if (result.IsSuccess)
        {
            return Success(successMessage);
        }

        return BadRequest(ApiResponse.CreateFailure(result.Error ?? "操作失败", result.ErrorCode));
    }

    /// <summary>
    /// 根据分页Result创建响应
    /// </summary>
    /// <typeparam name="T">数据类型</typeparam>
    /// <param name="result">分页操作结果</param>
    /// <param name="successMessage">成功消息</param>
    /// <returns>分页API响应</returns>
    protected ActionResult<PagedApiResponse<T>> HandlePagedResult<T>(
        Result<PagedResult<T>> result, 
        string? successMessage = null)
    {
        if (result.IsSuccess)
        {
            var pagedResult = result.Value!;
            var pagination = PaginationMetadata.Create(
                pagedResult.PageNumber,
                pagedResult.PageSize,
                pagedResult.TotalCount);

            return PagedSuccess(pagedResult.Items, pagination, successMessage);
        }

        return HandlePagedFailure<T>(result);
    }

    /// <summary>
    /// 处理失败结果
    /// </summary>
    /// <typeparam name="T">数据类型</typeparam>
    /// <param name="result">失败结果</param>
    /// <returns>错误响应</returns>
    private ActionResult<ApiResponse<T>> HandleFailure<T>(Result result)
    {
        var response = ApiResponse<T>.CreateFailure(result.Error ?? "操作失败", result.ErrorCode);
        response.TraceId = HttpContext.TraceIdentifier;

        return result.ErrorCode switch
        {
            "NOT_FOUND" => NotFound(response),
            "UNAUTHORIZED" => Unauthorized(response),
            "FORBIDDEN" => Forbid(),
            "VALIDATION_ERROR" => BadRequest(response),
            "CONFLICT" => Conflict(response),
            _ => BadRequest(response)
        };
    }

    /// <summary>
    /// 处理分页失败结果
    /// </summary>
    /// <typeparam name="T">数据类型</typeparam>
    /// <param name="result">失败结果</param>
    /// <returns>分页错误响应</returns>
    private ActionResult<PagedApiResponse<T>> HandlePagedFailure<T>(Result result)
    {
        var response = PagedApiResponse<T>.Failure(result.Error ?? "操作失败", result.ErrorCode);
        response.TraceId = HttpContext.TraceIdentifier;

        return result.ErrorCode switch
        {
            "NOT_FOUND" => NotFound(response),
            "UNAUTHORIZED" => Unauthorized(response),
            "FORBIDDEN" => Forbid(),
            "VALIDATION_ERROR" => BadRequest(response),
            "CONFLICT" => Conflict(response),
            _ => BadRequest(response)
        };
    }

    /// <summary>
    /// 验证模型状态
    /// </summary>
    /// <returns>验证结果</returns>
    protected ActionResult<ApiResponse>? ValidateModelState()
    {
        if (!ModelState.IsValid)
        {
            var errors = ModelState.Values
                .SelectMany(v => v.Errors)
                .Select(e => e.ErrorMessage)
                .ToList();

            var response = ApiResponse.CreateFailure("请求数据验证失败", "VALIDATION_ERROR", errors);
            response.TraceId = HttpContext.TraceIdentifier;

            return BadRequest(response);
        }

        return null;
    }
}
