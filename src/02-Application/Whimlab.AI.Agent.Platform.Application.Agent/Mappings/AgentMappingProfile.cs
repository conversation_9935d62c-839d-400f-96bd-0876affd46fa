using AutoMapper;
using Whimlab.AI.Agent.Platform.Application.Agent.DTOs;
using Whimlab.AI.Agent.Platform.Domain.Agent.Entities;
using Whimlab.AI.Agent.Platform.Domain.Agent.ValueObjects;

namespace Whimlab.AI.Agent.Platform.Application.Agent.Mappings;

/// <summary>
/// 智能体模块映射配置
/// </summary>
public class AgentMappingProfile : Profile
{
    /// <summary>
    /// 初始化映射配置
    /// </summary>
    public AgentMappingProfile()
    {
        CreateAgentMappings();
        CreateAgentVersionMappings();
        CreateConfigurationMappings();
    }

    /// <summary>
    /// 创建智能体映射
    /// </summary>
    private void CreateAgentMappings()
    {
        // Agent -> AgentDto
        CreateMap<Domain.Agent.Entities.Agent, AgentDto>()
            .ForMember(dest => dest.Configuration, opt => opt.MapFrom(src => src.Configuration))
            .ForMember(dest => dest.Tags, opt => opt.MapFrom(src => src.Tags.Select(t => t.Name)))
            .ForMember(dest => dest.Versions, opt => opt.MapFrom(src => src.Versions));

        // Agent -> AgentSummaryDto
        CreateMap<Domain.Agent.Entities.Agent, AgentSummaryDto>()
            .ForMember(dest => dest.Tags, opt => opt.MapFrom(src => src.Tags.Select(t => t.Name)));

        // CreateAgentCommand -> Agent (需要在处理器中手动处理)
        // UpdateAgentCommand -> Agent (需要在处理器中手动处理)
    }

    /// <summary>
    /// 创建智能体版本映射
    /// </summary>
    private void CreateAgentVersionMappings()
    {
        // AgentVersion -> AgentVersionDto
        CreateMap<AgentVersion, AgentVersionDto>()
            .ForMember(dest => dest.Configuration, opt => opt.MapFrom(src => src.Configuration));
    }

    /// <summary>
    /// 创建配置映射
    /// </summary>
    private void CreateConfigurationMappings()
    {
        // AgentConfiguration -> AgentConfigurationDto
        CreateMap<AgentConfiguration, AgentConfigurationDto>()
            .ForMember(dest => dest.ModelConfig, opt => opt.MapFrom(src => src.ModelConfig))
            .ForMember(dest => dest.ToolConfigs, opt => opt.MapFrom(src => src.Tools))
            .ForMember(dest => dest.KnowledgeBaseConfig, opt => opt.MapFrom(src => new KnowledgeBaseConfigDto())); // 默认空配置

        // ModelConfiguration -> ModelConfigurationDto
        CreateMap<ModelConfiguration, ModelConfigurationDto>();

        // ToolConfiguration -> ToolConfigurationDto
        CreateMap<ToolConfiguration, ToolConfigurationDto>();

        // 反向映射 - DTO to ValueObject (需要在处理器中手动处理，因为AgentConfiguration是不可变的值对象)
        // CreateMap<AgentConfigurationDto, AgentConfiguration>() - 不能直接映射，需要使用Create方法
    }
}
