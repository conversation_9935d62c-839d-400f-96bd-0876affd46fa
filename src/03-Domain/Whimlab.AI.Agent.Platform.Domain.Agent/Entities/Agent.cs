using Whimlab.AI.Agent.Platform.Domain.Core.Common;
using Whimlab.AI.Agent.Platform.Domain.Agent.ValueObjects;
using Whimlab.AI.Agent.Platform.Domain.Agent.Events;
using Whimlab.AI.Agent.Platform.Shared.Common.Enums;

namespace Whimlab.AI.Agent.Platform.Domain.Agent.Entities;

/// <summary>
/// 智能体聚合根
/// </summary>
public class Agent : AggregateRoot
{
    private readonly List<AgentVersion> _versions = new();
    private readonly List<AgentTag> _tags = new();

    /// <summary>
    /// 智能体名称
    /// </summary>
    public string Name { get; private set; } = string.Empty;

    /// <summary>
    /// 智能体描述
    /// </summary>
    public string Description { get; private set; } = string.Empty;

    /// <summary>
    /// 智能体图标URL
    /// </summary>
    public string? IconUrl { get; private set; }

    /// <summary>
    /// 智能体类型
    /// </summary>
    public AgentType AgentType { get; private set; }

    /// <summary>
    /// 智能体状态
    /// </summary>
    public AgentStatus Status { get; private set; }

    /// <summary>
    /// 创建者ID
    /// </summary>
    public Guid CreatedBy { get; private set; }

    /// <summary>
    /// 当前版本号
    /// </summary>
    public string CurrentVersion { get; private set; } = "1.0.0";

    /// <summary>
    /// 当前配置
    /// </summary>
    public AgentConfiguration Configuration { get; private set; } = null!;

    /// <summary>
    /// 是否公开
    /// </summary>
    public bool IsPublic { get; private set; }

    /// <summary>
    /// 分类
    /// </summary>
    public string? Category { get; private set; }

    /// <summary>
    /// 使用次数
    /// </summary>
    public long UsageCount { get; private set; }

    /// <summary>
    /// 评分
    /// </summary>
    public double Rating { get; private set; }

    /// <summary>
    /// 评分次数
    /// </summary>
    public int RatingCount { get; private set; }

    /// <summary>
    /// 最后使用时间
    /// </summary>
    public DateTime? LastUsedAt { get; private set; }

    /// <summary>
    /// 发布时间
    /// </summary>
    public DateTime? PublishedAt { get; private set; }

    /// <summary>
    /// 归档时间
    /// </summary>
    public DateTime? ArchivedAt { get; private set; }

    /// <summary>
    /// 版本列表（只读）
    /// </summary>
    public IReadOnlyList<AgentVersion> Versions => _versions.AsReadOnly();

    /// <summary>
    /// 标签列表（只读）
    /// </summary>
    public IReadOnlyList<AgentTag> Tags => _tags.AsReadOnly();

    /// <summary>
    /// 私有构造函数（用于EF Core）
    /// </summary>
    private Agent() { }

    /// <summary>
    /// 创建新智能体
    /// </summary>
    /// <param name="name">智能体名称</param>
    /// <param name="description">智能体描述</param>
    /// <param name="agentType">智能体类型</param>
    /// <param name="configuration">智能体配置</param>
    /// <param name="createdBy">创建者ID</param>
    /// <param name="iconUrl">图标URL</param>
    /// <param name="category">分类</param>
    /// <param name="isPublic">是否公开</param>
    /// <returns>智能体实体</returns>
    public static Agent Create(
        string name,
        string description,
        AgentType agentType,
        AgentConfiguration configuration,
        Guid createdBy,
        string? iconUrl = null,
        string? category = null,
        bool isPublic = false)
    {
        if (string.IsNullOrWhiteSpace(name))
            throw new ArgumentException("智能体名称不能为空", nameof(name));

        if (string.IsNullOrWhiteSpace(description))
            throw new ArgumentException("智能体描述不能为空", nameof(description));

        if (createdBy == Guid.Empty)
            throw new ArgumentException("创建者ID不能为空", nameof(createdBy));

        var agent = new Agent
        {
            Name = name.Trim(),
            Description = description.Trim(),
            AgentType = agentType,
            Configuration = configuration,
            CreatedBy = createdBy,
            IconUrl = iconUrl?.Trim(),
            Category = category?.Trim(),
            IsPublic = isPublic,
            Status = AgentStatus.Draft,
            CurrentVersion = "1.0.0",
            UsageCount = 0,
            Rating = 0.0,
            RatingCount = 0
        };

        // 创建初始版本
        var initialVersion = AgentVersion.Create(agent.Id, "1.0.0", configuration, "初始版本", createdBy);
        agent._versions.Add(initialVersion);

        agent.AddDomainEvent(new AgentCreatedEvent(agent.Id, agent.Name, agent.AgentType, createdBy));

        return agent;
    }

    /// <summary>
    /// 更新智能体信息
    /// </summary>
    /// <param name="name">智能体名称</param>
    /// <param name="description">智能体描述</param>
    /// <param name="iconUrl">图标URL</param>
    /// <param name="category">分类</param>
    /// <param name="isPublic">是否公开</param>
    public void UpdateInfo(string name, string description, string? iconUrl = null, string? category = null, bool? isPublic = null)
    {
        if (Status == AgentStatus.Archived)
            throw new InvalidOperationException("已归档的智能体不能修改");

        Name = name.Trim();
        Description = description.Trim();
        IconUrl = iconUrl?.Trim();
        Category = category?.Trim();
        
        if (isPublic.HasValue)
            IsPublic = isPublic.Value;

        MarkAsModified();

        AddDomainEvent(new AgentUpdatedEvent(Id, Name, Description));
    }

    /// <summary>
    /// 更新配置
    /// </summary>
    /// <param name="configuration">新配置</param>
    /// <param name="versionNotes">版本说明</param>
    /// <param name="updatedBy">更新者ID</param>
    public void UpdateConfiguration(AgentConfiguration configuration, string? versionNotes = null, Guid? updatedBy = null)
    {
        if (Status == AgentStatus.Archived)
            throw new InvalidOperationException("已归档的智能体不能修改配置");

        Configuration = configuration;
        
        // 创建新版本
        var newVersion = GetNextVersion();
        var version = AgentVersion.Create(Id, newVersion, configuration, versionNotes ?? "配置更新", updatedBy ?? CreatedBy);
        _versions.Add(version);
        
        CurrentVersion = newVersion;
        MarkAsModified();

        AddDomainEvent(new AgentConfigurationUpdatedEvent(Id, newVersion));
    }

    /// <summary>
    /// 发布智能体
    /// </summary>
    public void Publish()
    {
        if (Status == AgentStatus.Published)
            return;

        if (Status == AgentStatus.Archived)
            throw new InvalidOperationException("已归档的智能体不能发布");

        Status = AgentStatus.Published;
        PublishedAt = DateTime.UtcNow;
        MarkAsModified();

        AddDomainEvent(new AgentPublishedEvent(Id, Name));
    }

    /// <summary>
    /// 归档智能体
    /// </summary>
    public void Archive()
    {
        if (Status == AgentStatus.Archived)
            return;

        Status = AgentStatus.Archived;
        ArchivedAt = DateTime.UtcNow;
        MarkAsModified();

        AddDomainEvent(new AgentArchivedEvent(Id, Name));
    }

    /// <summary>
    /// 恢复智能体
    /// </summary>
    public void Restore()
    {
        if (Status != AgentStatus.Archived)
            return;

        Status = AgentStatus.Draft;
        ArchivedAt = null;
        MarkAsModified();

        AddDomainEvent(new AgentRestoredEvent(Id, Name));
    }

    /// <summary>
    /// 记录使用
    /// </summary>
    public void RecordUsage()
    {
        UsageCount++;
        LastUsedAt = DateTime.UtcNow;
        MarkAsModified();

        AddDomainEvent(new AgentUsedEvent(Id, UsageCount));
    }

    /// <summary>
    /// 添加评分
    /// </summary>
    /// <param name="score">评分（1-5）</param>
    public void AddRating(int score)
    {
        if (score < 1 || score > 5)
            throw new ArgumentException("评分必须在1-5之间", nameof(score));

        var totalScore = Rating * RatingCount + score;
        RatingCount++;
        Rating = totalScore / RatingCount;
        MarkAsModified();

        AddDomainEvent(new AgentRatedEvent(Id, score, Rating));
    }

    /// <summary>
    /// 添加标签
    /// </summary>
    /// <param name="tag">标签</param>
    public void AddTag(string tag)
    {
        if (string.IsNullOrWhiteSpace(tag))
            throw new ArgumentException("标签不能为空", nameof(tag));

        var normalizedTag = tag.Trim().ToLowerInvariant();
        
        if (_tags.Any(t => t.Name.Equals(normalizedTag, StringComparison.OrdinalIgnoreCase)))
            return; // 标签已存在

        var agentTag = AgentTag.Create(Id, normalizedTag);
        _tags.Add(agentTag);
        MarkAsModified();
    }

    /// <summary>
    /// 移除标签
    /// </summary>
    /// <param name="tag">标签</param>
    public void RemoveTag(string tag)
    {
        if (string.IsNullOrWhiteSpace(tag))
            return;

        var normalizedTag = tag.Trim().ToLowerInvariant();
        var agentTag = _tags.FirstOrDefault(t => t.Name.Equals(normalizedTag, StringComparison.OrdinalIgnoreCase));
        
        if (agentTag != null)
        {
            _tags.Remove(agentTag);
            MarkAsModified();
        }
    }

    /// <summary>
    /// 获取指定版本的配置
    /// </summary>
    /// <param name="version">版本号</param>
    /// <returns>配置</returns>
    public AgentConfiguration? GetVersionConfiguration(string version)
    {
        return _versions.FirstOrDefault(v => v.Version == version)?.Configuration;
    }

    /// <summary>
    /// 检查是否可以使用
    /// </summary>
    /// <returns>是否可以使用</returns>
    public bool CanBeUsed()
    {
        return Status == AgentStatus.Published && Configuration.ModelConfig.IsComplete();
    }

    /// <summary>
    /// 获取下一个版本号
    /// </summary>
    /// <returns>版本号</returns>
    private string GetNextVersion()
    {
        if (!_versions.Any())
            return "1.0.0";

        var latestVersion = _versions
            .Select(v => System.Version.Parse(v.Version))
            .OrderByDescending(v => v)
            .First();

        return $"{latestVersion.Major}.{latestVersion.Minor}.{latestVersion.Build + 1}";
    }
}
