using Whimlab.AI.Agent.Platform.Shared.Common.Enums;

namespace Whimlab.AI.Agent.Platform.Application.Agent.DTOs;

/// <summary>
/// 智能体DTO
/// </summary>
public class AgentDto
{
    /// <summary>
    /// 智能体ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// 智能体名称
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 智能体描述
    /// </summary>
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// 智能体图标URL
    /// </summary>
    public string? IconUrl { get; set; }

    /// <summary>
    /// 智能体类型
    /// </summary>
    public AgentType AgentType { get; set; }

    /// <summary>
    /// 智能体状态
    /// </summary>
    public AgentStatus Status { get; set; }

    /// <summary>
    /// 创建者ID
    /// </summary>
    public Guid CreatedBy { get; set; }

    /// <summary>
    /// 当前版本号
    /// </summary>
    public string CurrentVersion { get; set; } = string.Empty;

    /// <summary>
    /// 是否公开
    /// </summary>
    public bool IsPublic { get; set; }

    /// <summary>
    /// 分类
    /// </summary>
    public string? Category { get; set; }

    /// <summary>
    /// 使用次数
    /// </summary>
    public long UsageCount { get; set; }

    /// <summary>
    /// 评分
    /// </summary>
    public double Rating { get; set; }

    /// <summary>
    /// 评分次数
    /// </summary>
    public int RatingCount { get; set; }

    /// <summary>
    /// 最后使用时间
    /// </summary>
    public DateTime? LastUsedAt { get; set; }

    /// <summary>
    /// 发布时间
    /// </summary>
    public DateTime? PublishedAt { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedAt { get; set; }

    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime UpdatedAt { get; set; }

    /// <summary>
    /// 配置信息
    /// </summary>
    public AgentConfigurationDto? Configuration { get; set; }

    /// <summary>
    /// 标签列表
    /// </summary>
    public List<string> Tags { get; set; } = new();

    /// <summary>
    /// 版本列表
    /// </summary>
    public List<AgentVersionDto> Versions { get; set; } = new();
}

/// <summary>
/// 智能体简要信息DTO
/// </summary>
public class AgentSummaryDto
{
    /// <summary>
    /// 智能体ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// 智能体名称
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 智能体描述
    /// </summary>
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// 智能体图标URL
    /// </summary>
    public string? IconUrl { get; set; }

    /// <summary>
    /// 智能体类型
    /// </summary>
    public AgentType AgentType { get; set; }

    /// <summary>
    /// 智能体状态
    /// </summary>
    public AgentStatus Status { get; set; }

    /// <summary>
    /// 是否公开
    /// </summary>
    public bool IsPublic { get; set; }

    /// <summary>
    /// 分类
    /// </summary>
    public string? Category { get; set; }

    /// <summary>
    /// 使用次数
    /// </summary>
    public long UsageCount { get; set; }

    /// <summary>
    /// 评分
    /// </summary>
    public double Rating { get; set; }

    /// <summary>
    /// 标签列表
    /// </summary>
    public List<string> Tags { get; set; } = new();
}

/// <summary>
/// 创建智能体DTO
/// </summary>
public class CreateAgentDto
{
    /// <summary>
    /// 智能体名称
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 智能体描述
    /// </summary>
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// 智能体图标URL
    /// </summary>
    public string? IconUrl { get; set; }

    /// <summary>
    /// 智能体类型
    /// </summary>
    public AgentType AgentType { get; set; }

    /// <summary>
    /// 分类
    /// </summary>
    public string? Category { get; set; }

    /// <summary>
    /// 是否公开
    /// </summary>
    public bool IsPublic { get; set; }

    /// <summary>
    /// 配置信息
    /// </summary>
    public AgentConfigurationDto Configuration { get; set; } = new();

    /// <summary>
    /// 标签列表
    /// </summary>
    public List<string> Tags { get; set; } = new();
}

/// <summary>
/// 更新智能体DTO
/// </summary>
public class UpdateAgentDto
{
    /// <summary>
    /// 智能体ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// 智能体名称
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 智能体描述
    /// </summary>
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// 智能体图标URL
    /// </summary>
    public string? IconUrl { get; set; }

    /// <summary>
    /// 分类
    /// </summary>
    public string? Category { get; set; }

    /// <summary>
    /// 是否公开
    /// </summary>
    public bool IsPublic { get; set; }
}

/// <summary>
/// 智能体配置DTO
/// </summary>
public class AgentConfigurationDto
{
    /// <summary>
    /// 系统提示词
    /// </summary>
    public string SystemPrompt { get; set; } = string.Empty;

    /// <summary>
    /// 模型配置
    /// </summary>
    public ModelConfigurationDto ModelConfig { get; set; } = new();

    /// <summary>
    /// 工具配置列表
    /// </summary>
    public List<ToolConfigurationDto> ToolConfigs { get; set; } = new();

    /// <summary>
    /// 知识库配置
    /// </summary>
    public KnowledgeBaseConfigDto? KnowledgeBaseConfig { get; set; }
}

/// <summary>
/// 模型配置DTO
/// </summary>
public class ModelConfigurationDto
{
    /// <summary>
    /// 模型提供商
    /// </summary>
    public string Provider { get; set; } = string.Empty;

    /// <summary>
    /// 模型名称
    /// </summary>
    public string ModelName { get; set; } = string.Empty;

    /// <summary>
    /// API密钥
    /// </summary>
    public string? ApiKey { get; set; }

    /// <summary>
    /// API端点
    /// </summary>
    public string? ApiEndpoint { get; set; }

    /// <summary>
    /// 温度参数
    /// </summary>
    public double Temperature { get; set; } = 0.7;

    /// <summary>
    /// 最大Token数
    /// </summary>
    public int MaxTokens { get; set; } = 2048;

    /// <summary>
    /// Top P参数
    /// </summary>
    public double TopP { get; set; } = 1.0;

    /// <summary>
    /// 频率惩罚
    /// </summary>
    public double FrequencyPenalty { get; set; } = 0.0;

    /// <summary>
    /// 存在惩罚
    /// </summary>
    public double PresencePenalty { get; set; } = 0.0;
}

/// <summary>
/// 工具配置DTO
/// </summary>
public class ToolConfigurationDto
{
    /// <summary>
    /// 工具名称
    /// </summary>
    public string ToolName { get; set; } = string.Empty;

    /// <summary>
    /// 工具类型
    /// </summary>
    public string ToolType { get; set; } = string.Empty;

    /// <summary>
    /// 是否启用
    /// </summary>
    public bool IsEnabled { get; set; } = true;

    /// <summary>
    /// 配置参数（JSON格式）
    /// </summary>
    public string? Parameters { get; set; }
}

/// <summary>
/// 知识库配置DTO
/// </summary>
public class KnowledgeBaseConfigDto
{
    /// <summary>
    /// 知识库ID
    /// </summary>
    public string? KnowledgeBaseId { get; set; }

    /// <summary>
    /// 检索配置
    /// </summary>
    public string? RetrievalConfig { get; set; }
}

/// <summary>
/// 智能体版本DTO
/// </summary>
public class AgentVersionDto
{
    /// <summary>
    /// 版本ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// 版本号
    /// </summary>
    public string Version { get; set; } = string.Empty;

    /// <summary>
    /// 版本说明
    /// </summary>
    public string? Notes { get; set; }

    /// <summary>
    /// 是否为当前版本
    /// </summary>
    public bool IsCurrent { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedAt { get; set; }

    /// <summary>
    /// 配置信息
    /// </summary>
    public AgentConfigurationDto? Configuration { get; set; }
}
