Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.0.31903.59
MinimumVisualStudioVersion = 10.0.40219.1

Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "src", "src", "{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "01-Presentation", "01-Presentation", "{B1C2D3E4-F5G6-7890-BCDE-F12345678901}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "02-Application", "02-Application", "{C1D2E3F4-G5H6-7890-CDEF-123456789012}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "03-Domain", "03-Domain", "{D1E2F3G4-H5I6-7890-DEF1-************}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "04-Infrastructure", "04-Infrastructure", "{E1F2G3H4-I5J6-7890-EF12-************}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "05-Shared", "05-Shared", "{F1G2H3I4-J5K6-7890-F123-************}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "tests", "tests", "{G1H2I3J4-K5L6-7890-1234-567890123456}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "tools", "tools", "{H1I2J3K4-L5M6-7890-2345-678901234567}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "docs", "docs", "{I1J2K3L4-M5N6-7890-3456-789012345678}"
EndProject

Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Solution Items", "Solution Items", "{J1K2L3M4-N5O6-7890-4567-890123456789}"
	ProjectSection(SolutionItems) = preProject
		.gitignore = .gitignore
		.dockerignore = .dockerignore
		Directory.Build.props = Directory.Build.props
		Directory.Packages.props = Directory.Packages.props
		global.json = global.json
		nuget.config = nuget.config
		CodeAnalysis.ruleset = CodeAnalysis.ruleset
		README.md = README.md
		docker-compose.yml = docker-compose.yml
		docker-compose.override.yml = docker-compose.override.yml
	EndProjectSection
EndProject

Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{B1C2D3E4-F5G6-7890-BCDE-F12345678901} = {A1B2C3D4-E5F6-7890-ABCD-EF1234567890}
		{C1D2E3F4-G5H6-7890-CDEF-123456789012} = {A1B2C3D4-E5F6-7890-ABCD-EF1234567890}
		{D1E2F3G4-H5I6-7890-DEF1-************} = {A1B2C3D4-E5F6-7890-ABCD-EF1234567890}
		{E1F2G3H4-I5J6-7890-EF12-************} = {A1B2C3D4-E5F6-7890-ABCD-EF1234567890}
		{F1G2H3I4-J5K6-7890-F123-************} = {A1B2C3D4-E5F6-7890-ABCD-EF1234567890}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {12345678-1234-1234-1234-123456789012}
	EndGlobalSection
EndGlobal
