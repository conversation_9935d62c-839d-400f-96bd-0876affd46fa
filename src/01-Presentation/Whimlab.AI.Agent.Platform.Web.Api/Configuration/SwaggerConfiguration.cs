using System.Reflection;
using Microsoft.AspNetCore.Mvc.ApiExplorer;
using Microsoft.OpenApi.Models;
using Microsoft.OpenApi.Any;
using Swashbuckle.AspNetCore.SwaggerGen;
using Swashbuckle.AspNetCore.Annotations;

namespace Whimlab.AI.Agent.Platform.Web.Api.Configuration;

/// <summary>
/// Swagger配置扩展
/// </summary>
public static class SwaggerConfiguration
{
    /// <summary>
    /// 添加Swagger文档服务
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddSwaggerDocumentation(this IServiceCollection services)
    {
        services.AddEndpointsApiExplorer();
        
        services.AddSwaggerGen(options =>
        {
            // API信息配置
            options.SwaggerDoc("v1", new OpenApiInfo
            {
                Title = "AI智能体平台 API",
                Version = "v1.0",
                Description = "企业级AI智能体管理平台的RESTful API文档",
                Contact = new OpenApiContact
                {
                    Name = "开发团队",
                    Email = "<EMAIL>",
                    Url = new Uri("https://whimlab.ai")
                },
                License = new OpenApiLicense
                {
                    Name = "MIT License",
                    Url = new Uri("https://opensource.org/licenses/MIT")
                },
                TermsOfService = new Uri("https://whimlab.ai/terms")
            });

            // JWT认证配置
            options.AddSecurityDefinition("Bearer", new OpenApiSecurityScheme
            {
                Name = "Authorization",
                Type = SecuritySchemeType.Http,
                Scheme = "Bearer",
                BearerFormat = "JWT",
                In = ParameterLocation.Header,
                Description = "请输入JWT令牌，格式：Bearer {token}"
            });

            options.AddSecurityRequirement(new OpenApiSecurityRequirement
            {
                {
                    new OpenApiSecurityScheme
                    {
                        Reference = new OpenApiReference
                        {
                            Type = ReferenceType.SecurityScheme,
                            Id = "Bearer"
                        }
                    },
                    Array.Empty<string>()
                }
            });

            // XML注释文档
            var xmlFile = $"{Assembly.GetExecutingAssembly().GetName().Name}.xml";
            var xmlPath = Path.Combine(AppContext.BaseDirectory, xmlFile);
            if (File.Exists(xmlPath))
            {
                options.IncludeXmlComments(xmlPath);
            }

            // 包含应用层的XML注释
            var applicationCoreXml = Path.Combine(AppContext.BaseDirectory, 
                "Whimlab.AI.Agent.Platform.Application.Core.xml");
            if (File.Exists(applicationCoreXml))
            {
                options.IncludeXmlComments(applicationCoreXml);
            }

            // 自定义操作过滤器
            options.OperationFilter<SwaggerDefaultValuesFilter>();
            options.DocumentFilter<SwaggerTagDescriptionsFilter>();

            // 自定义Schema ID生成器
            options.CustomSchemaIds(type => type.FullName?.Replace("+", "."));

            // 启用注解
            options.EnableAnnotations();

            // 配置枚举显示
            options.SchemaFilter<EnumSchemaFilter>();
        });

        return services;
    }

    /// <summary>
    /// 使用Swagger文档
    /// </summary>
    /// <param name="app">应用程序构建器</param>
    /// <param name="environment">环境信息</param>
    /// <returns>应用程序构建器</returns>
    public static IApplicationBuilder UseSwaggerDocumentation(
        this IApplicationBuilder app, 
        IWebHostEnvironment environment)
    {
        if (environment.IsDevelopment() || environment.IsStaging())
        {
            app.UseSwagger(options =>
            {
                options.RouteTemplate = "api-docs/{documentName}/swagger.json";
            });

            app.UseSwaggerUI(options =>
            {
                options.SwaggerEndpoint("/api-docs/v1/swagger.json", "AI智能体平台 API v1.0");
                options.RoutePrefix = "api-docs";
                options.DocumentTitle = "AI智能体平台 API 文档";
                
                // UI配置
                options.DefaultModelsExpandDepth(-1);
                options.DefaultModelRendering(Swashbuckle.AspNetCore.SwaggerUI.ModelRendering.Model);
                options.DocExpansion(Swashbuckle.AspNetCore.SwaggerUI.DocExpansion.None);
                options.EnableDeepLinking();
                options.DisplayOperationId();
                options.DisplayRequestDuration();
                
                // 自定义CSS
                options.InjectStylesheet("/swagger-ui/custom.css");
            });
        }

        return app;
    }
}

/// <summary>
/// Swagger默认值过滤器
/// </summary>
public class SwaggerDefaultValuesFilter : IOperationFilter
{
    /// <summary>
    /// 应用过滤器
    /// </summary>
    /// <param name="operation">操作</param>
    /// <param name="context">上下文</param>
    public void Apply(OpenApiOperation operation, OperationFilterContext context)
    {
        var apiDescription = context.ApiDescription;

        operation.Deprecated |= apiDescription.IsDeprecated();

        foreach (var responseType in context.ApiDescription.SupportedResponseTypes)
        {
            var responseKey = responseType.IsDefaultResponse ? "default" : responseType.StatusCode.ToString();
            var response = operation.Responses[responseKey];

            foreach (var contentType in response.Content.Keys)
            {
                if (responseType.ApiResponseFormats.All(x => x.MediaType != contentType))
                {
                    response.Content.Remove(contentType);
                }
            }
        }

        if (operation.Parameters == null)
        {
            return;
        }

        foreach (var parameter in operation.Parameters)
        {
            var description = apiDescription.ParameterDescriptions
                .First(p => p.Name == parameter.Name);

            parameter.Description ??= description.ModelMetadata?.Description;

            if (parameter.Schema.Default == null && description.DefaultValue != null)
            {
                parameter.Schema.Default = Microsoft.OpenApi.Any.OpenApiAnyFactory
                    .CreateFromJson(System.Text.Json.JsonSerializer.Serialize(description.DefaultValue));
            }

            parameter.Required |= description.IsRequired;
        }
    }
}

/// <summary>
/// Swagger标签描述过滤器
/// </summary>
public class SwaggerTagDescriptionsFilter : IDocumentFilter
{
    /// <summary>
    /// 应用过滤器
    /// </summary>
    /// <param name="swaggerDoc">Swagger文档</param>
    /// <param name="context">上下文</param>
    public void Apply(OpenApiDocument swaggerDoc, DocumentFilterContext context)
    {
        swaggerDoc.Tags = new List<OpenApiTag>
        {
            new() { Name = "Auth", Description = "身份认证相关接口" },
            new() { Name = "Users", Description = "用户管理相关接口" },
            new() { Name = "Roles", Description = "角色管理相关接口" },
            new() { Name = "Permissions", Description = "权限管理相关接口" },
            new() { Name = "Agents", Description = "智能体管理相关接口" },
            new() { Name = "AgentConfigurations", Description = "智能体配置相关接口" },
            new() { Name = "Conversations", Description = "对话管理相关接口" },
            new() { Name = "Messages", Description = "消息处理相关接口" },
            new() { Name = "Subscriptions", Description = "订阅管理相关接口" },
            new() { Name = "Quota", Description = "配额管理相关接口" },
            new() { Name = "Health", Description = "健康检查相关接口" }
        };
    }
}

/// <summary>
/// 枚举Schema过滤器
/// </summary>
public class EnumSchemaFilter : ISchemaFilter
{
    /// <summary>
    /// 应用过滤器
    /// </summary>
    /// <param name="schema">Schema</param>
    /// <param name="context">上下文</param>
    public void Apply(OpenApiSchema schema, SchemaFilterContext context)
    {
        if (context.Type.IsEnum)
        {
            schema.Enum.Clear();
            Enum.GetNames(context.Type)
                .ToList()
                .ForEach(name => schema.Enum.Add(new Microsoft.OpenApi.Any.OpenApiString(name)));
        }
    }
}
