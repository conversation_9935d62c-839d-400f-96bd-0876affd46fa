using Whimlab.AI.Agent.Platform.Domain.Conversation.Entities;
using Whimlab.AI.Agent.Platform.Domain.Core.Common;
using Whimlab.AI.Agent.Platform.Shared.Common.Enums;

namespace Whimlab.AI.Agent.Platform.Domain.Conversation.Repositories;

/// <summary>
/// 对话仓储接口
/// </summary>
public interface IConversationRepository
{
    /// <summary>
    /// 根据ID获取对话
    /// </summary>
    /// <param name="id">对话ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>对话实体</returns>
    Task<Entities.Conversation?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default);

    /// <summary>
    /// 根据ID获取对话（包含消息）
    /// </summary>
    /// <param name="id">对话ID</param>
    /// <param name="includeMessages">是否包含消息</param>
    /// <param name="messageLimit">消息数量限制</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>对话实体</returns>
    Task<Entities.Conversation?> GetByIdWithMessagesAsync(
        Guid id, 
        bool includeMessages = true, 
        int? messageLimit = null, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取用户的对话列表
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="pageNumber">页码</param>
    /// <param name="pageSize">页大小</param>
    /// <param name="searchTerm">搜索关键词</param>
    /// <param name="status">状态过滤</param>
    /// <param name="agentId">智能体ID过滤</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>分页的对话列表</returns>
    Task<PagedResult<Entities.Conversation>> GetUserConversationsAsync(
        Guid userId,
        int pageNumber = 1,
        int pageSize = 20,
        string? searchTerm = null,
        ConversationStatus? status = null,
        Guid? agentId = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取智能体的对话列表
    /// </summary>
    /// <param name="agentId">智能体ID</param>
    /// <param name="pageNumber">页码</param>
    /// <param name="pageSize">页大小</param>
    /// <param name="status">状态过滤</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>分页的对话列表</returns>
    Task<PagedResult<Entities.Conversation>> GetAgentConversationsAsync(
        Guid agentId,
        int pageNumber = 1,
        int pageSize = 20,
        ConversationStatus? status = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取需要自动归档的对话
    /// </summary>
    /// <param name="inactiveThreshold">非活跃阈值</param>
    /// <param name="batchSize">批次大小</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>需要归档的对话列表</returns>
    Task<IEnumerable<Entities.Conversation>> GetConversationsForAutoArchiveAsync(
        TimeSpan inactiveThreshold,
        int batchSize = 100,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取对话统计信息
    /// </summary>
    /// <param name="userId">用户ID（可选）</param>
    /// <param name="agentId">智能体ID（可选）</param>
    /// <param name="startDate">开始日期（可选）</param>
    /// <param name="endDate">结束日期（可选）</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>统计信息</returns>
    Task<ConversationStatistics> GetStatisticsAsync(
        Guid? userId = null,
        Guid? agentId = null,
        DateTime? startDate = null,
        DateTime? endDate = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取Token使用统计
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="startDate">开始日期</param>
    /// <param name="endDate">结束日期</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>Token使用统计</returns>
    Task<TokenUsageStatistics> GetTokenUsageStatisticsAsync(
        Guid userId,
        DateTime startDate,
        DateTime endDate,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 添加对话
    /// </summary>
    /// <param name="conversation">对话实体</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>任务</returns>
    Task AddAsync(Entities.Conversation conversation, CancellationToken cancellationToken = default);

    /// <summary>
    /// 更新对话
    /// </summary>
    /// <param name="conversation">对话实体</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>任务</returns>
    Task UpdateAsync(Entities.Conversation conversation, CancellationToken cancellationToken = default);

    /// <summary>
    /// 删除对话
    /// </summary>
    /// <param name="conversation">对话实体</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>任务</returns>
    Task DeleteAsync(Entities.Conversation conversation, CancellationToken cancellationToken = default);

    /// <summary>
    /// 保存更改
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>受影响的行数</returns>
    Task<int> SaveChangesAsync(CancellationToken cancellationToken = default);
}

/// <summary>
/// 对话统计信息
/// </summary>
public class ConversationStatistics
{
    /// <summary>
    /// 总对话数
    /// </summary>
    public int TotalConversations { get; set; }

    /// <summary>
    /// 活跃对话数
    /// </summary>
    public int ActiveConversations { get; set; }

    /// <summary>
    /// 已归档对话数
    /// </summary>
    public int ArchivedConversations { get; set; }

    /// <summary>
    /// 总消息数
    /// </summary>
    public int TotalMessages { get; set; }

    /// <summary>
    /// 总Token使用量
    /// </summary>
    public long TotalTokensUsed { get; set; }

    /// <summary>
    /// 平均每个对话的消息数
    /// </summary>
    public double AverageMessagesPerConversation { get; set; }

    /// <summary>
    /// 平均每个对话的Token使用量
    /// </summary>
    public double AverageTokensPerConversation { get; set; }

    /// <summary>
    /// 按状态分组的统计
    /// </summary>
    public Dictionary<ConversationStatus, int> CountByStatus { get; set; } = new();
}

/// <summary>
/// Token使用统计
/// </summary>
public class TokenUsageStatistics
{
    /// <summary>
    /// 用户ID
    /// </summary>
    public Guid UserId { get; set; }

    /// <summary>
    /// 统计期间总Token使用量
    /// </summary>
    public long TotalTokensUsed { get; set; }

    /// <summary>
    /// 按智能体分组的Token使用量
    /// </summary>
    public Dictionary<Guid, long> TokensByAgent { get; set; } = new();

    /// <summary>
    /// 按日期分组的Token使用量
    /// </summary>
    public Dictionary<DateTime, long> TokensByDate { get; set; } = new();

    /// <summary>
    /// 平均每日Token使用量
    /// </summary>
    public double AverageTokensPerDay { get; set; }

    /// <summary>
    /// 最高单日Token使用量
    /// </summary>
    public long PeakDailyTokens { get; set; }
}
