using System.Globalization;
using System.Security.Cryptography;
using System.Text;
using System.Text.Json;
using System.Text.RegularExpressions;

namespace Whimlab.AI.Agent.Platform.Shared.Common.Extensions;

/// <summary>
/// 字符串扩展方法
/// </summary>
public static class StringExtensions
{
    private static readonly Regex EmailRegex = new(
        @"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$",
        RegexOptions.Compiled | RegexOptions.IgnoreCase);

    private static readonly Regex PhoneRegex = new(
        @"^1[3-9]\d{9}$",
        RegexOptions.Compiled);

    private static readonly Regex PasswordRegex = new(
        @"^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,20}$",
        RegexOptions.Compiled);

    /// <summary>
    /// 检查字符串是否为空或空白
    /// </summary>
    /// <param name="value">要检查的字符串</param>
    /// <returns>如果为空或空白返回true，否则返回false</returns>
    public static bool IsNullOrWhiteSpace(this string? value)
    {
        return string.IsNullOrWhiteSpace(value);
    }

    /// <summary>
    /// 检查字符串是否不为空且不为空白
    /// </summary>
    /// <param name="value">要检查的字符串</param>
    /// <returns>如果不为空且不为空白返回true，否则返回false</returns>
    public static bool IsNotNullOrWhiteSpace(this string? value)
    {
        return !string.IsNullOrWhiteSpace(value);
    }

    /// <summary>
    /// 安全地截取字符串
    /// </summary>
    /// <param name="value">要截取的字符串</param>
    /// <param name="maxLength">最大长度</param>
    /// <param name="suffix">超出长度时的后缀</param>
    /// <returns>截取后的字符串</returns>
    public static string Truncate(this string? value, int maxLength, string suffix = "...")
    {
        if (string.IsNullOrEmpty(value) || value.Length <= maxLength)
            return value ?? string.Empty;

        return value[..(maxLength - suffix.Length)] + suffix;
    }

    /// <summary>
    /// 转换为帕斯卡命名法
    /// </summary>
    /// <param name="value">要转换的字符串</param>
    /// <returns>帕斯卡命名法字符串</returns>
    public static string ToPascalCase(this string value)
    {
        if (string.IsNullOrWhiteSpace(value))
            return string.Empty;

        return CultureInfo.CurrentCulture.TextInfo.ToTitleCase(value.ToLower()).Replace(" ", "");
    }

    /// <summary>
    /// 转换为驼峰命名法
    /// </summary>
    /// <param name="value">要转换的字符串</param>
    /// <returns>驼峰命名法字符串</returns>
    public static string ToCamelCase(this string value)
    {
        if (string.IsNullOrWhiteSpace(value))
            return string.Empty;

        var pascalCase = value.ToPascalCase();
        return char.ToLowerInvariant(pascalCase[0]) + pascalCase[1..];
    }

    /// <summary>
    /// 转换为蛇形命名法
    /// </summary>
    /// <param name="value">要转换的字符串</param>
    /// <returns>蛇形命名法字符串</returns>
    public static string ToSnakeCase(this string value)
    {
        if (string.IsNullOrWhiteSpace(value))
            return string.Empty;

        return Regex.Replace(value, "([a-z0-9])([A-Z])", "$1_$2").ToLower();
    }

    /// <summary>
    /// 转换为短横线命名法
    /// </summary>
    /// <param name="value">要转换的字符串</param>
    /// <returns>短横线命名法字符串</returns>
    public static string ToKebabCase(this string value)
    {
        if (string.IsNullOrWhiteSpace(value))
            return string.Empty;

        return Regex.Replace(value, "([a-z0-9])([A-Z])", "$1-$2").ToLower();
    }

    /// <summary>
    /// 验证是否为有效的邮箱地址
    /// </summary>
    /// <param name="email">邮箱地址</param>
    /// <returns>如果是有效邮箱返回true，否则返回false</returns>
    public static bool IsValidEmail(this string? email)
    {
        if (string.IsNullOrWhiteSpace(email))
            return false;

        return EmailRegex.IsMatch(email);
    }

    /// <summary>
    /// 验证是否为有效的手机号码（中国大陆）
    /// </summary>
    /// <param name="phone">手机号码</param>
    /// <returns>如果是有效手机号返回true，否则返回false</returns>
    public static bool IsValidPhone(this string? phone)
    {
        if (string.IsNullOrWhiteSpace(phone))
            return false;

        return PhoneRegex.IsMatch(phone);
    }

    /// <summary>
    /// 验证是否为强密码
    /// </summary>
    /// <param name="password">密码</param>
    /// <returns>如果是强密码返回true，否则返回false</returns>
    public static bool IsStrongPassword(this string? password)
    {
        if (string.IsNullOrWhiteSpace(password))
            return false;

        return PasswordRegex.IsMatch(password);
    }

    /// <summary>
    /// 计算字符串的MD5哈希值
    /// </summary>
    /// <param name="value">要计算哈希的字符串</param>
    /// <returns>MD5哈希值</returns>
    public static string ToMd5Hash(this string value)
    {
        if (string.IsNullOrEmpty(value))
            return string.Empty;

        using var md5 = MD5.Create();
        var inputBytes = Encoding.UTF8.GetBytes(value);
        var hashBytes = md5.ComputeHash(inputBytes);

        return Convert.ToHexString(hashBytes).ToLowerInvariant();
    }

    /// <summary>
    /// 计算字符串的SHA256哈希值
    /// </summary>
    /// <param name="value">要计算哈希的字符串</param>
    /// <returns>SHA256哈希值</returns>
    public static string ToSha256Hash(this string value)
    {
        if (string.IsNullOrEmpty(value))
            return string.Empty;

        using var sha256 = SHA256.Create();
        var inputBytes = Encoding.UTF8.GetBytes(value);
        var hashBytes = sha256.ComputeHash(inputBytes);

        return Convert.ToHexString(hashBytes).ToLowerInvariant();
    }

    /// <summary>
    /// Base64编码
    /// </summary>
    /// <param name="value">要编码的字符串</param>
    /// <returns>Base64编码后的字符串</returns>
    public static string ToBase64(this string value)
    {
        if (string.IsNullOrEmpty(value))
            return string.Empty;

        var bytes = Encoding.UTF8.GetBytes(value);
        return Convert.ToBase64String(bytes);
    }

    /// <summary>
    /// Base64解码
    /// </summary>
    /// <param name="value">要解码的Base64字符串</param>
    /// <returns>解码后的字符串</returns>
    public static string FromBase64(this string value)
    {
        if (string.IsNullOrEmpty(value))
            return string.Empty;

        try
        {
            var bytes = Convert.FromBase64String(value);
            return Encoding.UTF8.GetString(bytes);
        }
        catch
        {
            return string.Empty;
        }
    }

    /// <summary>
    /// 反序列化JSON字符串
    /// </summary>
    /// <typeparam name="T">目标类型</typeparam>
    /// <param name="json">JSON字符串</param>
    /// <param name="options">JSON序列化选项</param>
    /// <returns>反序列化后的对象</returns>
    public static T? FromJson<T>(this string json, JsonSerializerOptions? options = null)
    {
        if (string.IsNullOrWhiteSpace(json))
            return default;

        try
        {
            return JsonSerializer.Deserialize<T>(json, options);
        }
        catch
        {
            return default;
        }
    }

    /// <summary>
    /// 移除HTML标签
    /// </summary>
    /// <param name="html">包含HTML标签的字符串</param>
    /// <returns>移除HTML标签后的纯文本</returns>
    public static string StripHtml(this string html)
    {
        if (string.IsNullOrWhiteSpace(html))
            return string.Empty;

        return Regex.Replace(html, "<.*?>", string.Empty);
    }

    /// <summary>
    /// 生成随机字符串
    /// </summary>
    /// <param name="length">字符串长度</param>
    /// <param name="includeNumbers">是否包含数字</param>
    /// <param name="includeUppercase">是否包含大写字母</param>
    /// <param name="includeLowercase">是否包含小写字母</param>
    /// <param name="includeSpecialChars">是否包含特殊字符</param>
    /// <returns>随机字符串</returns>
    public static string GenerateRandomString(
        int length,
        bool includeNumbers = true,
        bool includeUppercase = true,
        bool includeLowercase = true,
        bool includeSpecialChars = false)
    {
        if (length <= 0)
            return string.Empty;

        var chars = new StringBuilder();
        
        if (includeLowercase)
            chars.Append("abcdefghijklmnopqrstuvwxyz");
        
        if (includeUppercase)
            chars.Append("ABCDEFGHIJKLMNOPQRSTUVWXYZ");
        
        if (includeNumbers)
            chars.Append("0123456789");
        
        if (includeSpecialChars)
            chars.Append("!@#$%^&*()_+-=[]{}|;:,.<>?");

        if (chars.Length == 0)
            return string.Empty;

        var random = new Random();
        var result = new StringBuilder(length);
        var charArray = chars.ToString();

        for (int i = 0; i < length; i++)
        {
            result.Append(charArray[random.Next(charArray.Length)]);
        }

        return result.ToString();
    }

    /// <summary>
    /// 掩码敏感信息
    /// </summary>
    /// <param name="value">要掩码的字符串</param>
    /// <param name="visibleStart">开始可见字符数</param>
    /// <param name="visibleEnd">结束可见字符数</param>
    /// <param name="maskChar">掩码字符</param>
    /// <returns>掩码后的字符串</returns>
    public static string Mask(this string? value, int visibleStart = 3, int visibleEnd = 3, char maskChar = '*')
    {
        if (string.IsNullOrEmpty(value) || value.Length <= visibleStart + visibleEnd)
            return value ?? string.Empty;

        var start = value[..visibleStart];
        var end = value[^visibleEnd..];
        var maskLength = value.Length - visibleStart - visibleEnd;
        var mask = new string(maskChar, maskLength);

        return start + mask + end;
    }
}
