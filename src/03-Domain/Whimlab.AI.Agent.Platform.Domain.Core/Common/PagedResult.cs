namespace Whimlab.AI.Agent.Platform.Domain.Core.Common;

/// <summary>
/// 分页结果
/// </summary>
/// <typeparam name="T">数据类型</typeparam>
public class PagedResult<T>
{
    /// <summary>
    /// 数据列表
    /// </summary>
    public IEnumerable<T> Items { get; }

    /// <summary>
    /// 总记录数
    /// </summary>
    public int TotalCount { get; }

    /// <summary>
    /// 页码
    /// </summary>
    public int PageNumber { get; }

    /// <summary>
    /// 页大小
    /// </summary>
    public int PageSize { get; }

    /// <summary>
    /// 总页数
    /// </summary>
    public int TotalPages => (int)Math.Ceiling((double)TotalCount / PageSize);

    /// <summary>
    /// 是否有上一页
    /// </summary>
    public bool HasPreviousPage => PageNumber > 1;

    /// <summary>
    /// 是否有下一页
    /// </summary>
    public bool HasNextPage => PageNumber < TotalPages;

    /// <summary>
    /// 初始化分页结果
    /// </summary>
    /// <param name="items">数据列表</param>
    /// <param name="totalCount">总记录数</param>
    /// <param name="pageNumber">页码</param>
    /// <param name="pageSize">页大小</param>
    public PagedResult(IEnumerable<T> items, int totalCount, int pageNumber, int pageSize)
    {
        Items = items;
        TotalCount = totalCount;
        PageNumber = pageNumber;
        PageSize = pageSize;
    }

    /// <summary>
    /// 创建空的分页结果
    /// </summary>
    /// <param name="pageNumber">页码</param>
    /// <param name="pageSize">页大小</param>
    /// <returns>空的分页结果</returns>
    public static PagedResult<T> Empty(int pageNumber = 1, int pageSize = 20)
    {
        return new PagedResult<T>(Enumerable.Empty<T>(), 0, pageNumber, pageSize);
    }
}

/// <summary>
/// 分页查询基类
/// </summary>
public abstract class PagedQuery
{
    /// <summary>
    /// 页码（从1开始）
    /// </summary>
    public int PageNumber { get; set; } = 1;

    /// <summary>
    /// 页大小
    /// </summary>
    public int PageSize { get; set; } = 20;

    /// <summary>
    /// 搜索关键词
    /// </summary>
    public string? SearchTerm { get; set; }

    /// <summary>
    /// 排序字段
    /// </summary>
    public string? SortBy { get; set; }

    /// <summary>
    /// 排序方向
    /// </summary>
    public string? SortDirection { get; set; } = "asc";

    /// <summary>
    /// 获取跳过的记录数
    /// </summary>
    /// <returns>跳过的记录数</returns>
    public int GetSkip() => (PageNumber - 1) * PageSize;

    /// <summary>
    /// 验证分页参数
    /// </summary>
    public virtual void Validate()
    {
        if (PageNumber < 1)
            PageNumber = 1;

        if (PageSize < 1)
            PageSize = 20;

        if (PageSize > 100)
            PageSize = 100;
    }
}
