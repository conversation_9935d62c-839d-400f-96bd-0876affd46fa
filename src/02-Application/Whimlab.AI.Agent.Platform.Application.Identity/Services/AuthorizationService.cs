using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Logging;
using Whimlab.AI.Agent.Platform.Domain.Identity.Entities;
using Whimlab.AI.Agent.Platform.Domain.Identity.Repositories;
using Whimlab.AI.Agent.Platform.Domain.Identity.Services;

namespace Whimlab.AI.Agent.Platform.Application.Identity.Services;

/// <summary>
/// 授权服务实现
/// 负责基于角色的访问控制(RBAC)和资源级别的权限验证
/// </summary>
public class AuthorizationService : IAuthorizationService
{
    private readonly IUserRepository _userRepository;
    private readonly IRoleRepository _roleRepository;
    private readonly IPermissionRepository _permissionRepository;
    private readonly IUserDomainService _userDomainService;
    private readonly IMemoryCache _cache;
    private readonly ILogger<AuthorizationService> _logger;

    /// <summary>
    /// 缓存过期时间常量
    /// </summary>
    private static readonly TimeSpan DefaultCacheExpiration = TimeSpan.FromMinutes(15);
    private static readonly TimeSpan PermissionCacheExpiration = TimeSpan.FromMinutes(30);

    /// <summary>
    /// 缓存键前缀
    /// </summary>
    private const string UserPermissionsCacheKey = "user_permissions_{0}";
    private const string UserRolesCacheKey = "user_roles_{0}";
    private const string UserSummaryCacheKey = "user_summary_{0}";

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="userRepository">用户仓储</param>
    /// <param name="roleRepository">角色仓储</param>
    /// <param name="permissionRepository">权限仓储</param>
    /// <param name="userDomainService">用户领域服务</param>
    /// <param name="cache">内存缓存</param>
    /// <param name="logger">日志记录器</param>
    public AuthorizationService(
        IUserRepository userRepository,
        IRoleRepository roleRepository,
        IPermissionRepository permissionRepository,
        IUserDomainService userDomainService,
        IMemoryCache cache,
        ILogger<AuthorizationService> logger)
    {
        _userRepository = userRepository ?? throw new ArgumentNullException(nameof(userRepository));
        _roleRepository = roleRepository ?? throw new ArgumentNullException(nameof(roleRepository));
        _permissionRepository = permissionRepository ?? throw new ArgumentNullException(nameof(permissionRepository));
        _userDomainService = userDomainService ?? throw new ArgumentNullException(nameof(userDomainService));
        _cache = cache ?? throw new ArgumentNullException(nameof(cache));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// 检查用户是否拥有指定权限
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="permissionName">权限名称</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>是否拥有权限</returns>
    public async Task<bool> HasPermissionAsync(Guid userId, string permissionName, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("检查用户 {UserId} 是否拥有权限 {PermissionName}", userId, permissionName);

            // 检查超级管理员权限
            if (await IsSuperAdminAsync(userId, cancellationToken))
            {
                _logger.LogDebug("用户 {UserId} 是超级管理员，拥有所有权限", userId);
                return true;
            }

            // 从缓存获取用户权限
            var userPermissions = await GetUserPermissionsCachedAsync(userId, cancellationToken);
            var hasPermission = userPermissions.Contains(permissionName, StringComparer.OrdinalIgnoreCase);

            _logger.LogDebug("用户 {UserId} 权限检查结果: {HasPermission} (权限: {PermissionName})", 
                userId, hasPermission, permissionName);

            return hasPermission;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "检查用户权限时发生错误，用户 {UserId}，权限 {PermissionName}", userId, permissionName);
            return false;
        }
    }

    /// <summary>
    /// 检查用户是否拥有指定角色
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="roleName">角色名称</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>是否拥有角色</returns>
    public async Task<bool> HasRoleAsync(Guid userId, string roleName, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("检查用户 {UserId} 是否拥有角色 {RoleName}", userId, roleName);

            // 从缓存获取用户角色
            var userRoles = await GetUserRolesCachedAsync(userId, cancellationToken);
            var hasRole = userRoles.Contains(roleName, StringComparer.OrdinalIgnoreCase);

            _logger.LogDebug("用户 {UserId} 角色检查结果: {HasRole} (角色: {RoleName})", 
                userId, hasRole, roleName);

            return hasRole;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "检查用户角色时发生错误，用户 {UserId}，角色 {RoleName}", userId, roleName);
            return false;
        }
    }

    /// <summary>
    /// 检查用户是否拥有任一指定权限
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="permissionNames">权限名称列表</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>是否拥有任一权限</returns>
    public async Task<bool> HasAnyPermissionAsync(Guid userId, IEnumerable<string> permissionNames, CancellationToken cancellationToken = default)
    {
        try
        {
            var permissions = permissionNames.ToList();
            _logger.LogDebug("检查用户 {UserId} 是否拥有任一权限: {Permissions}", userId, string.Join(", ", permissions));

            // 检查超级管理员权限
            if (await IsSuperAdminAsync(userId, cancellationToken))
            {
                return true;
            }

            // 从缓存获取用户权限
            var userPermissions = await GetUserPermissionsCachedAsync(userId, cancellationToken);
            var hasAnyPermission = permissions.Any(p => userPermissions.Contains(p, StringComparer.OrdinalIgnoreCase));

            _logger.LogDebug("用户 {UserId} 任一权限检查结果: {HasAnyPermission}", userId, hasAnyPermission);

            return hasAnyPermission;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "检查用户任一权限时发生错误，用户 {UserId}", userId);
            return false;
        }
    }

    /// <summary>
    /// 检查用户是否拥有所有指定权限
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="permissionNames">权限名称列表</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>是否拥有所有权限</returns>
    public async Task<bool> HasAllPermissionsAsync(Guid userId, IEnumerable<string> permissionNames, CancellationToken cancellationToken = default)
    {
        try
        {
            var permissions = permissionNames.ToList();
            _logger.LogDebug("检查用户 {UserId} 是否拥有所有权限: {Permissions}", userId, string.Join(", ", permissions));

            // 检查超级管理员权限
            if (await IsSuperAdminAsync(userId, cancellationToken))
            {
                return true;
            }

            // 从缓存获取用户权限
            var userPermissions = await GetUserPermissionsCachedAsync(userId, cancellationToken);
            var hasAllPermissions = permissions.All(p => userPermissions.Contains(p, StringComparer.OrdinalIgnoreCase));

            _logger.LogDebug("用户 {UserId} 所有权限检查结果: {HasAllPermissions}", userId, hasAllPermissions);

            return hasAllPermissions;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "检查用户所有权限时发生错误，用户 {UserId}", userId);
            return false;
        }
    }

    /// <summary>
    /// 获取用户的所有权限
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>权限名称列表</returns>
    public async Task<IEnumerable<string>> GetUserPermissionsAsync(Guid userId, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("获取用户 {UserId} 的所有权限", userId);

            return await GetUserPermissionsCachedAsync(userId, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取用户权限时发生错误，用户 {UserId}", userId);
            return Enumerable.Empty<string>();
        }
    }

    /// <summary>
    /// 获取用户的所有角色
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>角色名称列表</returns>
    public async Task<IEnumerable<string>> GetUserRolesAsync(Guid userId, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("获取用户 {UserId} 的所有角色", userId);

            return await GetUserRolesCachedAsync(userId, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取用户角色时发生错误，用户 {UserId}", userId);
            return Enumerable.Empty<string>();
        }
    }

    /// <summary>
    /// 检查用户是否可以访问指定资源
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="resourceType">资源类型</param>
    /// <param name="resourceId">资源ID</param>
    /// <param name="operation">操作类型</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>是否可以访问</returns>
    public async Task<bool> CanAccessResourceAsync(Guid userId, string resourceType, Guid resourceId, string operation, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("检查用户 {UserId} 是否可以对资源 {ResourceType}:{ResourceId} 执行操作 {Operation}", 
                userId, resourceType, resourceId, operation);

            // 检查超级管理员权限
            if (await IsSuperAdminAsync(userId, cancellationToken))
            {
                return true;
            }

            // 构建权限名称
            var permissionName = $"{resourceType}.{operation}";
            
            // 检查是否拥有通用权限
            if (await HasPermissionAsync(userId, permissionName, cancellationToken))
            {
                return true;
            }

            // 检查是否为资源所有者
            if (await IsResourceOwnerAsync(userId, resourceType, resourceId, cancellationToken))
            {
                // 资源所有者通常拥有读取、更新权限，但不一定有删除权限
                var ownerOperations = new[] { "Read", "Update", "Execute" };
                if (ownerOperations.Contains(operation, StringComparer.OrdinalIgnoreCase))
                {
                    return true;
                }
            }

            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "检查资源访问权限时发生错误，用户 {UserId}，资源 {ResourceType}:{ResourceId}", 
                userId, resourceType, resourceId);
            return false;
        }
    }

    /// <summary>
    /// 检查用户是否为资源所有者
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="resourceType">资源类型</param>
    /// <param name="resourceId">资源ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>是否为所有者</returns>
    public async Task<bool> IsResourceOwnerAsync(Guid userId, string resourceType, Guid resourceId, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("检查用户 {UserId} 是否为资源 {ResourceType}:{ResourceId} 的所有者", 
                userId, resourceType, resourceId);

            // 这里需要根据不同的资源类型实现不同的所有者检查逻辑
            // 由于这是应用层服务，实际的资源所有者检查应该委托给相应的领域服务
            // 这里提供一个基础实现框架

            switch (resourceType.ToLowerInvariant())
            {
                case "user":
                    return userId == resourceId;
                
                case "agent":
                    // 需要调用Agent领域服务检查
                    // return await _agentDomainService.IsOwnerAsync(userId, resourceId, cancellationToken);
                    _logger.LogWarning("Agent资源所有者检查需要实现");
                    return false;
                
                case "conversation":
                    // 需要调用Conversation领域服务检查
                    // return await _conversationDomainService.IsOwnerAsync(userId, resourceId, cancellationToken);
                    _logger.LogWarning("Conversation资源所有者检查需要实现");
                    return false;
                
                default:
                    _logger.LogWarning("未知的资源类型: {ResourceType}", resourceType);
                    return false;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "检查资源所有者时发生错误，用户 {UserId}，资源 {ResourceType}:{ResourceId}", 
                userId, resourceType, resourceId);
            return false;
        }
    }
