using Whimlab.AI.Agent.Platform.Domain.Core.Common;
using Whimlab.AI.Agent.Platform.Domain.Identity.ValueObjects;
using Whimlab.AI.Agent.Platform.Domain.Identity.Events;
using Whimlab.AI.Agent.Platform.Shared.Common.Enums;

namespace Whimlab.AI.Agent.Platform.Domain.Identity.Entities;

/// <summary>
/// 用户聚合根
/// </summary>
public class User : AggregateRoot
{
    /// <summary>
    /// 用户名
    /// </summary>
    public string Username { get; private set; } = string.Empty;

    /// <summary>
    /// 邮箱地址
    /// </summary>
    public Email Email { get; private set; } = null!;

    /// <summary>
    /// 手机号码
    /// </summary>
    public PhoneNumber? PhoneNumber { get; private set; }

    /// <summary>
    /// 密码
    /// </summary>
    public Password Password { get; private set; } = null!;

    /// <summary>
    /// 显示名称
    /// </summary>
    public string DisplayName { get; private set; } = string.Empty;

    /// <summary>
    /// 头像URL
    /// </summary>
    public string? AvatarUrl { get; private set; }

    /// <summary>
    /// 用户类型
    /// </summary>
    public UserType UserType { get; private set; }

    /// <summary>
    /// 是否已激活
    /// </summary>
    public bool IsActive { get; private set; }

    /// <summary>
    /// 是否已验证邮箱
    /// </summary>
    public bool IsEmailVerified { get; private set; }

    /// <summary>
    /// 是否已验证手机
    /// </summary>
    public bool IsPhoneVerified { get; private set; }

    /// <summary>
    /// 是否已锁定
    /// </summary>
    public bool IsLocked { get; private set; }

    /// <summary>
    /// 锁定到期时间
    /// </summary>
    public DateTime? LockoutEnd { get; private set; }

    /// <summary>
    /// 失败登录次数
    /// </summary>
    public int FailedLoginAttempts { get; private set; }

    /// <summary>
    /// 最后登录时间
    /// </summary>
    public DateTime? LastLoginAt { get; private set; }

    /// <summary>
    /// 最后登录IP
    /// </summary>
    public string? LastLoginIp { get; private set; }

    /// <summary>
    /// 密码最后修改时间
    /// </summary>
    public DateTime PasswordChangedAt { get; private set; }

    /// <summary>
    /// 邮箱验证令牌
    /// </summary>
    public string? EmailVerificationToken { get; private set; }

    /// <summary>
    /// 邮箱验证令牌过期时间
    /// </summary>
    public DateTime? EmailVerificationTokenExpiry { get; private set; }

    /// <summary>
    /// 密码重置令牌
    /// </summary>
    public string? PasswordResetToken { get; private set; }

    /// <summary>
    /// 密码重置令牌过期时间
    /// </summary>
    public DateTime? PasswordResetTokenExpiry { get; private set; }

    /// <summary>
    /// 私有构造函数（用于EF Core）
    /// </summary>
    private User() { }

    /// <summary>
    /// 创建新用户
    /// </summary>
    /// <param name="username">用户名</param>
    /// <param name="email">邮箱地址</param>
    /// <param name="password">密码</param>
    /// <param name="displayName">显示名称</param>
    /// <param name="userType">用户类型</param>
    /// <param name="phoneNumber">手机号码</param>
    /// <returns>用户实体</returns>
    public static User Create(
        string username,
        Email email,
        Password password,
        string displayName,
        UserType userType,
        PhoneNumber? phoneNumber = null)
    {
        var user = new User
        {
            Username = username,
            Email = email,
            Password = password,
            DisplayName = displayName,
            UserType = userType,
            PhoneNumber = phoneNumber,
            IsActive = true,
            IsEmailVerified = false,
            IsPhoneVerified = false,
            IsLocked = false,
            FailedLoginAttempts = 0,
            PasswordChangedAt = DateTime.UtcNow
        };

        user.AddDomainEvent(new UserCreatedEvent(user.Id, user.Email.Value, user.UserType));
        user.GenerateEmailVerificationToken();

        return user;
    }

    /// <summary>
    /// 更新用户信息
    /// </summary>
    /// <param name="displayName">显示名称</param>
    /// <param name="avatarUrl">头像URL</param>
    public void UpdateProfile(string displayName, string? avatarUrl = null)
    {
        DisplayName = displayName;
        AvatarUrl = avatarUrl;
        MarkAsModified();

        AddDomainEvent(new UserProfileUpdatedEvent(Id, DisplayName, AvatarUrl));
    }

    /// <summary>
    /// 更改密码
    /// </summary>
    /// <param name="currentPassword">当前密码</param>
    /// <param name="newPassword">新密码</param>
    /// <exception cref="InvalidOperationException">当前密码验证失败</exception>
    public void ChangePassword(string currentPassword, Password newPassword)
    {
        if (!Password.Verify(currentPassword))
            throw new InvalidOperationException("当前密码验证失败");

        Password = newPassword;
        PasswordChangedAt = DateTime.UtcNow;
        MarkAsModified();

        AddDomainEvent(new UserPasswordChangedEvent(Id));
    }

    /// <summary>
    /// 重置密码
    /// </summary>
    /// <param name="token">重置令牌</param>
    /// <param name="newPassword">新密码</param>
    /// <exception cref="InvalidOperationException">重置令牌无效或已过期</exception>
    public void ResetPassword(string token, Password newPassword)
    {
        if (PasswordResetToken != token || 
            PasswordResetTokenExpiry == null || 
            PasswordResetTokenExpiry < DateTime.UtcNow)
        {
            throw new InvalidOperationException("密码重置令牌无效或已过期");
        }

        Password = newPassword;
        PasswordChangedAt = DateTime.UtcNow;
        PasswordResetToken = null;
        PasswordResetTokenExpiry = null;
        FailedLoginAttempts = 0;
        MarkAsModified();

        AddDomainEvent(new UserPasswordResetEvent(Id));
    }

    /// <summary>
    /// 验证邮箱
    /// </summary>
    /// <param name="token">验证令牌</param>
    /// <exception cref="InvalidOperationException">验证令牌无效或已过期</exception>
    public void VerifyEmail(string token)
    {
        if (EmailVerificationToken != token || 
            EmailVerificationTokenExpiry == null || 
            EmailVerificationTokenExpiry < DateTime.UtcNow)
        {
            throw new InvalidOperationException("邮箱验证令牌无效或已过期");
        }

        IsEmailVerified = true;
        EmailVerificationToken = null;
        EmailVerificationTokenExpiry = null;
        MarkAsModified();

        AddDomainEvent(new UserEmailVerifiedEvent(Id, Email.Value));
    }

    /// <summary>
    /// 记录登录成功
    /// </summary>
    /// <param name="ipAddress">IP地址</param>
    public void RecordSuccessfulLogin(string ipAddress)
    {
        LastLoginAt = DateTime.UtcNow;
        LastLoginIp = ipAddress;
        FailedLoginAttempts = 0;
        
        if (IsLocked)
        {
            IsLocked = false;
            LockoutEnd = null;
        }
        
        MarkAsModified();

        AddDomainEvent(new UserLoggedInEvent(Id, ipAddress));
    }

    /// <summary>
    /// 记录登录失败
    /// </summary>
    /// <param name="maxAttempts">最大失败次数</param>
    /// <param name="lockoutDuration">锁定时长</param>
    public void RecordFailedLogin(int maxAttempts = 5, TimeSpan? lockoutDuration = null)
    {
        FailedLoginAttempts++;
        
        if (FailedLoginAttempts >= maxAttempts)
        {
            IsLocked = true;
            LockoutEnd = DateTime.UtcNow.Add(lockoutDuration ?? TimeSpan.FromMinutes(30));
            
            AddDomainEvent(new UserLockedEvent(Id, LockoutEnd.Value));
        }
        
        MarkAsModified();
    }

    /// <summary>
    /// 激活用户
    /// </summary>
    public void Activate()
    {
        if (IsActive) return;

        IsActive = true;
        MarkAsModified();

        AddDomainEvent(new UserActivatedEvent(Id));
    }

    /// <summary>
    /// 停用用户
    /// </summary>
    public void Deactivate()
    {
        if (!IsActive) return;

        IsActive = false;
        MarkAsModified();

        AddDomainEvent(new UserDeactivatedEvent(Id));
    }

    /// <summary>
    /// 生成邮箱验证令牌
    /// </summary>
    public void GenerateEmailVerificationToken()
    {
        EmailVerificationToken = Guid.NewGuid().ToString("N");
        EmailVerificationTokenExpiry = DateTime.UtcNow.AddHours(24);
        MarkAsModified();
    }

    /// <summary>
    /// 生成密码重置令牌
    /// </summary>
    public void GeneratePasswordResetToken()
    {
        PasswordResetToken = Guid.NewGuid().ToString("N");
        PasswordResetTokenExpiry = DateTime.UtcNow.AddHours(1);
        MarkAsModified();
    }

    /// <summary>
    /// 检查是否可以登录
    /// </summary>
    /// <returns>是否可以登录</returns>
    public bool CanLogin()
    {
        return IsActive && !IsLocked && (LockoutEnd == null || LockoutEnd < DateTime.UtcNow);
    }

    /// <summary>
    /// 验证密码
    /// </summary>
    /// <param name="password">密码</param>
    /// <returns>是否验证成功</returns>
    public bool VerifyPassword(string password)
    {
        return Password.Verify(password);
    }
}
