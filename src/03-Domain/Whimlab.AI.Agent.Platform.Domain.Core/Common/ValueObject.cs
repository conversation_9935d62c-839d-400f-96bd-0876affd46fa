namespace Whimlab.AI.Agent.Platform.Domain.Core.Common;

/// <summary>
/// 值对象基类
/// </summary>
public abstract class ValueObject : IEquatable<ValueObject>
{
    /// <summary>
    /// 获取相等性组件
    /// </summary>
    /// <returns>相等性组件集合</returns>
    protected abstract IEnumerable<object?> GetEqualityComponents();

    /// <summary>
    /// 判断是否相等
    /// </summary>
    /// <param name="other">其他值对象</param>
    /// <returns>是否相等</returns>
    public bool Equals(ValueObject? other)
    {
        if (other is null) return false;
        if (ReferenceEquals(this, other)) return true;
        if (GetType() != other.GetType()) return false;

        return GetEqualityComponents().SequenceEqual(other.GetEqualityComponents());
    }

    /// <summary>
    /// 判断是否相等
    /// </summary>
    /// <param name="obj">其他对象</param>
    /// <returns>是否相等</returns>
    public override bool Equals(object? obj)
    {
        return obj is ValueObject valueObject && Equals(valueObject);
    }

    /// <summary>
    /// 获取哈希码
    /// </summary>
    /// <returns>哈希码</returns>
    public override int GetHashCode()
    {
        return GetEqualityComponents()
            .Where(x => x != null)
            .Aggregate(1, (current, obj) => current * 23 + obj!.GetHashCode());
    }

    /// <summary>
    /// 相等运算符
    /// </summary>
    /// <param name="left">左操作数</param>
    /// <param name="right">右操作数</param>
    /// <returns>是否相等</returns>
    public static bool operator ==(ValueObject? left, ValueObject? right)
    {
        return Equals(left, right);
    }

    /// <summary>
    /// 不等运算符
    /// </summary>
    /// <param name="left">左操作数</param>
    /// <param name="right">右操作数</param>
    /// <returns>是否不等</returns>
    public static bool operator !=(ValueObject? left, ValueObject? right)
    {
        return !Equals(left, right);
    }

    /// <summary>
    /// 创建值对象的副本
    /// </summary>
    /// <returns>值对象副本</returns>
    public virtual ValueObject Copy()
    {
        return (ValueObject)MemberwiseClone();
    }

    /// <summary>
    /// 转换为字符串
    /// </summary>
    /// <returns>字符串表示</returns>
    public override string ToString()
    {
        var components = GetEqualityComponents()
            .Where(x => x != null)
            .Select(x => x!.ToString());
        
        return $"{GetType().Name}({string.Join(", ", components)})";
    }
}

/// <summary>
/// 单值对象基类
/// </summary>
/// <typeparam name="T">值类型</typeparam>
public abstract class SingleValueObject<T> : ValueObject
{
    /// <summary>
    /// 值
    /// </summary>
    public T Value { get; }

    /// <summary>
    /// 初始化单值对象
    /// </summary>
    /// <param name="value">值</param>
    protected SingleValueObject(T value)
    {
        Value = value;
    }

    /// <summary>
    /// 获取相等性组件
    /// </summary>
    /// <returns>相等性组件集合</returns>
    protected override IEnumerable<object?> GetEqualityComponents()
    {
        yield return Value;
    }

    /// <summary>
    /// 转换为字符串
    /// </summary>
    /// <returns>字符串表示</returns>
    public override string ToString()
    {
        return Value?.ToString() ?? string.Empty;
    }

    /// <summary>
    /// 隐式转换为值类型
    /// </summary>
    /// <param name="valueObject">值对象</param>
    /// <returns>值</returns>
    public static implicit operator T(SingleValueObject<T> valueObject)
    {
        return valueObject.Value;
    }
}

/// <summary>
/// 枚举值对象基类
/// </summary>
/// <typeparam name="T">枚举类型</typeparam>
public abstract class Enumeration<T> : ValueObject, IComparable<Enumeration<T>>
    where T : Enumeration<T>
{
    /// <summary>
    /// 标识符
    /// </summary>
    public int Id { get; }

    /// <summary>
    /// 名称
    /// </summary>
    public string Name { get; }

    /// <summary>
    /// 初始化枚举值对象
    /// </summary>
    /// <param name="id">标识符</param>
    /// <param name="name">名称</param>
    protected Enumeration(int id, string name)
    {
        Id = id;
        Name = name;
    }

    /// <summary>
    /// 获取相等性组件
    /// </summary>
    /// <returns>相等性组件集合</returns>
    protected override IEnumerable<object?> GetEqualityComponents()
    {
        yield return Id;
        yield return Name;
    }

    /// <summary>
    /// 转换为字符串
    /// </summary>
    /// <returns>字符串表示</returns>
    public override string ToString()
    {
        return Name;
    }

    /// <summary>
    /// 获取所有枚举值
    /// </summary>
    /// <returns>所有枚举值</returns>
    public static IEnumerable<T> GetAll()
    {
        var type = typeof(T);
        var fields = type.GetFields(System.Reflection.BindingFlags.Public |
                                   System.Reflection.BindingFlags.Static |
                                   System.Reflection.BindingFlags.DeclaredOnly);

        foreach (var info in fields)
        {
            if (info.GetValue(null) is T locatedValue)
            {
                yield return locatedValue;
            }
        }
    }

    /// <summary>
    /// 根据标识符获取枚举值
    /// </summary>
    /// <param name="id">标识符</param>
    /// <returns>枚举值</returns>
    public static T? FromId(int id)
    {
        return GetAll().FirstOrDefault(item => item.Id == id);
    }

    /// <summary>
    /// 根据名称获取枚举值
    /// </summary>
    /// <param name="name">名称</param>
    /// <returns>枚举值</returns>
    public static T? FromName(string name)
    {
        return GetAll().FirstOrDefault(item => string.Equals(item.Name, name, StringComparison.OrdinalIgnoreCase));
    }

    /// <summary>
    /// 比较
    /// </summary>
    /// <param name="other">其他枚举值</param>
    /// <returns>比较结果</returns>
    public int CompareTo(Enumeration<T>? other)
    {
        if (other is null) return 1;
        return Id.CompareTo(other.Id);
    }

    /// <summary>
    /// 小于运算符
    /// </summary>
    /// <param name="left">左操作数</param>
    /// <param name="right">右操作数</param>
    /// <returns>是否小于</returns>
    public static bool operator <(Enumeration<T>? left, Enumeration<T>? right)
    {
        return left?.CompareTo(right) < 0;
    }

    /// <summary>
    /// 小于等于运算符
    /// </summary>
    /// <param name="left">左操作数</param>
    /// <param name="right">右操作数</param>
    /// <returns>是否小于等于</returns>
    public static bool operator <=(Enumeration<T>? left, Enumeration<T>? right)
    {
        return left?.CompareTo(right) <= 0;
    }

    /// <summary>
    /// 大于运算符
    /// </summary>
    /// <param name="left">左操作数</param>
    /// <param name="right">右操作数</param>
    /// <returns>是否大于</returns>
    public static bool operator >(Enumeration<T>? left, Enumeration<T>? right)
    {
        return left?.CompareTo(right) > 0;
    }

    /// <summary>
    /// 大于等于运算符
    /// </summary>
    /// <param name="left">左操作数</param>
    /// <param name="right">右操作数</param>
    /// <returns>是否大于等于</returns>
    public static bool operator >=(Enumeration<T>? left, Enumeration<T>? right)
    {
        return left?.CompareTo(right) >= 0;
    }
}
