using Whimlab.AI.Agent.Platform.Domain.Core.Common;
using Whimlab.AI.Agent.Platform.Domain.Conversation.Events;
using Whimlab.AI.Agent.Platform.Shared.Common.Enums;

namespace Whimlab.AI.Agent.Platform.Domain.Conversation.Entities;

/// <summary>
/// 对话聚合根
/// </summary>
public class Conversation : AggregateRoot
{
    private readonly List<Message> _messages = new();

    /// <summary>
    /// 对话标题
    /// </summary>
    public string Title { get; private set; } = string.Empty;

    /// <summary>
    /// 用户ID
    /// </summary>
    public Guid UserId { get; private set; }

    /// <summary>
    /// 智能体ID
    /// </summary>
    public Guid AgentId { get; private set; }

    /// <summary>
    /// 对话状态
    /// </summary>
    public ConversationStatus Status { get; private set; }

    /// <summary>
    /// 消息总数
    /// </summary>
    public int MessageCount { get; private set; }

    /// <summary>
    /// 总Token消耗
    /// </summary>
    public long TotalTokensUsed { get; private set; }

    /// <summary>
    /// 最后活跃时间
    /// </summary>
    public DateTime LastActiveAt { get; private set; }

    /// <summary>
    /// 归档时间
    /// </summary>
    public DateTime? ArchivedAt { get; private set; }

    /// <summary>
    /// 对话上下文（JSON格式）
    /// </summary>
    public string? Context { get; private set; }

    /// <summary>
    /// 消息列表（只读）
    /// </summary>
    public IReadOnlyList<Message> Messages => _messages.AsReadOnly();

    /// <summary>
    /// 私有构造函数（用于EF Core）
    /// </summary>
    private Conversation() { }

    /// <summary>
    /// 创建新对话
    /// </summary>
    /// <param name="title">对话标题</param>
    /// <param name="userId">用户ID</param>
    /// <param name="agentId">智能体ID</param>
    /// <param name="context">对话上下文</param>
    /// <returns>对话实体</returns>
    public static Conversation Create(string title, Guid userId, Guid agentId, string? context = null)
    {
        if (string.IsNullOrWhiteSpace(title))
            throw new ArgumentException("对话标题不能为空", nameof(title));

        if (userId == Guid.Empty)
            throw new ArgumentException("用户ID不能为空", nameof(userId));

        if (agentId == Guid.Empty)
            throw new ArgumentException("智能体ID不能为空", nameof(agentId));

        var conversation = new Conversation
        {
            Title = title.Trim(),
            UserId = userId,
            AgentId = agentId,
            Status = ConversationStatus.Active,
            MessageCount = 0,
            TotalTokensUsed = 0,
            LastActiveAt = DateTime.UtcNow,
            Context = context
        };

        conversation.AddDomainEvent(new ConversationCreatedEvent(conversation.Id, userId, agentId, title));

        return conversation;
    }

    /// <summary>
    /// 添加用户消息
    /// </summary>
    /// <param name="content">消息内容</param>
    /// <param name="attachments">附件</param>
    /// <returns>消息实体</returns>
    public Message AddUserMessage(string content, IEnumerable<MessageAttachment>? attachments = null)
    {
        if (Status != ConversationStatus.Active)
            throw new InvalidOperationException("只有活跃状态的对话才能添加消息");

        if (string.IsNullOrWhiteSpace(content))
            throw new ArgumentException("消息内容不能为空", nameof(content));

        var message = Message.CreateUserMessage(Id, content, attachments);
        _messages.Add(message);
        
        MessageCount++;
        LastActiveAt = DateTime.UtcNow;
        MarkAsModified();

        AddDomainEvent(new MessageAddedEvent(Id, message.Id, SenderType.User, content.Length));

        return message;
    }

    /// <summary>
    /// 添加智能体消息
    /// </summary>
    /// <param name="content">消息内容</param>
    /// <param name="tokensUsed">使用的Token数量</param>
    /// <param name="modelInfo">模型信息</param>
    /// <param name="attachments">附件</param>
    /// <returns>消息实体</returns>
    public Message AddAgentMessage(string content, int tokensUsed = 0, string? modelInfo = null, IEnumerable<MessageAttachment>? attachments = null)
    {
        if (Status != ConversationStatus.Active)
            throw new InvalidOperationException("只有活跃状态的对话才能添加消息");

        if (string.IsNullOrWhiteSpace(content))
            throw new ArgumentException("消息内容不能为空", nameof(content));

        var message = Message.CreateAgentMessage(Id, content, tokensUsed, modelInfo, attachments);
        _messages.Add(message);
        
        MessageCount++;
        TotalTokensUsed += tokensUsed;
        LastActiveAt = DateTime.UtcNow;
        MarkAsModified();

        AddDomainEvent(new MessageAddedEvent(Id, message.Id, SenderType.Agent, content.Length));
        
        if (tokensUsed > 0)
        {
            AddDomainEvent(new TokensConsumedEvent(Id, UserId, AgentId, tokensUsed));
        }

        return message;
    }

    /// <summary>
    /// 添加系统消息
    /// </summary>
    /// <param name="content">消息内容</param>
    /// <param name="messageType">消息类型</param>
    /// <returns>消息实体</returns>
    public Message AddSystemMessage(string content, string? messageType = null)
    {
        if (string.IsNullOrWhiteSpace(content))
            throw new ArgumentException("消息内容不能为空", nameof(content));

        var message = Message.CreateSystemMessage(Id, content, messageType);
        _messages.Add(message);
        
        MessageCount++;
        LastActiveAt = DateTime.UtcNow;
        MarkAsModified();

        AddDomainEvent(new MessageAddedEvent(Id, message.Id, SenderType.System, content.Length));

        return message;
    }

    /// <summary>
    /// 更新对话标题
    /// </summary>
    /// <param name="title">新标题</param>
    public void UpdateTitle(string title)
    {
        if (string.IsNullOrWhiteSpace(title))
            throw new ArgumentException("对话标题不能为空", nameof(title));

        Title = title.Trim();
        MarkAsModified();

        AddDomainEvent(new ConversationTitleUpdatedEvent(Id, Title));
    }

    /// <summary>
    /// 更新对话上下文
    /// </summary>
    /// <param name="context">对话上下文</param>
    public void UpdateContext(string? context)
    {
        Context = context;
        MarkAsModified();
    }

    /// <summary>
    /// 归档对话
    /// </summary>
    public void Archive()
    {
        if (Status == ConversationStatus.ArchivedByUser)
            return;

        Status = ConversationStatus.ArchivedByUser;
        ArchivedAt = DateTime.UtcNow;
        MarkAsModified();

        AddDomainEvent(new ConversationArchivedEvent(Id, UserId, ConversationStatus.ArchivedByUser));
    }

    /// <summary>
    /// 系统自动归档
    /// </summary>
    public void AutoArchive()
    {
        if (Status == ConversationStatus.AutoArchivedSystem)
            return;

        Status = ConversationStatus.AutoArchivedSystem;
        ArchivedAt = DateTime.UtcNow;
        MarkAsModified();

        AddDomainEvent(new ConversationArchivedEvent(Id, UserId, ConversationStatus.AutoArchivedSystem));
    }

    /// <summary>
    /// 恢复对话
    /// </summary>
    public void Restore()
    {
        if (Status == ConversationStatus.Active)
            return;

        Status = ConversationStatus.Active;
        ArchivedAt = null;
        LastActiveAt = DateTime.UtcNow;
        MarkAsModified();

        AddDomainEvent(new ConversationRestoredEvent(Id, UserId));
    }

    /// <summary>
    /// 删除消息
    /// </summary>
    /// <param name="messageId">消息ID</param>
    public void DeleteMessage(Guid messageId)
    {
        var message = _messages.FirstOrDefault(m => m.Id == messageId);
        if (message == null)
            throw new InvalidOperationException($"消息 {messageId} 不存在");

        message.Delete();
        MessageCount--;
        
        if (message.SenderType == SenderType.Agent && message.TokensUsed > 0)
        {
            TotalTokensUsed -= message.TokensUsed;
        }
        
        MarkAsModified();

        AddDomainEvent(new MessageDeletedEvent(Id, messageId, message.SenderType));
    }

    /// <summary>
    /// 获取最近的消息
    /// </summary>
    /// <param name="count">消息数量</param>
    /// <returns>最近的消息列表</returns>
    public IEnumerable<Message> GetRecentMessages(int count = 10)
    {
        return _messages
            .Where(m => !m.IsDeleted)
            .OrderByDescending(m => m.CreatedAt)
            .Take(count)
            .OrderBy(m => m.CreatedAt);
    }

    /// <summary>
    /// 获取对话摘要
    /// </summary>
    /// <returns>对话摘要</returns>
    public string GetSummary()
    {
        var recentMessages = GetRecentMessages(5);
        var preview = string.Join(" ", recentMessages.Select(m => m.Content.Length > 50 ? m.Content[..50] + "..." : m.Content));
        return string.IsNullOrEmpty(preview) ? Title : preview;
    }

    /// <summary>
    /// 检查是否可以添加消息
    /// </summary>
    /// <returns>是否可以添加消息</returns>
    public bool CanAddMessage()
    {
        return Status == ConversationStatus.Active;
    }

    /// <summary>
    /// 检查是否需要自动归档
    /// </summary>
    /// <param name="inactiveThreshold">非活跃阈值</param>
    /// <returns>是否需要自动归档</returns>
    public bool ShouldAutoArchive(TimeSpan inactiveThreshold)
    {
        return Status == ConversationStatus.Active && 
               DateTime.UtcNow - LastActiveAt > inactiveThreshold;
    }
}
