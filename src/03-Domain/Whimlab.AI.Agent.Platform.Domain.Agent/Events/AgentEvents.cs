using MediatR;
using Whimlab.AI.Agent.Platform.Shared.Common.Enums;

namespace Whimlab.AI.Agent.Platform.Domain.Agent.Events;

/// <summary>
/// 智能体创建事件
/// </summary>
/// <param name="AgentId">智能体ID</param>
/// <param name="Name">智能体名称</param>
/// <param name="AgentType">智能体类型</param>
/// <param name="CreatedBy">创建者ID</param>
public record AgentCreatedEvent(Guid AgentId, string Name, AgentType AgentType, Guid CreatedBy) : INotification;

/// <summary>
/// 智能体更新事件
/// </summary>
/// <param name="AgentId">智能体ID</param>
/// <param name="Name">智能体名称</param>
/// <param name="Description">智能体描述</param>
public record AgentUpdatedEvent(Guid AgentId, string Name, string Description) : INotification;

/// <summary>
/// 智能体配置更新事件
/// </summary>
/// <param name="AgentId">智能体ID</param>
/// <param name="Version">新版本号</param>
public record AgentConfigurationUpdatedEvent(Guid AgentId, string Version) : INotification;

/// <summary>
/// 智能体发布事件
/// </summary>
/// <param name="AgentId">智能体ID</param>
/// <param name="Name">智能体名称</param>
public record AgentPublishedEvent(Guid AgentId, string Name) : INotification;

/// <summary>
/// 智能体归档事件
/// </summary>
/// <param name="AgentId">智能体ID</param>
/// <param name="Name">智能体名称</param>
public record AgentArchivedEvent(Guid AgentId, string Name) : INotification;

/// <summary>
/// 智能体恢复事件
/// </summary>
/// <param name="AgentId">智能体ID</param>
/// <param name="Name">智能体名称</param>
public record AgentRestoredEvent(Guid AgentId, string Name) : INotification;

/// <summary>
/// 智能体使用事件
/// </summary>
/// <param name="AgentId">智能体ID</param>
/// <param name="UsageCount">使用次数</param>
public record AgentUsedEvent(Guid AgentId, long UsageCount) : INotification;

/// <summary>
/// 智能体评分事件
/// </summary>
/// <param name="AgentId">智能体ID</param>
/// <param name="Score">评分</param>
/// <param name="AverageRating">平均评分</param>
public record AgentRatedEvent(Guid AgentId, int Score, double AverageRating) : INotification;

/// <summary>
/// 智能体删除事件
/// </summary>
/// <param name="AgentId">智能体ID</param>
/// <param name="Name">智能体名称</param>
public record AgentDeletedEvent(Guid AgentId, string Name) : INotification;
