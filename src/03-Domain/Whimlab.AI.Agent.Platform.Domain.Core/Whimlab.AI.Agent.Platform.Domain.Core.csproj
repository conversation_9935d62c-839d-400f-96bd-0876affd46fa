<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <RootNamespace>Whimlab.AI.Agent.Platform.Domain.Core</RootNamespace>
    <AssemblyName>Whimlab.AI.Agent.Platform.Domain.Core</AssemblyName>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="../../05-Shared/Whimlab.AI.Agent.Platform.Shared.Common/Whimlab.AI.Agent.Platform.Shared.Common.csproj" />
    <ProjectReference Include="../../05-Shared/Whimlab.AI.Agent.Platform.Shared.Exceptions/Whimlab.AI.Agent.Platform.Shared.Exceptions.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="MediatR" />
  </ItemGroup>

</Project>
