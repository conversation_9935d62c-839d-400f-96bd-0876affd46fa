using Microsoft.Extensions.Logging;
using Whimlab.AI.Agent.Platform.Domain.Subscription.Entities;
using Whimlab.AI.Agent.Platform.Domain.Subscription.Repositories;
using Whimlab.AI.Agent.Platform.Domain.Subscription.Services;
using Whimlab.AI.Agent.Platform.Shared.Common.Enums;
using TrendDirection = Whimlab.AI.Agent.Platform.Domain.Subscription.Services.TrendDirection;

namespace Whimlab.AI.Agent.Platform.Application.Subscription.Services;

/// <summary>
/// 配额管理服务实现
/// 负责配额检查、使用、重置等核心业务逻辑
/// </summary>
public class QuotaManagementService : IQuotaManagementService
{
    private readonly ICustomerSubscriptionRepository _subscriptionRepository;
    private readonly ISubscriptionPlanRepository _planRepository;
    private readonly ILogger<QuotaManagementService> _logger;

    /// <summary>
    /// 配额警告阈值常量
    /// </summary>
    private const double DefaultWarningThreshold = 0.8; // 80%
    private const double CriticalWarningThreshold = 0.95; // 95%

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="subscriptionRepository">客户订阅仓储</param>
    /// <param name="planRepository">订阅计划仓储</param>
    /// <param name="logger">日志记录器</param>
    public QuotaManagementService(
        ICustomerSubscriptionRepository subscriptionRepository,
        ISubscriptionPlanRepository planRepository,
        ILogger<QuotaManagementService> logger)
    {
        _subscriptionRepository = subscriptionRepository ?? throw new ArgumentNullException(nameof(subscriptionRepository));
        _planRepository = planRepository ?? throw new ArgumentNullException(nameof(planRepository));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// 检查配额是否足够
    /// </summary>
    /// <param name="customerId">客户ID</param>
    /// <param name="quotaType">配额类型</param>
    /// <param name="requestedAmount">请求使用量</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>配额检查结果</returns>
    public async Task<QuotaCheckResult> CheckQuotaAsync(Guid customerId, string quotaType, long requestedAmount, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("开始检查客户 {CustomerId} 的配额 {QuotaType}，请求使用量: {RequestedAmount}", 
                customerId, quotaType, requestedAmount);

            // 获取客户的活跃订阅
            var subscription = await _subscriptionRepository.GetActiveByCustomerIdAsync(customerId, cancellationToken);
            if (subscription == null)
            {
                _logger.LogWarning("客户 {CustomerId} 没有活跃的订阅", customerId);
                return new QuotaCheckResult
                {
                    IsAllowed = false,
                    DenialReason = "客户没有活跃的订阅",
                    SuggestedActions = { "请先购买订阅计划" }
                };
            }

            // 获取订阅计划
            var plan = await _planRepository.GetByIdAsync(subscription.SubscriptionPlanId, cancellationToken);
            if (plan == null)
            {
                _logger.LogError("找不到订阅计划 {PlanId}", subscription.SubscriptionPlanId);
                return new QuotaCheckResult
                {
                    IsAllowed = false,
                    DenialReason = "订阅计划不存在",
                    SuggestedActions = { "请联系客服" }
                };
            }

            // 获取配额限制
            var quotaLimit = plan.Quotas.FirstOrDefault(q => q.QuotaType == quotaType);
            if (quotaLimit == null)
            {
                _logger.LogWarning("订阅计划 {PlanId} 中没有配额类型 {QuotaType}", plan.Id, quotaType);
                return new QuotaCheckResult
                {
                    IsAllowed = false,
                    DenialReason = $"当前订阅计划不支持 {quotaType} 配额",
                    SuggestedActions = { "请升级到支持此功能的计划" }
                };
            }

            // 检查是否无限制配额
            if (quotaLimit.Limit == 0) // 0表示无限制
            {
                return new QuotaCheckResult
                {
                    IsAllowed = true,
                    CurrentUsage = 0,
                    QuotaLimit = long.MaxValue,
                    RemainingQuota = long.MaxValue
                };
            }

            // 计算当前使用量
            var currentUsage = subscription.GetCurrentQuotaUsage(quotaType);
            var remainingQuota = quotaLimit.Limit - currentUsage;

            // 检查是否有足够的配额
            var isAllowed = remainingQuota >= requestedAmount;
            var result = new QuotaCheckResult
            {
                IsAllowed = isAllowed,
                CurrentUsage = currentUsage,
                QuotaLimit = quotaLimit.Limit,
                RemainingQuota = remainingQuota
            };

            if (!isAllowed)
            {
                result.DenialReason = $"配额不足，当前剩余: {remainingQuota}，请求: {requestedAmount}";
                result.SuggestedActions.Add("请等待配额重置或升级订阅计划");
                
                // 如果接近配额限制，建议升级
                if (currentUsage > quotaLimit.Limit * 0.9)
                {
                    result.SuggestedActions.Add("建议升级到更高级的订阅计划");
                }
            }
            else
            {
                // 检查是否需要警告
                var usageAfterRequest = currentUsage + requestedAmount;
                var usageRate = (double)usageAfterRequest / quotaLimit.Limit;
                
                if (usageRate >= CriticalWarningThreshold)
                {
                    result.SuggestedActions.Add("配额即将耗尽，建议升级订阅计划");
                }
                else if (usageRate >= DefaultWarningThreshold)
                {
                    result.SuggestedActions.Add("配额使用量较高，请注意监控");
                }
            }

            _logger.LogInformation("配额检查完成，客户 {CustomerId}，配额类型 {QuotaType}，结果: {IsAllowed}", 
                customerId, quotaType, isAllowed);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "检查配额时发生错误，客户 {CustomerId}，配额类型 {QuotaType}", customerId, quotaType);
            return new QuotaCheckResult
            {
                IsAllowed = false,
                DenialReason = "系统错误，请稍后重试",
                SuggestedActions = { "请联系技术支持" }
            };
        }
    }

    /// <summary>
    /// 使用配额
    /// </summary>
    /// <param name="customerId">客户ID</param>
    /// <param name="quotaType">配额类型</param>
    /// <param name="amount">使用量</param>
    /// <param name="description">使用描述</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>配额使用结果</returns>
    public async Task<QuotaUsageResult> UseQuotaAsync(Guid customerId, string quotaType, long amount, string? description = null, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("开始使用配额，客户 {CustomerId}，配额类型 {QuotaType}，使用量: {Amount}", 
                customerId, quotaType, amount);

            // 先检查配额是否足够
            var checkResult = await CheckQuotaAsync(customerId, quotaType, amount, cancellationToken);
            if (!checkResult.IsAllowed)
            {
                return new QuotaUsageResult
                {
                    IsSuccess = false,
                    ErrorMessage = checkResult.DenialReason,
                    RemainingQuota = checkResult.RemainingQuota
                };
            }

            // 获取客户订阅
            var subscription = await _subscriptionRepository.GetActiveByCustomerIdAsync(customerId, cancellationToken);
            if (subscription == null)
            {
                return new QuotaUsageResult
                {
                    IsSuccess = false,
                    ErrorMessage = "客户没有活跃的订阅"
                };
            }

            // 使用配额
            var success = subscription.UseQuota(quotaType, amount, description);
            if (!success)
            {
                return new QuotaUsageResult
                {
                    IsSuccess = false,
                    ErrorMessage = "配额使用失败"
                };
            }

            // 保存更改
            await _subscriptionRepository.UpdateAsync(subscription, cancellationToken);

            // 计算新的累计使用量和剩余配额
            var newCumulativeUsage = subscription.GetCurrentQuotaUsage(quotaType);
            var newRemainingQuota = checkResult.QuotaLimit - newCumulativeUsage;

            // 检查是否需要警告
            var usageRate = (double)newCumulativeUsage / checkResult.QuotaLimit;
            var result = new QuotaUsageResult
            {
                IsSuccess = true,
                NewCumulativeUsage = newCumulativeUsage,
                RemainingQuota = newRemainingQuota
            };

            if (usageRate >= CriticalWarningThreshold)
            {
                result.TriggeredWarning = true;
                result.WarningMessage = "配额使用量已达到95%，即将耗尽";
            }
            else if (usageRate >= DefaultWarningThreshold)
            {
                result.TriggeredWarning = true;
                result.WarningMessage = "配额使用量已达到80%，请注意监控";
            }

            _logger.LogInformation("配额使用成功，客户 {CustomerId}，配额类型 {QuotaType}，新累计使用量: {NewUsage}", 
                customerId, quotaType, newCumulativeUsage);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "使用配额时发生错误，客户 {CustomerId}，配额类型 {QuotaType}", customerId, quotaType);
            return new QuotaUsageResult
            {
                IsSuccess = false,
                ErrorMessage = "系统错误，请稍后重试"
            };
        }
    }

    /// <summary>
    /// 获取客户的配额状态
    /// </summary>
    /// <param name="customerId">客户ID</param>
    /// <param name="quotaType">配额类型（可选）</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>配额状态</returns>
    public async Task<CustomerQuotaStatus> GetQuotaStatusAsync(Guid customerId, string? quotaType = null, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("获取客户 {CustomerId} 的配额状态", customerId);

            // 获取客户的活跃订阅
            var subscription = await _subscriptionRepository.GetActiveByCustomerIdAsync(customerId, cancellationToken);
            if (subscription == null)
            {
                throw new InvalidOperationException($"客户 {customerId} 没有活跃的订阅");
            }

            // 获取订阅计划
            var plan = await _planRepository.GetByIdAsync(subscription.SubscriptionPlanId, cancellationToken);
            if (plan == null)
            {
                throw new InvalidOperationException($"找不到订阅计划 {subscription.SubscriptionPlanId}");
            }

            var quotaStatus = new CustomerQuotaStatus
            {
                CustomerId = customerId,
                SubscriptionPlanId = subscription.SubscriptionPlanId,
                CurrentPeriodStart = subscription.CurrentPeriodStart,
                CurrentPeriodEnd = subscription.CurrentPeriodEnd,
                SubscriptionStatus = subscription.Status.ToString()
            };

            // 获取配额详情
            var quotasToProcess = string.IsNullOrEmpty(quotaType) 
                ? plan.Quotas 
                : plan.Quotas.Where(q => q.QuotaType == quotaType);

            foreach (var planQuota in quotasToProcess)
            {
                var currentUsage = subscription.GetCurrentQuotaUsage(planQuota.QuotaType);
                var isUnlimited = planQuota.Limit == 0;
                var remainingQuota = isUnlimited ? long.MaxValue : planQuota.Limit - currentUsage;
                var usageRate = isUnlimited ? 0 : (double)currentUsage / planQuota.Limit;

                var quotaDetail = new QuotaDetail
                {
                    QuotaType = planQuota.QuotaType,
                    Limit = isUnlimited ? long.MaxValue : planQuota.Limit,
                    CurrentUsage = currentUsage,
                    RemainingQuota = remainingQuota,
                    UsageRate = usageRate,
                    Period = planQuota.Period,
                    IsUnlimited = isUnlimited,
                    NextResetAt = CalculateNextResetTime(subscription.CurrentPeriodStart, planQuota.Period)
                };

                quotaStatus.QuotaDetails.Add(quotaDetail);
            }

            return quotaStatus;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取配额状态时发生错误，客户 {CustomerId}", customerId);
            throw;
        }
    }

    /// <summary>
    /// 重置客户配额
    /// </summary>
    /// <param name="customerId">客户ID</param>
    /// <param name="quotaType">配额类型</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>任务</returns>
    public async Task ResetQuotaAsync(Guid customerId, string quotaType, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("开始重置客户 {CustomerId} 的配额 {QuotaType}", customerId, quotaType);

            // 获取客户的活跃订阅
            var subscription = await _subscriptionRepository.GetActiveByCustomerIdAsync(customerId, cancellationToken);
            if (subscription == null)
            {
                _logger.LogWarning("客户 {CustomerId} 没有活跃的订阅，无法重置配额", customerId);
                return;
            }

            // 重置配额
            subscription.ResetQuota(quotaType);

            // 保存更改
            await _subscriptionRepository.UpdateAsync(subscription, cancellationToken);

            _logger.LogInformation("成功重置客户 {CustomerId} 的配额 {QuotaType}", customerId, quotaType);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "重置配额时发生错误，客户 {CustomerId}，配额类型 {QuotaType}", customerId, quotaType);
            throw;
        }
    }

    /// <summary>
    /// 批量重置配额（用于定时任务）
    /// </summary>
    /// <param name="quotaType">配额类型</param>
    /// <param name="resetPeriod">重置周期</param>
    /// <param name="batchSize">批次大小</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>重置的客户数量</returns>
    public async Task<int> BatchResetQuotaAsync(string quotaType, string resetPeriod, int batchSize = 1000, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("开始批量重置配额，配额类型: {QuotaType}，重置周期: {ResetPeriod}，批次大小: {BatchSize}",
                quotaType, resetPeriod, batchSize);

            var resetCount = 0;
            var currentDate = DateTime.UtcNow;

            // 计算需要重置的时间范围
            var resetThreshold = CalculateResetThreshold(currentDate, resetPeriod);
            if (resetThreshold == null)
            {
                _logger.LogWarning("无效的重置周期: {ResetPeriod}", resetPeriod);
                return 0;
            }

            // 分批获取需要重置的订阅
            var offset = 0;
            List<CustomerSubscription> subscriptions;

            do
            {
                subscriptions = await _subscriptionRepository.GetSubscriptionsForQuotaResetAsync(
                    quotaType, resetThreshold.Value, batchSize, offset, cancellationToken);

                foreach (var subscription in subscriptions)
                {
                    try
                    {
                        subscription.ResetQuota(quotaType);
                        await _subscriptionRepository.UpdateAsync(subscription, cancellationToken);
                        resetCount++;

                        _logger.LogDebug("重置客户 {CustomerId} 的配额 {QuotaType}",
                            subscription.CustomerId, quotaType);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "重置客户 {CustomerId} 配额时发生错误", subscription.CustomerId);
                        // 继续处理其他客户，不中断批量操作
                    }
                }

                offset += batchSize;

            } while (subscriptions.Count == batchSize && !cancellationToken.IsCancellationRequested);

            _logger.LogInformation("批量重置配额完成，配额类型: {QuotaType}，重置数量: {ResetCount}",
                quotaType, resetCount);

            return resetCount;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "批量重置配额时发生错误，配额类型: {QuotaType}", quotaType);
            throw;
        }
    }

    /// <summary>
    /// 计算配额使用率
    /// </summary>
    /// <param name="customerId">客户ID</param>
    /// <param name="quotaType">配额类型</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>使用率（0-1）</returns>
    public async Task<double> CalculateUsageRateAsync(Guid customerId, string quotaType, CancellationToken cancellationToken = default)
    {
        try
        {
            var quotaStatus = await GetQuotaStatusAsync(customerId, quotaType, cancellationToken);
            var quotaDetail = quotaStatus.QuotaDetails.FirstOrDefault(q => q.QuotaType == quotaType);

            return quotaDetail?.UsageRate ?? 0.0;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "计算配额使用率时发生错误，客户 {CustomerId}，配额类型 {QuotaType}", customerId, quotaType);
            return 0.0;
        }
    }

    /// <summary>
    /// 计算重置阈值时间
    /// </summary>
    /// <param name="currentDate">当前时间</param>
    /// <param name="resetPeriod">重置周期</param>
    /// <returns>重置阈值时间</returns>
    private static DateTime? CalculateResetThreshold(DateTime currentDate, string resetPeriod)
    {
        return resetPeriod.ToLowerInvariant() switch
        {
            "daily" => currentDate.Date,
            "weekly" => currentDate.Date.AddDays(-(int)currentDate.DayOfWeek),
            "monthly" => new DateTime(currentDate.Year, currentDate.Month, 1),
            "yearly" => new DateTime(currentDate.Year, 1, 1),
            _ => null
        };
    }

    /// <summary>
    /// 预测配额耗尽时间
    /// </summary>
    /// <param name="customerId">客户ID</param>
    /// <param name="quotaType">配额类型</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>预计耗尽时间</returns>
    public async Task<DateTime?> PredictQuotaExhaustionAsync(Guid customerId, string quotaType, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("预测客户 {CustomerId} 配额 {QuotaType} 的耗尽时间", customerId, quotaType);

            // 获取使用趋势
            var trend = await GetUsageTrendAsync(customerId, quotaType, 30, cancellationToken);
            if (trend.AverageDailyUsage <= 0)
            {
                return null; // 没有使用趋势，无法预测
            }

            // 获取当前配额状态
            var quotaStatus = await GetQuotaStatusAsync(customerId, quotaType, cancellationToken);
            var quotaDetail = quotaStatus.QuotaDetails.FirstOrDefault(q => q.QuotaType == quotaType);

            if (quotaDetail == null || quotaDetail.IsUnlimited)
            {
                return null; // 无限制配额不会耗尽
            }

            // 计算预计耗尽天数
            var remainingQuota = quotaDetail.RemainingQuota;
            if (remainingQuota <= 0)
            {
                return DateTime.UtcNow; // 已经耗尽
            }

            var daysToExhaustion = remainingQuota / trend.AverageDailyUsage;
            var exhaustionDate = DateTime.UtcNow.AddDays(daysToExhaustion);

            // 确保不超过当前周期结束时间
            if (quotaDetail.NextResetAt.HasValue && exhaustionDate > quotaDetail.NextResetAt.Value)
            {
                return null; // 在配额重置前不会耗尽
            }

            return exhaustionDate;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "预测配额耗尽时间时发生错误，客户 {CustomerId}，配额类型 {QuotaType}", customerId, quotaType);
            return null;
        }
    }

    /// <summary>
    /// 获取配额使用趋势
    /// </summary>
    /// <param name="customerId">客户ID</param>
    /// <param name="quotaType">配额类型</param>
    /// <param name="days">天数</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>使用趋势</returns>
    public async Task<QuotaUsageTrend> GetUsageTrendAsync(Guid customerId, string quotaType, int days = 30, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("获取客户 {CustomerId} 配额 {QuotaType} 的使用趋势，天数: {Days}",
                customerId, quotaType, days);

            var endDate = DateTime.UtcNow.Date;
            var startDate = endDate.AddDays(-days);

            // 获取使用统计
            var statistics = await _subscriptionRepository.GetQuotaUsageStatisticsAsync(
                customerId, quotaType, startDate, endDate, cancellationToken);

            var trend = new QuotaUsageTrend
            {
                CustomerId = customerId,
                QuotaType = quotaType,
                DailyUsage = statistics.UsageByDate,
                AverageDailyUsage = statistics.AverageDailyUsage,
                PeakUsageDate = statistics.UsageByDate.OrderByDescending(kvp => kvp.Value).FirstOrDefault().Key,
                PeakUsageAmount = statistics.PeakDailyUsage
            };

            // 计算趋势方向和变化率
            CalculateTrendDirection(trend);

            return trend;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取配额使用趋势时发生错误，客户 {CustomerId}，配额类型 {QuotaType}", customerId, quotaType);
            return new QuotaUsageTrend
            {
                CustomerId = customerId,
                QuotaType = quotaType,
                Trend = TrendDirection.Stable
            };
        }
    }

    /// <summary>
    /// 检查是否需要配额警告
    /// </summary>
    /// <param name="customerId">客户ID</param>
    /// <param name="quotaType">配额类型</param>
    /// <param name="warningThreshold">警告阈值（0-1）</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>是否需要警告</returns>
    public async Task<bool> ShouldWarnAboutQuotaAsync(Guid customerId, string quotaType, double warningThreshold = 0.8, CancellationToken cancellationToken = default)
    {
        try
        {
            var usageRate = await CalculateUsageRateAsync(customerId, quotaType, cancellationToken);
            return usageRate >= warningThreshold;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "检查配额警告时发生错误，客户 {CustomerId}，配额类型 {QuotaType}", customerId, quotaType);
            return false;
        }
    }

    /// <summary>
    /// 获取超限的客户列表
    /// </summary>
    /// <param name="quotaType">配额类型</param>
    /// <param name="batchSize">批次大小</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>超限客户列表</returns>
    public async Task<IEnumerable<Guid>> GetOverLimitCustomersAsync(string quotaType, int batchSize = 100, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("获取配额 {QuotaType} 超限的客户列表，批次大小: {BatchSize}", quotaType, batchSize);

            return await _subscriptionRepository.GetOverLimitCustomersAsync(quotaType, batchSize, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取超限客户列表时发生错误，配额类型: {QuotaType}", quotaType);
            return Enumerable.Empty<Guid>();
        }
    }

    /// <summary>
    /// 计算趋势方向
    /// </summary>
    /// <param name="trend">使用趋势对象</param>
    private static void CalculateTrendDirection(QuotaUsageTrend trend)
    {
        if (trend.DailyUsage.Count < 2)
        {
            trend.Trend = TrendDirection.Stable;
            trend.TrendRate = 0;
            return;
        }

        var sortedUsage = trend.DailyUsage.OrderBy(kvp => kvp.Key).ToList();
        var firstHalf = sortedUsage.Take(sortedUsage.Count / 2).Average(kvp => kvp.Value);
        var secondHalf = sortedUsage.Skip(sortedUsage.Count / 2).Average(kvp => kvp.Value);

        var changeRate = firstHalf > 0 ? (secondHalf - firstHalf) / firstHalf : 0;
        trend.TrendRate = changeRate;

        if (Math.Abs(changeRate) < 0.1) // 变化小于10%认为是稳定
        {
            trend.Trend = TrendDirection.Stable;
        }
        else if (changeRate > 0)
        {
            trend.Trend = TrendDirection.Increasing;
        }
        else
        {
            trend.Trend = TrendDirection.Decreasing;
        }
    }

    /// <summary>
    /// 升级客户订阅计划
    /// </summary>
    /// <param name="customerId">客户ID</param>
    /// <param name="newPlanId">新计划ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>升级结果</returns>
    public async Task<SubscriptionUpgradeResult> UpgradeSubscriptionAsync(Guid customerId, Guid newPlanId, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("开始升级客户 {CustomerId} 的订阅计划到 {NewPlanId}", customerId, newPlanId);

            // 获取当前订阅
            var currentSubscription = await _subscriptionRepository.GetActiveByCustomerIdAsync(customerId, cancellationToken);
            if (currentSubscription == null)
            {
                return new SubscriptionUpgradeResult
                {
                    IsSuccess = false,
                    ErrorMessage = "客户没有活跃的订阅"
                };
            }

            // 获取新计划
            var newPlan = await _planRepository.GetByIdAsync(newPlanId, cancellationToken);
            if (newPlan == null)
            {
                return new SubscriptionUpgradeResult
                {
                    IsSuccess = false,
                    ErrorMessage = "新订阅计划不存在"
                };
            }

            // 获取当前计划
            var currentPlan = await _planRepository.GetByIdAsync(currentSubscription.SubscriptionPlanId, cancellationToken);
            if (currentPlan == null)
            {
                return new SubscriptionUpgradeResult
                {
                    IsSuccess = false,
                    ErrorMessage = "当前订阅计划不存在"
                };
            }

            // 执行升级（由于CustomerSubscription实体没有UpgradePlan方法，这里暂时简化实现）
            // 实际实现需要在CustomerSubscription实体中添加UpgradePlan方法
            _logger.LogWarning("订阅升级功能需要在CustomerSubscription实体中实现UpgradePlan方法");

            // 暂时返回成功，实际实现需要更新订阅计划ID和相关逻辑
            // currentSubscription.SubscriptionPlanId = newPlanId;

            // 保存更改
            await _subscriptionRepository.UpdateAsync(currentSubscription, cancellationToken);

            // 构建升级详情
            var upgradeDetails = new UpgradeDetails
            {
                OldPlanName = currentPlan.Name,
                NewPlanName = newPlan.Name,
                PriceDifference = newPlan.MonthlyPrice.Amount - currentPlan.MonthlyPrice.Amount,
                EffectiveDate = DateTime.UtcNow,
                QuotaChanges = CalculateQuotaChanges(currentPlan, newPlan)
            };

            _logger.LogInformation("成功升级客户 {CustomerId} 的订阅计划从 {OldPlan} 到 {NewPlan}",
                customerId, currentPlan.Name, newPlan.Name);

            return new SubscriptionUpgradeResult
            {
                IsSuccess = true,
                NewSubscriptionId = currentSubscription.Id,
                Details = upgradeDetails
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "升级订阅计划时发生错误，客户 {CustomerId}，新计划 {NewPlanId}", customerId, newPlanId);
            return new SubscriptionUpgradeResult
            {
                IsSuccess = false,
                ErrorMessage = "系统错误，请稍后重试"
            };
        }
    }

    /// <summary>
    /// 临时增加配额
    /// </summary>
    /// <param name="customerId">客户ID</param>
    /// <param name="quotaType">配额类型</param>
    /// <param name="additionalAmount">增加量</param>
    /// <param name="expiresAt">过期时间</param>
    /// <param name="reason">增加原因</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>任务</returns>
    public async Task AddTemporaryQuotaAsync(Guid customerId, string quotaType, long additionalAmount, DateTime expiresAt, string reason, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("为客户 {CustomerId} 临时增加配额 {QuotaType}，增加量: {Amount}，过期时间: {ExpiresAt}，原因: {Reason}",
                customerId, quotaType, additionalAmount, expiresAt, reason);

            // 获取客户订阅
            var subscription = await _subscriptionRepository.GetActiveByCustomerIdAsync(customerId, cancellationToken);
            if (subscription == null)
            {
                throw new InvalidOperationException($"客户 {customerId} 没有活跃的订阅");
            }

            // 添加临时配额（这里需要在CustomerSubscription实体中实现AddTemporaryQuota方法）
            // subscription.AddTemporaryQuota(quotaType, additionalAmount, expiresAt, reason);

            // 保存更改
            await _subscriptionRepository.UpdateAsync(subscription, cancellationToken);

            _logger.LogInformation("成功为客户 {CustomerId} 添加临时配额 {QuotaType}", customerId, quotaType);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "添加临时配额时发生错误，客户 {CustomerId}，配额类型 {QuotaType}", customerId, quotaType);
            throw;
        }
    }

    /// <summary>
    /// 计算配额变化
    /// </summary>
    /// <param name="oldPlan">原计划</param>
    /// <param name="newPlan">新计划</param>
    /// <returns>配额变化字典</returns>
    private static Dictionary<string, QuotaChange> CalculateQuotaChanges(SubscriptionPlan oldPlan, SubscriptionPlan newPlan)
    {
        var changes = new Dictionary<string, QuotaChange>();

        // 获取所有配额类型
        var allQuotaTypes = oldPlan.Quotas.Select(q => q.QuotaType)
            .Union(newPlan.Quotas.Select(q => q.QuotaType))
            .Distinct();

        foreach (var quotaType in allQuotaTypes)
        {
            var oldQuota = oldPlan.Quotas.FirstOrDefault(q => q.QuotaType == quotaType);
            var newQuota = newPlan.Quotas.FirstOrDefault(q => q.QuotaType == quotaType);

            var oldLimit = oldQuota?.Limit ?? 0;
            var newLimit = newQuota?.Limit ?? 0;
            var change = newLimit - oldLimit;
            var changePercentage = oldLimit > 0 ? (double)change / oldLimit * 100 : 0;

            changes[quotaType] = new QuotaChange
            {
                OldLimit = oldLimit,
                NewLimit = newLimit,
                Change = change,
                ChangePercentage = changePercentage
            };
        }

        return changes;
    }

    /// <summary>
    /// 计算下次重置时间
    /// </summary>
    /// <param name="periodStart">周期开始时间</param>
    /// <param name="period">重置周期</param>
    /// <returns>下次重置时间</returns>
    private static DateTime? CalculateNextResetTime(DateTime periodStart, string period)
    {
        return period.ToLowerInvariant() switch
        {
            "daily" => periodStart.AddDays(1),
            "weekly" => periodStart.AddDays(7),
            "monthly" => periodStart.AddMonths(1),
            "yearly" => periodStart.AddYears(1),
            _ => null
        };
    }
}
