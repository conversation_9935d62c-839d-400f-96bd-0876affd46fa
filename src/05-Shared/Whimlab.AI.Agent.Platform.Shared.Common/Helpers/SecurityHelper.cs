using System.Security.Cryptography;
using System.Text;

namespace Whimlab.AI.Agent.Platform.Shared.Common.Helpers;

/// <summary>
/// 安全相关的帮助类
/// </summary>
public static class SecurityHelper
{
    private static readonly char[] PasswordChars = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*()_+-=[]{}|;:,.<>?".ToCharArray();
    private static readonly char[] TokenChars = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789".ToCharArray();

    /// <summary>
    /// 生成安全的随机密码
    /// </summary>
    /// <param name="length">密码长度</param>
    /// <param name="includeSpecialChars">是否包含特殊字符</param>
    /// <returns>随机密码</returns>
    public static string GenerateSecurePassword(int length = 12, bool includeSpecialChars = true)
    {
        if (length < 4)
            throw new ArgumentException("密码长度不能少于4位", nameof(length));

        using var rng = RandomNumberGenerator.Create();
        var chars = includeSpecialChars ? PasswordChars : TokenChars;
        var password = new StringBuilder(length);

        // 确保密码包含至少一个小写字母、大写字母、数字
        password.Append(GetRandomChar(rng, "abcdefghijklmnopqrstuvwxyz"));
        password.Append(GetRandomChar(rng, "ABCDEFGHIJKLMNOPQRSTUVWXYZ"));
        password.Append(GetRandomChar(rng, "0123456789"));

        if (includeSpecialChars && length > 3)
        {
            password.Append(GetRandomChar(rng, "!@#$%^&*()_+-=[]{}|;:,.<>?"));
        }

        // 填充剩余长度
        var remainingLength = length - password.Length;
        for (int i = 0; i < remainingLength; i++)
        {
            password.Append(GetRandomChar(rng, chars));
        }

        // 随机打乱字符顺序
        return ShuffleString(password.ToString(), rng);
    }

    /// <summary>
    /// 生成安全的随机令牌
    /// </summary>
    /// <param name="length">令牌长度</param>
    /// <returns>随机令牌</returns>
    public static string GenerateSecureToken(int length = 32)
    {
        if (length <= 0)
            throw new ArgumentException("令牌长度必须大于0", nameof(length));

        using var rng = RandomNumberGenerator.Create();
        var token = new StringBuilder(length);

        for (int i = 0; i < length; i++)
        {
            token.Append(GetRandomChar(rng, TokenChars));
        }

        return token.ToString();
    }

    /// <summary>
    /// 生成Base64编码的安全随机字符串
    /// </summary>
    /// <param name="byteLength">字节长度</param>
    /// <returns>Base64编码的随机字符串</returns>
    public static string GenerateBase64Token(int byteLength = 32)
    {
        if (byteLength <= 0)
            throw new ArgumentException("字节长度必须大于0", nameof(byteLength));

        using var rng = RandomNumberGenerator.Create();
        var bytes = new byte[byteLength];
        rng.GetBytes(bytes);
        return Convert.ToBase64String(bytes);
    }

    /// <summary>
    /// 生成URL安全的Base64编码随机字符串
    /// </summary>
    /// <param name="byteLength">字节长度</param>
    /// <returns>URL安全的Base64编码随机字符串</returns>
    public static string GenerateUrlSafeBase64Token(int byteLength = 32)
    {
        var base64 = GenerateBase64Token(byteLength);
        return base64.Replace('+', '-').Replace('/', '_').TrimEnd('=');
    }

    /// <summary>
    /// 生成数字验证码
    /// </summary>
    /// <param name="length">验证码长度</param>
    /// <returns>数字验证码</returns>
    public static string GenerateNumericCode(int length = 6)
    {
        if (length <= 0)
            throw new ArgumentException("验证码长度必须大于0", nameof(length));

        using var rng = RandomNumberGenerator.Create();
        var code = new StringBuilder(length);

        for (int i = 0; i < length; i++)
        {
            code.Append(GetRandomChar(rng, "0123456789"));
        }

        return code.ToString();
    }

    /// <summary>
    /// 计算HMAC-SHA256哈希值
    /// </summary>
    /// <param name="data">要计算哈希的数据</param>
    /// <param name="key">密钥</param>
    /// <returns>HMAC-SHA256哈希值</returns>
    public static string ComputeHmacSha256(string data, string key)
    {
        if (string.IsNullOrEmpty(data))
            throw new ArgumentException("数据不能为空", nameof(data));
        
        if (string.IsNullOrEmpty(key))
            throw new ArgumentException("密钥不能为空", nameof(key));

        using var hmac = new HMACSHA256(Encoding.UTF8.GetBytes(key));
        var hashBytes = hmac.ComputeHash(Encoding.UTF8.GetBytes(data));
        return Convert.ToHexString(hashBytes).ToLowerInvariant();
    }

    /// <summary>
    /// 计算HMAC-SHA256哈希值（Base64编码）
    /// </summary>
    /// <param name="data">要计算哈希的数据</param>
    /// <param name="key">密钥</param>
    /// <returns>Base64编码的HMAC-SHA256哈希值</returns>
    public static string ComputeHmacSha256Base64(string data, string key)
    {
        if (string.IsNullOrEmpty(data))
            throw new ArgumentException("数据不能为空", nameof(data));
        
        if (string.IsNullOrEmpty(key))
            throw new ArgumentException("密钥不能为空", nameof(key));

        using var hmac = new HMACSHA256(Encoding.UTF8.GetBytes(key));
        var hashBytes = hmac.ComputeHash(Encoding.UTF8.GetBytes(data));
        return Convert.ToBase64String(hashBytes);
    }

    /// <summary>
    /// 验证HMAC-SHA256签名
    /// </summary>
    /// <param name="data">原始数据</param>
    /// <param name="signature">签名</param>
    /// <param name="key">密钥</param>
    /// <returns>签名是否有效</returns>
    public static bool VerifyHmacSha256Signature(string data, string signature, string key)
    {
        if (string.IsNullOrEmpty(data) || string.IsNullOrEmpty(signature) || string.IsNullOrEmpty(key))
            return false;

        try
        {
            var computedSignature = ComputeHmacSha256(data, key);
            return string.Equals(signature, computedSignature, StringComparison.OrdinalIgnoreCase);
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// 使用AES加密字符串
    /// </summary>
    /// <param name="plainText">明文</param>
    /// <param name="key">密钥（32字节）</param>
    /// <param name="iv">初始化向量（16字节）</param>
    /// <returns>Base64编码的密文</returns>
    public static string EncryptAes(string plainText, byte[] key, byte[] iv)
    {
        if (string.IsNullOrEmpty(plainText))
            throw new ArgumentException("明文不能为空", nameof(plainText));
        
        if (key == null || key.Length != 32)
            throw new ArgumentException("密钥必须是32字节", nameof(key));
        
        if (iv == null || iv.Length != 16)
            throw new ArgumentException("初始化向量必须是16字节", nameof(iv));

        using var aes = Aes.Create();
        aes.Key = key;
        aes.IV = iv;
        aes.Mode = CipherMode.CBC;
        aes.Padding = PaddingMode.PKCS7;

        using var encryptor = aes.CreateEncryptor();
        var plainBytes = Encoding.UTF8.GetBytes(plainText);
        var encryptedBytes = encryptor.TransformFinalBlock(plainBytes, 0, plainBytes.Length);
        
        return Convert.ToBase64String(encryptedBytes);
    }

    /// <summary>
    /// 使用AES解密字符串
    /// </summary>
    /// <param name="cipherText">Base64编码的密文</param>
    /// <param name="key">密钥（32字节）</param>
    /// <param name="iv">初始化向量（16字节）</param>
    /// <returns>明文</returns>
    public static string DecryptAes(string cipherText, byte[] key, byte[] iv)
    {
        if (string.IsNullOrEmpty(cipherText))
            throw new ArgumentException("密文不能为空", nameof(cipherText));
        
        if (key == null || key.Length != 32)
            throw new ArgumentException("密钥必须是32字节", nameof(key));
        
        if (iv == null || iv.Length != 16)
            throw new ArgumentException("初始化向量必须是16字节", nameof(iv));

        using var aes = Aes.Create();
        aes.Key = key;
        aes.IV = iv;
        aes.Mode = CipherMode.CBC;
        aes.Padding = PaddingMode.PKCS7;

        using var decryptor = aes.CreateDecryptor();
        var encryptedBytes = Convert.FromBase64String(cipherText);
        var decryptedBytes = decryptor.TransformFinalBlock(encryptedBytes, 0, encryptedBytes.Length);
        
        return Encoding.UTF8.GetString(decryptedBytes);
    }

    /// <summary>
    /// 生成AES密钥
    /// </summary>
    /// <returns>32字节的AES密钥</returns>
    public static byte[] GenerateAesKey()
    {
        using var aes = Aes.Create();
        aes.GenerateKey();
        return aes.Key;
    }

    /// <summary>
    /// 生成AES初始化向量
    /// </summary>
    /// <returns>16字节的AES初始化向量</returns>
    public static byte[] GenerateAesIV()
    {
        using var aes = Aes.Create();
        aes.GenerateIV();
        return aes.IV;
    }

    /// <summary>
    /// 检查密码强度
    /// </summary>
    /// <param name="password">密码</param>
    /// <returns>密码强度分数（0-100）</returns>
    public static int CalculatePasswordStrength(string password)
    {
        if (string.IsNullOrEmpty(password))
            return 0;

        var score = 0;

        // 长度分数
        if (password.Length >= 8) score += 25;
        if (password.Length >= 12) score += 25;

        // 字符类型分数
        if (password.Any(char.IsLower)) score += 10;
        if (password.Any(char.IsUpper)) score += 10;
        if (password.Any(char.IsDigit)) score += 10;
        if (password.Any(c => !char.IsLetterOrDigit(c))) score += 20;

        return Math.Min(score, 100);
    }

    /// <summary>
    /// 从字符集中获取随机字符
    /// </summary>
    /// <param name="rng">随机数生成器</param>
    /// <param name="chars">字符集</param>
    /// <returns>随机字符</returns>
    private static char GetRandomChar(RandomNumberGenerator rng, string chars)
    {
        var bytes = new byte[4];
        rng.GetBytes(bytes);
        var value = BitConverter.ToUInt32(bytes, 0);
        return chars[(int)(value % (uint)chars.Length)];
    }

    /// <summary>
    /// 从字符数组中获取随机字符
    /// </summary>
    /// <param name="rng">随机数生成器</param>
    /// <param name="chars">字符数组</param>
    /// <returns>随机字符</returns>
    private static char GetRandomChar(RandomNumberGenerator rng, char[] chars)
    {
        var bytes = new byte[4];
        rng.GetBytes(bytes);
        var value = BitConverter.ToUInt32(bytes, 0);
        return chars[value % chars.Length];
    }

    /// <summary>
    /// 随机打乱字符串
    /// </summary>
    /// <param name="input">输入字符串</param>
    /// <param name="rng">随机数生成器</param>
    /// <returns>打乱后的字符串</returns>
    private static string ShuffleString(string input, RandomNumberGenerator rng)
    {
        var chars = input.ToCharArray();
        
        for (int i = chars.Length - 1; i > 0; i--)
        {
            var bytes = new byte[4];
            rng.GetBytes(bytes);
            var j = (int)(BitConverter.ToUInt32(bytes, 0) % (uint)(i + 1));
            (chars[i], chars[j]) = (chars[j], chars[i]);
        }
        
        return new string(chars);
    }
}
