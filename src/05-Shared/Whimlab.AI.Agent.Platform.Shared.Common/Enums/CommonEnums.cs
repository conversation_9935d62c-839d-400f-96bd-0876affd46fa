namespace Whimlab.AI.Agent.Platform.Shared.Common.Enums;

/// <summary>
/// 用户类型枚举
/// </summary>
public enum UserType
{
    /// <summary>
    /// 客户用户
    /// </summary>
    Customer = 1,

    /// <summary>
    /// 管理员用户
    /// </summary>
    Admin = 2
}

/// <summary>
/// 智能体类型枚举
/// </summary>
public enum AgentType
{
    /// <summary>
    /// Semantic Kernel 智能体
    /// </summary>
    SemanticKernel = 1,

    /// <summary>
    /// Dify 聊天智能体
    /// </summary>
    DifyChat = 2,

    /// <summary>
    /// Dify 补全智能体
    /// </summary>
    DifyCompletion = 3,

    /// <summary>
    /// Dify Agent 应用
    /// </summary>
    DifyAgentApp = 4,

    /// <summary>
    /// Dify 工作流
    /// </summary>
    DifyWorkflow = 5
}

/// <summary>
/// 智能体状态枚举
/// </summary>
public enum AgentStatus
{
    /// <summary>
    /// 草稿状态
    /// </summary>
    Draft = 1,

    /// <summary>
    /// 已发布
    /// </summary>
    Published = 2,

    /// <summary>
    /// 已归档
    /// </summary>
    Archived = 3
}

/// <summary>
/// 对话状态枚举
/// </summary>
public enum ConversationStatus
{
    /// <summary>
    /// 活跃状态
    /// </summary>
    Active = 1,

    /// <summary>
    /// 用户归档
    /// </summary>
    ArchivedByUser = 2,

    /// <summary>
    /// 系统自动归档
    /// </summary>
    AutoArchivedSystem = 3
}

/// <summary>
/// 消息发送者类型枚举
/// </summary>
public enum SenderType
{
    /// <summary>
    /// 用户
    /// </summary>
    User = 1,

    /// <summary>
    /// 智能体
    /// </summary>
    Agent = 2,

    /// <summary>
    /// 系统
    /// </summary>
    System = 3
}

/// <summary>
/// 订阅状态枚举
/// </summary>
public enum SubscriptionStatus
{
    /// <summary>
    /// 试用中
    /// </summary>
    Trialing = 1,

    /// <summary>
    /// 活跃状态
    /// </summary>
    Active = 2,

    /// <summary>
    /// 支付失败
    /// </summary>
    PaymentFailed = 3,

    /// <summary>
    /// 已取消
    /// </summary>
    Canceled = 4,

    /// <summary>
    /// 已过期
    /// </summary>
    Expired = 5
}

/// <summary>
/// 计费周期枚举
/// </summary>
public enum BillingCycle
{
    /// <summary>
    /// 月度
    /// </summary>
    Monthly = 1,

    /// <summary>
    /// 年度
    /// </summary>
    Yearly = 2
}

/// <summary>
/// 支付网关类型枚举
/// </summary>
public enum PaymentGatewayType
{
    /// <summary>
    /// 支付宝
    /// </summary>
    Alipay = 1,

    /// <summary>
    /// 微信支付
    /// </summary>
    WeChatPay = 2,

    /// <summary>
    /// 银行卡
    /// </summary>
    BankCard = 3,

    /// <summary>
    /// 企业转账
    /// </summary>
    Enterprise = 4
}

/// <summary>
/// 支付状态枚举
/// </summary>
public enum PaymentStatus
{
    /// <summary>
    /// 等待发起
    /// </summary>
    PendingInitiation = 1,

    /// <summary>
    /// 等待网关响应
    /// </summary>
    PendingGatewayResponse = 2,

    /// <summary>
    /// 支付成功
    /// </summary>
    Succeeded = 3,

    /// <summary>
    /// 支付失败
    /// </summary>
    Failed = 4,

    /// <summary>
    /// 需要用户操作
    /// </summary>
    RequiresAction = 5,

    /// <summary>
    /// 已取消
    /// </summary>
    Canceled = 6,

    /// <summary>
    /// 已退款
    /// </summary>
    Refunded = 7,

    /// <summary>
    /// 部分退款
    /// </summary>
    PartiallyRefunded = 8
}

/// <summary>
/// 退款状态枚举
/// </summary>
public enum RefundStatus
{
    /// <summary>
    /// 处理中
    /// </summary>
    Pending = 1,

    /// <summary>
    /// 退款成功
    /// </summary>
    Succeeded = 2,

    /// <summary>
    /// 退款失败
    /// </summary>
    Failed = 3
}

/// <summary>
/// 通知渠道类型枚举
/// </summary>
public enum NotificationChannelType
{
    /// <summary>
    /// 邮件通知
    /// </summary>
    Email = 1,

    /// <summary>
    /// 应用内通知
    /// </summary>
    InApp = 2,

    /// <summary>
    /// 短信通知
    /// </summary>
    SMS = 3,

    /// <summary>
    /// 微信通知
    /// </summary>
    WeChat = 4,

    /// <summary>
    /// 钉钉通知
    /// </summary>
    DingTalk = 5
}

/// <summary>
/// 通知严重程度枚举
/// </summary>
public enum NotificationSeverity
{
    /// <summary>
    /// 信息
    /// </summary>
    Info = 1,

    /// <summary>
    /// 警告
    /// </summary>
    Warning = 2,

    /// <summary>
    /// 错误
    /// </summary>
    Error = 3,

    /// <summary>
    /// 成功
    /// </summary>
    Success = 4
}

/// <summary>
/// 通知状态枚举
/// </summary>
public enum NotificationStatus
{
    /// <summary>
    /// 等待发送
    /// </summary>
    PendingDispatch = 1,

    /// <summary>
    /// 已发送
    /// </summary>
    Sent = 2,

    /// <summary>
    /// 已送达（应用内）
    /// </summary>
    DeliveredInApp = 3,

    /// <summary>
    /// 已读（应用内）
    /// </summary>
    ReadInApp = 4,

    /// <summary>
    /// 发送失败
    /// </summary>
    DispatchFailed = 5
}

/// <summary>
/// 智能体权限级别枚举
/// </summary>
public enum AgentPermissionLevel
{
    /// <summary>
    /// 只读权限
    /// </summary>
    ReadOnly = 1,

    /// <summary>
    /// 编辑权限
    /// </summary>
    Edit = 2,

    /// <summary>
    /// 管理权限
    /// </summary>
    Manage = 3
}

/// <summary>
/// 排序方向枚举
/// </summary>
public enum SortDirection
{
    /// <summary>
    /// 升序
    /// </summary>
    Ascending = 1,

    /// <summary>
    /// 降序
    /// </summary>
    Descending = 2
}

/// <summary>
/// 报告粒度枚举
/// </summary>
public enum ReportGranularity
{
    /// <summary>
    /// 小时
    /// </summary>
    Hourly = 1,

    /// <summary>
    /// 日
    /// </summary>
    Daily = 2,

    /// <summary>
    /// 周
    /// </summary>
    Weekly = 3,

    /// <summary>
    /// 月
    /// </summary>
    Monthly = 4
}

/// <summary>
/// 消息角色
/// </summary>
public enum MessageRole
{
    /// <summary>
    /// 系统消息
    /// </summary>
    System = 0,

    /// <summary>
    /// 用户消息
    /// </summary>
    User = 1,

    /// <summary>
    /// 助手消息
    /// </summary>
    Assistant = 2,

    /// <summary>
    /// 工具消息
    /// </summary>
    Tool = 3
}

/// <summary>
/// 消息类型
/// </summary>
public enum MessageType
{
    /// <summary>
    /// 文本消息
    /// </summary>
    Text = 0,

    /// <summary>
    /// 图片消息
    /// </summary>
    Image = 1,

    /// <summary>
    /// 文件消息
    /// </summary>
    File = 2,

    /// <summary>
    /// 音频消息
    /// </summary>
    Audio = 3,

    /// <summary>
    /// 视频消息
    /// </summary>
    Video = 4,

    /// <summary>
    /// 工具调用消息
    /// </summary>
    ToolCall = 5,

    /// <summary>
    /// 工具结果消息
    /// </summary>
    ToolResult = 6
}

/// <summary>
/// 订阅计划类型
/// </summary>
public enum PlanType
{
    /// <summary>
    /// 免费计划
    /// </summary>
    Free = 0,

    /// <summary>
    /// 基础计划
    /// </summary>
    Basic = 1,

    /// <summary>
    /// 专业计划
    /// </summary>
    Professional = 2,

    /// <summary>
    /// 企业计划
    /// </summary>
    Enterprise = 3,

    /// <summary>
    /// 自定义计划
    /// </summary>
    Custom = 4
}

/// <summary>
/// 趋势方向
/// </summary>
public enum TrendDirection
{
    /// <summary>
    /// 稳定
    /// </summary>
    Stable = 0,

    /// <summary>
    /// 上升
    /// </summary>
    Increasing = 1,

    /// <summary>
    /// 下降
    /// </summary>
    Decreasing = -1
}

/// <summary>
/// 用户状态
/// </summary>
public enum UserStatus
{
    /// <summary>
    /// 活跃
    /// </summary>
    Active = 0,

    /// <summary>
    /// 非活跃
    /// </summary>
    Inactive = 1,

    /// <summary>
    /// 已暂停
    /// </summary>
    Suspended = 2,

    /// <summary>
    /// 已删除
    /// </summary>
    Deleted = 3
}


