using Whimlab.AI.Agent.Platform.Domain.Core.Exceptions;

namespace Whimlab.AI.Agent.Platform.Domain.Core.Common;

/// <summary>
/// 聚合根基类
/// </summary>
/// <typeparam name="TId">主键类型</typeparam>
public abstract class AggregateRoot<TId> : Entity<TId>
    where TId : notnull
{
    /// <summary>
    /// 版本号（用于乐观锁）
    /// </summary>
    public long Version { get; protected set; } = 1;

    /// <summary>
    /// 增加版本号
    /// </summary>
    protected void IncrementVersion()
    {
        Version++;
        MarkAsModified();
    }

    /// <summary>
    /// 应用业务规则
    /// </summary>
    /// <param name="rule">业务规则</param>
    /// <exception cref="BusinessRuleValidationException">业务规则验证失败</exception>
    protected void CheckRule(IBusinessRule rule)
    {
        if (!rule.IsSatisfied())
        {
            throw new BusinessRuleValidationException(rule);
        }
    }

    /// <summary>
    /// 应用多个业务规则
    /// </summary>
    /// <param name="rules">业务规则集合</param>
    /// <exception cref="BusinessRuleValidationException">业务规则验证失败</exception>
    protected void CheckRules(params IBusinessRule[] rules)
    {
        var failedRules = rules.Where(rule => !rule.IsSatisfied()).ToList();
        
        if (failedRules.Any())
        {
            throw new BusinessRuleValidationException(failedRules);
        }
    }

    /// <summary>
    /// 验证聚合状态
    /// </summary>
    /// <returns>验证结果</returns>
    public virtual ValidationResult Validate()
    {
        return ValidationResult.Success();
    }

    /// <summary>
    /// 检查聚合是否有效
    /// </summary>
    /// <exception cref="DomainValidationException">聚合验证失败</exception>
    protected void EnsureValid()
    {
        var validationResult = Validate();
        if (!validationResult.IsValid)
        {
            throw new DomainValidationException(validationResult.Errors);
        }
    }
}

/// <summary>
/// 聚合根基类（Guid主键）
/// </summary>
public abstract class AggregateRoot : AggregateRoot<Guid>
{
    /// <summary>
    /// 初始化聚合根
    /// </summary>
    protected AggregateRoot()
    {
        Id = Guid.NewGuid();
    }

    /// <summary>
    /// 初始化聚合根
    /// </summary>
    /// <param name="id">聚合根标识符</param>
    protected AggregateRoot(Guid id)
    {
        Id = id;
    }
}

/// <summary>
/// 业务规则接口
/// </summary>
public interface IBusinessRule
{
    /// <summary>
    /// 规则描述
    /// </summary>
    string Message { get; }

    /// <summary>
    /// 错误代码
    /// </summary>
    string ErrorCode { get; }

    /// <summary>
    /// 检查规则是否满足
    /// </summary>
    /// <returns>是否满足</returns>
    bool IsSatisfied();
}

/// <summary>
/// 验证结果
/// </summary>
public class ValidationResult
{
    /// <summary>
    /// 是否有效
    /// </summary>
    public bool IsValid => !Errors.Any();

    /// <summary>
    /// 错误列表
    /// </summary>
    public IReadOnlyList<ValidationError> Errors { get; }

    /// <summary>
    /// 初始化验证结果
    /// </summary>
    /// <param name="errors">错误列表</param>
    public ValidationResult(IEnumerable<ValidationError> errors)
    {
        Errors = errors.ToList().AsReadOnly();
    }

    /// <summary>
    /// 创建成功的验证结果
    /// </summary>
    /// <returns>成功的验证结果</returns>
    public static ValidationResult Success()
    {
        return new ValidationResult(Enumerable.Empty<ValidationError>());
    }

    /// <summary>
    /// 创建失败的验证结果
    /// </summary>
    /// <param name="errors">错误列表</param>
    /// <returns>失败的验证结果</returns>
    public static ValidationResult Failure(IEnumerable<ValidationError> errors)
    {
        return new ValidationResult(errors);
    }

    /// <summary>
    /// 创建失败的验证结果
    /// </summary>
    /// <param name="error">错误</param>
    /// <returns>失败的验证结果</returns>
    public static ValidationResult Failure(ValidationError error)
    {
        return new ValidationResult(new[] { error });
    }

    /// <summary>
    /// 创建失败的验证结果
    /// </summary>
    /// <param name="propertyName">属性名称</param>
    /// <param name="errorMessage">错误消息</param>
    /// <param name="errorCode">错误代码</param>
    /// <returns>失败的验证结果</returns>
    public static ValidationResult Failure(string propertyName, string errorMessage, string? errorCode = null)
    {
        return new ValidationResult(new[] { new ValidationError(propertyName, errorMessage, errorCode) });
    }
}

/// <summary>
/// 验证错误
/// </summary>
public class ValidationError
{
    /// <summary>
    /// 属性名称
    /// </summary>
    public string PropertyName { get; }

    /// <summary>
    /// 错误消息
    /// </summary>
    public string ErrorMessage { get; }

    /// <summary>
    /// 错误代码
    /// </summary>
    public string? ErrorCode { get; }

    /// <summary>
    /// 初始化验证错误
    /// </summary>
    /// <param name="propertyName">属性名称</param>
    /// <param name="errorMessage">错误消息</param>
    /// <param name="errorCode">错误代码</param>
    public ValidationError(string propertyName, string errorMessage, string? errorCode = null)
    {
        PropertyName = propertyName;
        ErrorMessage = errorMessage;
        ErrorCode = errorCode;
    }
}
