﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.0.31903.59
MinimumVisualStudioVersion = 10.0.40219.1
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "src", "src", "{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "01-Presentation", "01-Presentation", "{8AD311A3-B0E3-90EA-6C45-B53BA4D74690}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "02-Application", "02-Application", "{33601912-BEFF-B879-9066-D3D76342D001}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "03-Domain", "03-Domain", "{F3B5EFF4-7A72-7FF2-D22F-B8C0696A68A1}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "04-Infrastructure", "04-Infrastructure", "{4F5DDF67-EBE9-B325-59DB-CE79DBE104F1}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "05-Shared", "05-Shared", "{6123445D-53E3-5402-05DE-C7CFF6B93B17}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "tests", "tests", "{0AB3BF05-4346-4AA6-1389-037BE0695223}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "tools", "tools", "{07C2787E-EAC7-C090-1BA3-A61EC2A24D84}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "docs", "docs", "{208BA388-4CEE-2CBF-559C-5607C099C1F8}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Solution Items", "Solution Items", "{089100B1-113F-4E66-888A-E83F3999EAFD}"
	ProjectSection(SolutionItems) = preProject
		.gitignore = .gitignore
		.dockerignore = .dockerignore
		Directory.Build.props = Directory.Build.props
		Directory.Packages.props = Directory.Packages.props
		global.json = global.json
		nuget.config = nuget.config
		CodeAnalysis.ruleset = CodeAnalysis.ruleset
		README.md = README.md
		docker-compose.yml = docker-compose.yml
		docker-compose.override.yml = docker-compose.override.yml
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "04-Infrastructure", "04-Infrastructure", "{CC156375-B9B2-4323-A2FE-256FC7A7D87F}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Whimlab.AI.Agent.Platform.Infrastructure", "src\04-Infrastructure\Whimlab.AI.Agent.Platform.Infrastructure\Whimlab.AI.Agent.Platform.Infrastructure.csproj", "{A7E0B522-09D5-457C-B74F-33187061BDB5}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Debug|x64 = Debug|x64
		Debug|x86 = Debug|x86
		Release|Any CPU = Release|Any CPU
		Release|x64 = Release|x64
		Release|x86 = Release|x86
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{A7E0B522-09D5-457C-B74F-33187061BDB5}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A7E0B522-09D5-457C-B74F-33187061BDB5}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A7E0B522-09D5-457C-B74F-33187061BDB5}.Debug|x64.ActiveCfg = Debug|Any CPU
		{A7E0B522-09D5-457C-B74F-33187061BDB5}.Debug|x64.Build.0 = Debug|Any CPU
		{A7E0B522-09D5-457C-B74F-33187061BDB5}.Debug|x86.ActiveCfg = Debug|Any CPU
		{A7E0B522-09D5-457C-B74F-33187061BDB5}.Debug|x86.Build.0 = Debug|Any CPU
		{A7E0B522-09D5-457C-B74F-33187061BDB5}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A7E0B522-09D5-457C-B74F-33187061BDB5}.Release|Any CPU.Build.0 = Release|Any CPU
		{A7E0B522-09D5-457C-B74F-33187061BDB5}.Release|x64.ActiveCfg = Release|Any CPU
		{A7E0B522-09D5-457C-B74F-33187061BDB5}.Release|x64.Build.0 = Release|Any CPU
		{A7E0B522-09D5-457C-B74F-33187061BDB5}.Release|x86.ActiveCfg = Release|Any CPU
		{A7E0B522-09D5-457C-B74F-33187061BDB5}.Release|x86.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{CC156375-B9B2-4323-A2FE-256FC7A7D87F} = {A1B2C3D4-E5F6-7890-ABCD-EF1234567890}
		{A7E0B522-09D5-457C-B74F-33187061BDB5} = {CC156375-B9B2-4323-A2FE-256FC7A7D87F}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {12345678-1234-1234-1234-123456789012}
	EndGlobalSection
EndGlobal
