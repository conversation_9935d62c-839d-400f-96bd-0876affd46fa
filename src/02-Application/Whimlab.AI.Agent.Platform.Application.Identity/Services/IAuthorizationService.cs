using Whimlab.AI.Agent.Platform.Domain.Identity.Entities;

namespace Whimlab.AI.Agent.Platform.Application.Identity.Services;

/// <summary>
/// 授权服务接口
/// 负责基于角色的访问控制(RBAC)和资源级别的权限验证
/// </summary>
public interface IAuthorizationService
{
    /// <summary>
    /// 检查用户是否拥有指定权限
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="permissionName">权限名称</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>是否拥有权限</returns>
    Task<bool> HasPermissionAsync(Guid userId, string permissionName, CancellationToken cancellationToken = default);

    /// <summary>
    /// 检查用户是否拥有指定角色
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="roleName">角色名称</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>是否拥有角色</returns>
    Task<bool> HasRoleAsync(Guid userId, string roleName, CancellationToken cancellationToken = default);

    /// <summary>
    /// 检查用户是否拥有任一指定权限
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="permissionNames">权限名称列表</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>是否拥有任一权限</returns>
    Task<bool> HasAnyPermissionAsync(Guid userId, IEnumerable<string> permissionNames, CancellationToken cancellationToken = default);

    /// <summary>
    /// 检查用户是否拥有所有指定权限
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="permissionNames">权限名称列表</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>是否拥有所有权限</returns>
    Task<bool> HasAllPermissionsAsync(Guid userId, IEnumerable<string> permissionNames, CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取用户的所有权限
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>权限名称列表</returns>
    Task<IEnumerable<string>> GetUserPermissionsAsync(Guid userId, CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取用户的所有角色
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>角色名称列表</returns>
    Task<IEnumerable<string>> GetUserRolesAsync(Guid userId, CancellationToken cancellationToken = default);

    /// <summary>
    /// 检查用户是否可以访问指定资源
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="resourceType">资源类型</param>
    /// <param name="resourceId">资源ID</param>
    /// <param name="operation">操作类型</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>是否可以访问</returns>
    Task<bool> CanAccessResourceAsync(Guid userId, string resourceType, Guid resourceId, string operation, CancellationToken cancellationToken = default);

    /// <summary>
    /// 检查用户是否为资源所有者
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="resourceType">资源类型</param>
    /// <param name="resourceId">资源ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>是否为所有者</returns>
    Task<bool> IsResourceOwnerAsync(Guid userId, string resourceType, Guid resourceId, CancellationToken cancellationToken = default);

    /// <summary>
    /// 验证权限并抛出异常（如果没有权限）
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="permissionName">权限名称</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>任务</returns>
    /// <exception cref="UnauthorizedAccessException">没有权限时抛出</exception>
    Task RequirePermissionAsync(Guid userId, string permissionName, CancellationToken cancellationToken = default);

    /// <summary>
    /// 验证角色并抛出异常（如果没有角色）
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="roleName">角色名称</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>任务</returns>
    /// <exception cref="UnauthorizedAccessException">没有角色时抛出</exception>
    Task RequireRoleAsync(Guid userId, string roleName, CancellationToken cancellationToken = default);

    /// <summary>
    /// 验证资源访问权限并抛出异常（如果没有权限）
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="resourceType">资源类型</param>
    /// <param name="resourceId">资源ID</param>
    /// <param name="operation">操作类型</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>任务</returns>
    /// <exception cref="UnauthorizedAccessException">没有权限时抛出</exception>
    Task RequireResourceAccessAsync(Guid userId, string resourceType, Guid resourceId, string operation, CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取用户的权限摘要
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>权限摘要</returns>
    Task<UserPermissionSummary> GetUserPermissionSummaryAsync(Guid userId, CancellationToken cancellationToken = default);
}

/// <summary>
/// 用户权限摘要
/// </summary>
public class UserPermissionSummary
{
    /// <summary>
    /// 用户ID
    /// </summary>
    public Guid UserId { get; set; }

    /// <summary>
    /// 用户名
    /// </summary>
    public string Username { get; set; } = string.Empty;

    /// <summary>
    /// 角色列表
    /// </summary>
    public List<string> Roles { get; set; } = new();

    /// <summary>
    /// 权限列表
    /// </summary>
    public List<string> Permissions { get; set; } = new();

    /// <summary>
    /// 是否为超级管理员
    /// </summary>
    public bool IsSuperAdmin { get; set; }

    /// <summary>
    /// 权限最后更新时间
    /// </summary>
    public DateTime LastUpdated { get; set; }
}

/// <summary>
/// 资源访问权限常量
/// </summary>
public static class ResourcePermissions
{
    /// <summary>
    /// 资源类型
    /// </summary>
    public static class ResourceTypes
    {
        public const string Agent = "Agent";
        public const string Conversation = "Conversation";
        public const string Subscription = "Subscription";
        public const string User = "User";
        public const string Role = "Role";
        public const string Permission = "Permission";
    }

    /// <summary>
    /// 操作类型
    /// </summary>
    public static class Operations
    {
        public const string Create = "Create";
        public const string Read = "Read";
        public const string Update = "Update";
        public const string Delete = "Delete";
        public const string Execute = "Execute";
        public const string Manage = "Manage";
        public const string Publish = "Publish";
        public const string Share = "Share";
    }

    /// <summary>
    /// 系统权限
    /// </summary>
    public static class SystemPermissions
    {
        public const string SuperAdmin = "System.SuperAdmin";
        public const string UserManagement = "System.UserManagement";
        public const string RoleManagement = "System.RoleManagement";
        public const string PermissionManagement = "System.PermissionManagement";
        public const string SystemConfiguration = "System.Configuration";
        public const string SystemMonitoring = "System.Monitoring";
        public const string DataExport = "System.DataExport";
        public const string DataImport = "System.DataImport";
    }

    /// <summary>
    /// 智能体权限
    /// </summary>
    public static class AgentPermissions
    {
        public const string CreateAgent = "Agent.Create";
        public const string ReadAgent = "Agent.Read";
        public const string UpdateAgent = "Agent.Update";
        public const string DeleteAgent = "Agent.Delete";
        public const string PublishAgent = "Agent.Publish";
        public const string ShareAgent = "Agent.Share";
        public const string ManageAgent = "Agent.Manage";
    }

    /// <summary>
    /// 对话权限
    /// </summary>
    public static class ConversationPermissions
    {
        public const string CreateConversation = "Conversation.Create";
        public const string ReadConversation = "Conversation.Read";
        public const string UpdateConversation = "Conversation.Update";
        public const string DeleteConversation = "Conversation.Delete";
        public const string ShareConversation = "Conversation.Share";
        public const string ExportConversation = "Conversation.Export";
    }

    /// <summary>
    /// 订阅权限
    /// </summary>
    public static class SubscriptionPermissions
    {
        public const string ViewSubscription = "Subscription.View";
        public const string ManageSubscription = "Subscription.Manage";
        public const string CreateSubscription = "Subscription.Create";
        public const string UpdateSubscription = "Subscription.Update";
        public const string CancelSubscription = "Subscription.Cancel";
        public const string ViewUsage = "Subscription.ViewUsage";
        public const string ManageQuota = "Subscription.ManageQuota";
    }
}
