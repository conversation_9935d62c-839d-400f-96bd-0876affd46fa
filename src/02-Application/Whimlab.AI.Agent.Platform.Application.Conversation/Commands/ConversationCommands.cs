using Whimlab.AI.Agent.Platform.Application.Core.Common;
using Whimlab.AI.Agent.Platform.Application.Conversation.DTOs;
using Whimlab.AI.Agent.Platform.Shared.Common.Enums;

namespace Whimlab.AI.Agent.Platform.Application.Conversation.Commands;

/// <summary>
/// 创建对话命令
/// </summary>
public class CreateConversationCommand : ICommand<ConversationDto>
{
    /// <summary>
    /// 对话标题
    /// </summary>
    public string Title { get; set; } = string.Empty;

    /// <summary>
    /// 用户ID
    /// </summary>
    public Guid UserId { get; set; }

    /// <summary>
    /// 智能体ID
    /// </summary>
    public Guid AgentId { get; set; }

    /// <summary>
    /// 初始消息内容
    /// </summary>
    public string? InitialMessage { get; set; }

    /// <summary>
    /// 标签列表
    /// </summary>
    public List<string> Tags { get; set; } = new();
}

/// <summary>
/// 更新对话命令
/// </summary>
public class UpdateConversationCommand : ICommand<ConversationDto>
{
    /// <summary>
    /// 对话ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// 对话标题
    /// </summary>
    public string Title { get; set; } = string.Empty;

    /// <summary>
    /// 对话摘要
    /// </summary>
    public string? Summary { get; set; }

    /// <summary>
    /// 标签列表
    /// </summary>
    public List<string> Tags { get; set; } = new();

    /// <summary>
    /// 操作者ID
    /// </summary>
    public Guid OperatorId { get; set; }
}

/// <summary>
/// 归档对话命令
/// </summary>
public class ArchiveConversationCommand : ICommand
{
    /// <summary>
    /// 对话ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// 操作者ID
    /// </summary>
    public Guid OperatorId { get; set; }
}

/// <summary>
/// 恢复对话命令
/// </summary>
public class RestoreConversationCommand : ICommand
{
    /// <summary>
    /// 对话ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// 操作者ID
    /// </summary>
    public Guid OperatorId { get; set; }
}

/// <summary>
/// 删除对话命令
/// </summary>
public class DeleteConversationCommand : ICommand
{
    /// <summary>
    /// 对话ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// 操作者ID
    /// </summary>
    public Guid OperatorId { get; set; }
}

/// <summary>
/// 添加对话标签命令
/// </summary>
public class AddConversationTagsCommand : ICommand
{
    /// <summary>
    /// 对话ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// 标签列表
    /// </summary>
    public List<string> Tags { get; set; } = new();

    /// <summary>
    /// 操作者ID
    /// </summary>
    public Guid OperatorId { get; set; }
}

/// <summary>
/// 移除对话标签命令
/// </summary>
public class RemoveConversationTagsCommand : ICommand
{
    /// <summary>
    /// 对话ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// 标签列表
    /// </summary>
    public List<string> Tags { get; set; } = new();

    /// <summary>
    /// 操作者ID
    /// </summary>
    public Guid OperatorId { get; set; }
}

/// <summary>
/// 生成对话摘要命令
/// </summary>
public class GenerateConversationSummaryCommand : ICommand<string>
{
    /// <summary>
    /// 对话ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// 操作者ID
    /// </summary>
    public Guid OperatorId { get; set; }
}

/// <summary>
/// 批量归档对话命令
/// </summary>
public class BatchArchiveConversationsCommand : ICommand<int>
{
    /// <summary>
    /// 用户ID
    /// </summary>
    public Guid UserId { get; set; }

    /// <summary>
    /// 归档条件：最后活动时间早于此时间的对话将被归档
    /// </summary>
    public DateTime LastActivityBefore { get; set; }

    /// <summary>
    /// 批次大小
    /// </summary>
    public int BatchSize { get; set; } = 100;

    /// <summary>
    /// 操作者ID
    /// </summary>
    public Guid OperatorId { get; set; }
}

/// <summary>
/// 批量删除对话命令
/// </summary>
public class BatchDeleteConversationsCommand : ICommand<int>
{
    /// <summary>
    /// 用户ID
    /// </summary>
    public Guid UserId { get; set; }

    /// <summary>
    /// 删除条件：归档时间早于此时间的对话将被删除
    /// </summary>
    public DateTime ArchivedBefore { get; set; }

    /// <summary>
    /// 批次大小
    /// </summary>
    public int BatchSize { get; set; } = 100;

    /// <summary>
    /// 操作者ID
    /// </summary>
    public Guid OperatorId { get; set; }
}

/// <summary>
/// 创建消息命令
/// </summary>
public class CreateMessageCommand : ICommand<MessageDto>
{
    /// <summary>
    /// 对话ID
    /// </summary>
    public Guid ConversationId { get; set; }

    /// <summary>
    /// 消息角色
    /// </summary>
    public MessageRole Role { get; set; }

    /// <summary>
    /// 消息内容
    /// </summary>
    public string Content { get; set; } = string.Empty;

    /// <summary>
    /// 消息类型
    /// </summary>
    public MessageType MessageType { get; set; } = MessageType.Text;

    /// <summary>
    /// 附件列表
    /// </summary>
    public List<CreateMessageAttachmentDto> Attachments { get; set; } = new();

    /// <summary>
    /// 元数据（JSON格式）
    /// </summary>
    public string? Metadata { get; set; }

    /// <summary>
    /// 操作者ID
    /// </summary>
    public Guid OperatorId { get; set; }
}

/// <summary>
/// 发送消息并获取AI回复命令
/// </summary>
public class SendMessageCommand : ICommand<MessageDto>
{
    /// <summary>
    /// 对话ID
    /// </summary>
    public Guid ConversationId { get; set; }

    /// <summary>
    /// 消息内容
    /// </summary>
    public string Content { get; set; } = string.Empty;

    /// <summary>
    /// 消息类型
    /// </summary>
    public MessageType MessageType { get; set; } = MessageType.Text;

    /// <summary>
    /// 附件列表
    /// </summary>
    public List<CreateMessageAttachmentDto> Attachments { get; set; } = new();

    /// <summary>
    /// 元数据（JSON格式）
    /// </summary>
    public string? Metadata { get; set; }

    /// <summary>
    /// 用户ID
    /// </summary>
    public Guid UserId { get; set; }

    /// <summary>
    /// 是否流式响应
    /// </summary>
    public bool StreamResponse { get; set; } = false;
}

/// <summary>
/// 删除消息命令
/// </summary>
public class DeleteMessageCommand : ICommand
{
    /// <summary>
    /// 消息ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// 操作者ID
    /// </summary>
    public Guid OperatorId { get; set; }
}

/// <summary>
/// 重新生成消息命令
/// </summary>
public class RegenerateMessageCommand : ICommand<MessageDto>
{
    /// <summary>
    /// 消息ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// 操作者ID
    /// </summary>
    public Guid OperatorId { get; set; }
}
