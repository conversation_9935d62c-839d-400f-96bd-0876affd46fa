using MediatR;

namespace Whimlab.AI.Agent.Platform.Application.Core.Common;

/// <summary>
/// 基础命令接口
/// </summary>
public interface ICommand : IRequest<Result>
{
}

/// <summary>
/// 基础命令接口（带返回值）
/// </summary>
/// <typeparam name="TResponse">返回值类型</typeparam>
public interface ICommand<TResponse> : IRequest<Result<TResponse>>
{
}

/// <summary>
/// 基础命令处理器接口
/// </summary>
/// <typeparam name="TCommand">命令类型</typeparam>
public interface ICommandHandler<in TCommand> : IRequestHandler<TCommand, Result>
    where TCommand : ICommand
{
}

/// <summary>
/// 基础命令处理器接口（带返回值）
/// </summary>
/// <typeparam name="TCommand">命令类型</typeparam>
/// <typeparam name="TResponse">返回值类型</typeparam>
public interface ICommandHandler<in TCommand, TResponse> : IRequestHandler<TCommand, Result<TResponse>>
    where TCommand : ICommand<TResponse>
{
}

/// <summary>
/// 基础查询接口
/// </summary>
/// <typeparam name="TResponse">返回值类型</typeparam>
public interface IQuery<TResponse> : IRequest<Result<TResponse>>
{
}

/// <summary>
/// 基础查询处理器接口
/// </summary>
/// <typeparam name="TQuery">查询类型</typeparam>
/// <typeparam name="TResponse">返回值类型</typeparam>
public interface IQueryHandler<in TQuery, TResponse> : IRequestHandler<TQuery, Result<TResponse>>
    where TQuery : IQuery<TResponse>
{
}

/// <summary>
/// 操作结果
/// </summary>
public class Result
{
    /// <summary>
    /// 是否成功
    /// </summary>
    public bool IsSuccess { get; }

    /// <summary>
    /// 是否失败
    /// </summary>
    public bool IsFailure => !IsSuccess;

    /// <summary>
    /// 错误信息
    /// </summary>
    public string? Error { get; }

    /// <summary>
    /// 错误代码
    /// </summary>
    public string? ErrorCode { get; }

    /// <summary>
    /// 初始化操作结果
    /// </summary>
    /// <param name="isSuccess">是否成功</param>
    /// <param name="error">错误信息</param>
    /// <param name="errorCode">错误代码</param>
    protected Result(bool isSuccess, string? error, string? errorCode = null)
    {
        IsSuccess = isSuccess;
        Error = error;
        ErrorCode = errorCode;
    }

    /// <summary>
    /// 创建成功结果
    /// </summary>
    /// <returns>成功结果</returns>
    public static Result Success() => new(true, null);

    /// <summary>
    /// 创建失败结果
    /// </summary>
    /// <param name="error">错误信息</param>
    /// <param name="errorCode">错误代码</param>
    /// <returns>失败结果</returns>
    public static Result Failure(string error, string? errorCode = null) => new(false, error, errorCode);

    /// <summary>
    /// 创建成功结果（带返回值）
    /// </summary>
    /// <typeparam name="T">返回值类型</typeparam>
    /// <param name="value">返回值</param>
    /// <returns>成功结果</returns>
    public static Result<T> Success<T>(T value) => new(value, true, null);

    /// <summary>
    /// 创建失败结果（带返回值）
    /// </summary>
    /// <typeparam name="T">返回值类型</typeparam>
    /// <param name="error">错误信息</param>
    /// <param name="errorCode">错误代码</param>
    /// <returns>失败结果</returns>
    public static Result<T> Failure<T>(string error, string? errorCode = null) => new(default, false, error, errorCode);
}

/// <summary>
/// 操作结果（带返回值）
/// </summary>
/// <typeparam name="T">返回值类型</typeparam>
public class Result<T> : Result
{
    /// <summary>
    /// 返回值
    /// </summary>
    public T? Value { get; }

    /// <summary>
    /// 初始化操作结果
    /// </summary>
    /// <param name="value">返回值</param>
    /// <param name="isSuccess">是否成功</param>
    /// <param name="error">错误信息</param>
    /// <param name="errorCode">错误代码</param>
    internal Result(T? value, bool isSuccess, string? error, string? errorCode = null)
        : base(isSuccess, error, errorCode)
    {
        Value = value;
    }

    /// <summary>
    /// 隐式转换为返回值
    /// </summary>
    /// <param name="result">操作结果</param>
    /// <returns>返回值</returns>
    public static implicit operator T?(Result<T> result) => result.Value;
}


