using FluentValidation;
using Whimlab.AI.Agent.Platform.Application.Identity.Commands;
using Whimlab.AI.Agent.Platform.Shared.Common.Enums;

namespace Whimlab.AI.Agent.Platform.Application.Identity.Validators;

/// <summary>
/// 创建用户命令验证器
/// </summary>
public class CreateUserCommandValidator : AbstractValidator<CreateUserCommand>
{
    /// <summary>
    /// 初始化验证器
    /// </summary>
    public CreateUserCommandValidator()
    {
        RuleFor(x => x.Username)
            .NotEmpty().WithMessage("用户名不能为空")
            .Length(3, 50).WithMessage("用户名长度必须在3-50个字符之间")
            .Matches("^[a-zA-Z0-9_]+$").WithMessage("用户名只能包含字母、数字和下划线");

        RuleFor(x => x.Email)
            .NotEmpty().WithMessage("邮箱地址不能为空")
            .EmailAddress().WithMessage("邮箱地址格式不正确")
            .MaximumLength(255).WithMessage("邮箱地址长度不能超过255个字符");

        RuleFor(x => x.Password)
            .NotEmpty().WithMessage("密码不能为空")
            .MinimumLength(8).WithMessage("密码长度至少8个字符")
            .MaximumLength(128).WithMessage("密码长度不能超过128个字符")
            .Matches(@"^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)").WithMessage("密码必须包含至少一个小写字母、一个大写字母和一个数字");

        RuleFor(x => x.ConfirmPassword)
            .NotEmpty().WithMessage("确认密码不能为空")
            .Equal(x => x.Password).WithMessage("确认密码与密码不匹配");

        RuleFor(x => x.PhoneNumber)
            .Matches(@"^1[3-9]\d{9}$").WithMessage("手机号格式不正确")
            .When(x => !string.IsNullOrWhiteSpace(x.PhoneNumber));

        RuleFor(x => x.DisplayName)
            .MaximumLength(100).WithMessage("显示名称长度不能超过100个字符")
            .When(x => !string.IsNullOrWhiteSpace(x.DisplayName));

        RuleFor(x => x.UserType)
            .IsInEnum().WithMessage("用户类型无效");

        RuleFor(x => x.RoleIds)
            .Must(roleIds => roleIds.All(id => id != Guid.Empty))
            .WithMessage("角色ID不能为空")
            .When(x => x.RoleIds.Any());
    }
}

/// <summary>
/// 更新用户命令验证器
/// </summary>
public class UpdateUserCommandValidator : AbstractValidator<UpdateUserCommand>
{
    /// <summary>
    /// 初始化验证器
    /// </summary>
    public UpdateUserCommandValidator()
    {
        RuleFor(x => x.Id)
            .NotEmpty().WithMessage("用户ID不能为空");

        RuleFor(x => x.DisplayName)
            .MaximumLength(100).WithMessage("显示名称长度不能超过100个字符")
            .When(x => !string.IsNullOrWhiteSpace(x.DisplayName));

        RuleFor(x => x.PhoneNumber)
            .Matches(@"^1[3-9]\d{9}$").WithMessage("手机号格式不正确")
            .When(x => !string.IsNullOrWhiteSpace(x.PhoneNumber));

        RuleFor(x => x.AvatarUrl)
            .Must(BeValidUrl).WithMessage("头像URL格式不正确")
            .When(x => !string.IsNullOrWhiteSpace(x.AvatarUrl));
    }

    /// <summary>
    /// 验证URL格式
    /// </summary>
    /// <param name="url">URL</param>
    /// <returns>是否有效</returns>
    private static bool BeValidUrl(string? url)
    {
        if (string.IsNullOrWhiteSpace(url))
            return true;

        return Uri.TryCreate(url, UriKind.Absolute, out var result) &&
               (result.Scheme == Uri.UriSchemeHttp || result.Scheme == Uri.UriSchemeHttps);
    }
}

/// <summary>
/// 删除用户命令验证器
/// </summary>
public class DeleteUserCommandValidator : AbstractValidator<DeleteUserCommand>
{
    /// <summary>
    /// 初始化验证器
    /// </summary>
    public DeleteUserCommandValidator()
    {
        RuleFor(x => x.Id)
            .NotEmpty().WithMessage("用户ID不能为空");
    }
}

/// <summary>
/// 修改密码命令验证器
/// </summary>
public class ChangePasswordCommandValidator : AbstractValidator<ChangePasswordCommand>
{
    /// <summary>
    /// 初始化验证器
    /// </summary>
    public ChangePasswordCommandValidator()
    {
        RuleFor(x => x.UserId)
            .NotEmpty().WithMessage("用户ID不能为空");

        RuleFor(x => x.CurrentPassword)
            .NotEmpty().WithMessage("当前密码不能为空");

        RuleFor(x => x.NewPassword)
            .NotEmpty().WithMessage("新密码不能为空")
            .MinimumLength(8).WithMessage("新密码长度至少8个字符")
            .MaximumLength(128).WithMessage("新密码长度不能超过128个字符")
            .Matches(@"^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)").WithMessage("新密码必须包含至少一个小写字母、一个大写字母和一个数字")
            .NotEqual(x => x.CurrentPassword).WithMessage("新密码不能与当前密码相同");

        RuleFor(x => x.ConfirmNewPassword)
            .NotEmpty().WithMessage("确认新密码不能为空")
            .Equal(x => x.NewPassword).WithMessage("确认新密码与新密码不匹配");
    }
}

/// <summary>
/// 重置密码命令验证器
/// </summary>
public class ResetPasswordCommandValidator : AbstractValidator<ResetPasswordCommand>
{
    /// <summary>
    /// 初始化验证器
    /// </summary>
    public ResetPasswordCommandValidator()
    {
        RuleFor(x => x.Email)
            .NotEmpty().WithMessage("邮箱地址不能为空")
            .EmailAddress().WithMessage("邮箱地址格式不正确");

        RuleFor(x => x.Token)
            .NotEmpty().WithMessage("重置令牌不能为空");

        RuleFor(x => x.NewPassword)
            .NotEmpty().WithMessage("新密码不能为空")
            .MinimumLength(8).WithMessage("新密码长度至少8个字符")
            .MaximumLength(128).WithMessage("新密码长度不能超过128个字符")
            .Matches(@"^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)").WithMessage("新密码必须包含至少一个小写字母、一个大写字母和一个数字");

        RuleFor(x => x.ConfirmNewPassword)
            .NotEmpty().WithMessage("确认新密码不能为空")
            .Equal(x => x.NewPassword).WithMessage("确认新密码与新密码不匹配");
    }
}

/// <summary>
/// 发送密码重置邮件命令验证器
/// </summary>
public class SendPasswordResetEmailCommandValidator : AbstractValidator<SendPasswordResetEmailCommand>
{
    /// <summary>
    /// 初始化验证器
    /// </summary>
    public SendPasswordResetEmailCommandValidator()
    {
        RuleFor(x => x.Email)
            .NotEmpty().WithMessage("邮箱地址不能为空")
            .EmailAddress().WithMessage("邮箱地址格式不正确");
    }
}

/// <summary>
/// 验证邮箱命令验证器
/// </summary>
public class VerifyEmailCommandValidator : AbstractValidator<VerifyEmailCommand>
{
    /// <summary>
    /// 初始化验证器
    /// </summary>
    public VerifyEmailCommandValidator()
    {
        RuleFor(x => x.UserId)
            .NotEmpty().WithMessage("用户ID不能为空");

        RuleFor(x => x.VerificationCode)
            .NotEmpty().WithMessage("验证码不能为空")
            .Length(6).WithMessage("验证码长度必须为6位")
            .Matches(@"^\d{6}$").WithMessage("验证码必须为6位数字");
    }
}

/// <summary>
/// 验证手机号命令验证器
/// </summary>
public class VerifyPhoneCommandValidator : AbstractValidator<VerifyPhoneCommand>
{
    /// <summary>
    /// 初始化验证器
    /// </summary>
    public VerifyPhoneCommandValidator()
    {
        RuleFor(x => x.UserId)
            .NotEmpty().WithMessage("用户ID不能为空");

        RuleFor(x => x.VerificationCode)
            .NotEmpty().WithMessage("验证码不能为空")
            .Length(6).WithMessage("验证码长度必须为6位")
            .Matches(@"^\d{6}$").WithMessage("验证码必须为6位数字");
    }
}

/// <summary>
/// 分配角色命令验证器
/// </summary>
public class AssignRolesToUserCommandValidator : AbstractValidator<AssignRolesToUserCommand>
{
    /// <summary>
    /// 初始化验证器
    /// </summary>
    public AssignRolesToUserCommandValidator()
    {
        RuleFor(x => x.UserId)
            .NotEmpty().WithMessage("用户ID不能为空");

        RuleFor(x => x.RoleIds)
            .NotEmpty().WithMessage("角色ID列表不能为空")
            .Must(roleIds => roleIds.All(id => id != Guid.Empty))
            .WithMessage("角色ID不能为空");
    }
}
