using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Whimlab.AI.Agent.Platform.Domain.Agent.Repositories;
using Whimlab.AI.Agent.Platform.Domain.Conversation.Repositories;
using Whimlab.AI.Agent.Platform.Domain.Identity.Repositories;
using Whimlab.AI.Agent.Platform.Domain.Subscription.Repositories;
using Whimlab.AI.Agent.Platform.Infrastructure.Data;
using Whimlab.AI.Agent.Platform.Infrastructure.Repositories;

namespace Whimlab.AI.Agent.Platform.Infrastructure.Extensions;

/// <summary>
/// 服务集合扩展方法
/// </summary>
public static class ServiceCollectionExtensions
{
    /// <summary>
    /// 添加基础设施层服务
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <param name="configuration">配置</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddInfrastructure(
        this IServiceCollection services,
        IConfiguration configuration)
    {
        // 添加数据库上下文
        services.AddDbContext(configuration);

        // 添加仓储
        services.AddRepositories();

        return services;
    }

    /// <summary>
    /// 添加数据库上下文
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <param name="configuration">配置</param>
    /// <returns>服务集合</returns>
    private static IServiceCollection AddDbContext(
        this IServiceCollection services,
        IConfiguration configuration)
    {
        var connectionString = configuration.GetConnectionString("DefaultConnection");

        services.AddDbContext<AgentPlatformDbContext>((serviceProvider, options) =>
        {
            var logger = serviceProvider.GetService<ILogger<AgentPlatformDbContext>>();

            if (!string.IsNullOrEmpty(connectionString))
            {
                // 使用SQL Server
                options.UseSqlServer(connectionString, sqlOptions =>
                {
                    sqlOptions.EnableRetryOnFailure(
                        maxRetryCount: 3,
                        maxRetryDelay: TimeSpan.FromSeconds(30),
                        errorNumbersToAdd: null);
                    
                    sqlOptions.CommandTimeout(30);
                });

                logger?.LogInformation("使用SQL Server数据库连接");
            }
            else
            {
                // 使用内存数据库作为后备
                options.UseInMemoryDatabase("AgentPlatformDb");
                logger?.LogWarning("未配置数据库连接字符串，使用内存数据库");
            }

            // 开发环境启用敏感数据日志记录
            if (configuration.GetValue<bool>("Logging:EnableSensitiveDataLogging"))
            {
                options.EnableSensitiveDataLogging();
                options.EnableDetailedErrors();
            }

            // 启用查询分割行为
            options.UseQuerySplittingBehavior(QuerySplittingBehavior.SplitQuery);
        });

        return services;
    }

    /// <summary>
    /// 添加仓储服务
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <returns>服务集合</returns>
    private static IServiceCollection AddRepositories(this IServiceCollection services)
    {
        // Identity模块仓储
        services.AddScoped<IUserRepository, UserRepository>();
        services.AddScoped<IRoleRepository, RoleRepository>();
        services.AddScoped<IPermissionRepository, PermissionRepository>();

        // Agent模块仓储
        services.AddScoped<IAgentRepository, AgentRepository>();

        // Conversation模块仓储
        services.AddScoped<IConversationRepository, ConversationRepository>();
        services.AddScoped<IMessageRepository, MessageRepository>();

        // Subscription模块仓储
        services.AddScoped<ICustomerSubscriptionRepository, CustomerSubscriptionRepository>();
        services.AddScoped<ISubscriptionPlanRepository, SubscriptionPlanRepository>();

        return services;
    }

    /// <summary>
    /// 添加数据库迁移服务
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddDatabaseMigration(this IServiceCollection services)
    {
        services.AddHostedService<DatabaseMigrationService>();
        return services;
    }

    /// <summary>
    /// 确保数据库已创建
    /// </summary>
    /// <param name="serviceProvider">服务提供者</param>
    /// <returns>异步任务</returns>
    public static async Task EnsureDatabaseCreatedAsync(this IServiceProvider serviceProvider)
    {
        using var scope = serviceProvider.CreateScope();
        var context = scope.ServiceProvider.GetRequiredService<AgentPlatformDbContext>();
        var logger = scope.ServiceProvider.GetRequiredService<ILogger<AgentPlatformDbContext>>();

        try
        {
            logger.LogInformation("开始检查数据库状态");

            // 确保数据库已创建
            var created = await context.Database.EnsureCreatedAsync();
            
            if (created)
            {
                logger.LogInformation("数据库已成功创建");
            }
            else
            {
                logger.LogInformation("数据库已存在");
            }

            // 检查是否有待应用的迁移
            var pendingMigrations = await context.Database.GetPendingMigrationsAsync();
            if (pendingMigrations.Any())
            {
                logger.LogInformation("发现 {Count} 个待应用的迁移: {Migrations}", 
                    pendingMigrations.Count(), string.Join(", ", pendingMigrations));

                await context.Database.MigrateAsync();
                logger.LogInformation("数据库迁移已完成");
            }
            else
            {
                logger.LogInformation("数据库已是最新版本");
            }
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "数据库初始化失败");
            throw;
        }
    }
}
