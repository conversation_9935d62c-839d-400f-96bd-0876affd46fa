using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Whimlab.AI.Agent.Platform.Domain.Conversation.Entities;
using Whimlab.AI.Agent.Platform.Domain.Conversation.Repositories;
using Whimlab.AI.Agent.Platform.Domain.Core.Common;
using Whimlab.AI.Agent.Platform.Infrastructure.Data;
using Whimlab.AI.Agent.Platform.Infrastructure.Models.Statistics;
using Whimlab.AI.Agent.Platform.Infrastructure.Repositories.Abstractions;
using Whimlab.AI.Agent.Platform.Shared.Common.Enums;

namespace Whimlab.AI.Agent.Platform.Infrastructure.Repositories;

/// <summary>
/// 消息仓储实现
/// </summary>
public class MessageRepository : BaseRepository<Message, Guid>, IMessageRepository
{
    private readonly AgentPlatformDbContext _context;
    private readonly ILogger<MessageRepository> _logger;
    private readonly DbSet<Message> _dbSet;

    /// <summary>
    /// 初始化消息仓储
    /// </summary>
    /// <param name="context">数据库上下文</param>
    /// <param name="logger">日志记录器</param>
    public MessageRepository(
        AgentPlatformDbContext context,
        ILogger<MessageRepository> logger)
    {
        _context = context;
        _logger = logger;
        _dbSet = context.Messages;
    }

    /// <summary>
    /// 根据ID获取消息
    /// </summary>
    /// <param name="id">消息ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>消息</returns>
    public async Task<Message?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("根据ID获取消息: {MessageId}", id);
            return await _dbSet.FirstOrDefaultAsync(m => m.Id == id, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "根据ID获取消息时发生错误: {MessageId}", id);
            throw;
        }
    }

    /// <summary>
    /// 添加消息
    /// </summary>
    /// <param name="entity">消息实体</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>任务</returns>
    public async Task AddAsync(Message entity, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("添加消息: {MessageId}", entity.Id);
            await _dbSet.AddAsync(entity, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "添加消息时发生错误: {MessageId}", entity.Id);
            throw;
        }
    }

    /// <summary>
    /// 更新消息
    /// </summary>
    /// <param name="entity">消息实体</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>任务</returns>
    public Task UpdateAsync(Message entity, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("更新消息: {MessageId}", entity.Id);
            _dbSet.Update(entity);
            return Task.CompletedTask;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新消息时发生错误: {MessageId}", entity.Id);
            throw;
        }
    }

    /// <summary>
    /// 删除消息
    /// </summary>
    /// <param name="entity">消息实体</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>任务</returns>
    public Task DeleteAsync(Message entity, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("删除消息: {MessageId}", entity.Id);
            _dbSet.Remove(entity);
            return Task.CompletedTask;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "删除消息时发生错误: {MessageId}", entity.Id);
            throw;
        }
    }

    /// <summary>
    /// 保存更改
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>受影响的行数</returns>
    public async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            return await _context.SaveChangesAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "保存更改时发生错误");
            throw;
        }
    }

    /// <summary>
    /// 根据对话ID获取消息列表
    /// </summary>
    /// <param name="conversationId">对话ID</param>
    /// <param name="includeDeleted">是否包含已删除的消息</param>
    /// <param name="pageNumber">页码</param>
    /// <param name="pageSize">页大小</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>消息列表</returns>
    public async Task<(IEnumerable<Message> Messages, int TotalCount)> GetByConversationIdAsync(
        Guid conversationId,
        bool includeDeleted = false,
        int pageNumber = 1,
        int pageSize = 50,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("根据对话ID获取消息列表: {ConversationId}, 包含已删除: {IncludeDeleted}, 页码: {PageNumber}, 页大小: {PageSize}",
                conversationId, includeDeleted, pageNumber, pageSize);

            var query = _dbSet.Where(m => m.ConversationId == conversationId);

            if (!includeDeleted)
            {
                query = query.Where(m => !m.IsDeleted);
            }

            var totalCount = await query.CountAsync(cancellationToken);

            var messages = await query
                .OrderBy(m => m.CreatedAt)
                .Skip((pageNumber - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync(cancellationToken);

            return (messages, totalCount);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "根据对话ID获取消息列表时发生错误: {ConversationId}", conversationId);
            throw;
        }
    }

    /// <summary>
    /// 根据角色获取消息列表
    /// </summary>
    /// <param name="conversationId">对话ID</param>
    /// <param name="role">消息角色</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>消息列表</returns>
    public async Task<IEnumerable<Message>> GetByRoleAsync(
        Guid conversationId,
        MessageRole role,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("根据角色获取消息列表: {ConversationId}, 角色: {Role}", conversationId, role);

            return await _dbSet
                .Where(m => m.ConversationId == conversationId && m.Role == role && !m.IsDeleted)
                .OrderBy(m => m.CreatedAt)
                .ToListAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "根据角色获取消息列表时发生错误: {ConversationId}, 角色: {Role}", conversationId, role);
            throw;
        }
    }

    /// <summary>
    /// 获取最近的消息列表
    /// </summary>
    /// <param name="conversationId">对话ID</param>
    /// <param name="count">数量</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>最近的消息列表</returns>
    public async Task<IEnumerable<Message>> GetRecentMessagesAsync(
        Guid conversationId,
        int count = 10,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("获取最近的消息列表: {ConversationId}, 数量: {Count}", conversationId, count);

            return await _dbSet
                .Where(m => m.ConversationId == conversationId && !m.IsDeleted)
                .OrderByDescending(m => m.CreatedAt)
                .Take(count)
                .OrderBy(m => m.CreatedAt)
                .ToListAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取最近的消息列表时发生错误: {ConversationId}, 数量: {Count}", conversationId, count);
            throw;
        }
    }

    /// <summary>
    /// 搜索消息
    /// </summary>
    /// <param name="conversationId">对话ID</param>
    /// <param name="searchTerm">搜索词</param>
    /// <param name="role">消息角色过滤</param>
    /// <param name="messageType">消息类型过滤</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>匹配的消息列表</returns>
    public async Task<IEnumerable<Message>> SearchMessagesAsync(
        Guid conversationId,
        string searchTerm,
        MessageRole? role = null,
        MessageType? messageType = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("搜索消息: {ConversationId}, 搜索词: {SearchTerm}, 角色: {Role}, 类型: {MessageType}",
                conversationId, searchTerm, role, messageType);

            if (string.IsNullOrWhiteSpace(searchTerm))
            {
                return new List<Message>();
            }

            var searchTermLower = searchTerm.ToLower();

            var query = _dbSet
                .Where(m => m.ConversationId == conversationId && !m.IsDeleted)
                .Where(m => m.Content.ToLower().Contains(searchTermLower));

            if (role.HasValue)
            {
                query = query.Where(m => m.Role == role.Value);
            }

            if (messageType.HasValue)
            {
                query = query.Where(m => m.MessageType == messageType.Value);
            }

            return await query
                .OrderBy(m => m.CreatedAt)
                .ToListAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "搜索消息时发生错误: {ConversationId}, 搜索词: {SearchTerm}", conversationId, searchTerm);
            throw;
        }
    }

    /// <summary>
    /// 获取消息统计信息
    /// </summary>
    /// <param name="conversationId">对话ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>消息统计信息</returns>
    public async Task<Domain.Conversation.Repositories.MessageStatistics> GetStatisticsAsync(Guid conversationId, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("获取消息统计信息: {ConversationId}", conversationId);

            var messages = await _dbSet
                .Where(m => m.ConversationId == conversationId && !m.IsDeleted)
                .ToListAsync(cancellationToken);

            var totalMessages = messages.Count;
            var userMessages = messages.Count(m => m.Role == MessageRole.User);
            var assistantMessages = messages.Count(m => m.Role == MessageRole.Assistant);
            var systemMessages = messages.Count(m => m.Role == MessageRole.System);
            var totalTokens = messages.Sum(m => m.TokensUsed ?? 0);

            return new Domain.Conversation.Repositories.MessageStatistics
            {
                ConversationId = conversationId,
                TotalMessages = totalMessages,
                UserMessages = userMessages,
                AssistantMessages = assistantMessages,
                SystemMessages = systemMessages,
                TotalTokensUsed = totalTokens,
                AverageTokensPerMessage = totalMessages > 0 ? (double)totalTokens / totalMessages : 0
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取消息统计信息时发生错误: {ConversationId}", conversationId);
            throw;
        }
    }

    /// <summary>
    /// 软删除消息
    /// </summary>
    /// <param name="messageId">消息ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>任务</returns>
    public async Task SoftDeleteAsync(Guid messageId, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("软删除消息: {MessageId}", messageId);

            var message = await _dbSet.FirstOrDefaultAsync(m => m.Id == messageId, cancellationToken);
            if (message != null)
            {
                message.SoftDelete();
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "软删除消息时发生错误: {MessageId}", messageId);
            throw;
        }
    }

    /// <summary>
    /// 恢复已删除的消息
    /// </summary>
    /// <param name="messageId">消息ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>任务</returns>
    public async Task RestoreAsync(Guid messageId, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("恢复已删除的消息: {MessageId}", messageId);

            var message = await _dbSet.IgnoreQueryFilters()
                .FirstOrDefaultAsync(m => m.Id == messageId, cancellationToken);
            
            if (message != null && message.IsDeleted)
            {
                message.Restore();
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "恢复已删除的消息时发生错误: {MessageId}", messageId);
            throw;
        }
    }

    /// <summary>
    /// 批量软删除消息
    /// </summary>
    /// <param name="messageIds">消息ID列表</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>任务</returns>
    public async Task BatchSoftDeleteAsync(IEnumerable<Guid> messageIds, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("批量软删除消息: {MessageIds}", string.Join(", ", messageIds));

            var messages = await _dbSet
                .Where(m => messageIds.Contains(m.Id))
                .ToListAsync(cancellationToken);

            foreach (var message in messages)
            {
                message.SoftDelete();
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "批量软删除消息时发生错误: {MessageIds}", string.Join(", ", messageIds));
            throw;
        }
    }

    /// <summary>
    /// 获取对话的Token使用统计
    /// </summary>
    /// <param name="conversationId">对话ID</param>
    /// <param name="startDate">开始日期</param>
    /// <param name="endDate">结束日期</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>Token使用统计</returns>
    public async Task<Domain.Conversation.Repositories.TokenUsageStatistics> GetTokenUsageStatisticsAsync(
        Guid conversationId,
        DateTime? startDate = null,
        DateTime? endDate = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("获取对话的Token使用统计: {ConversationId}, 开始日期: {StartDate}, 结束日期: {EndDate}",
                conversationId, startDate, endDate);

            var query = _dbSet.Where(m => m.ConversationId == conversationId && !m.IsDeleted);

            if (startDate.HasValue)
            {
                query = query.Where(m => m.CreatedAt >= startDate.Value);
            }

            if (endDate.HasValue)
            {
                query = query.Where(m => m.CreatedAt <= endDate.Value);
            }

            var messages = await query.ToListAsync(cancellationToken);

            var totalTokens = messages.Sum(m => m.TokensUsed ?? 0);
            var userTokens = messages.Where(m => m.Role == MessageRole.User).Sum(m => m.TokensUsed ?? 0);
            var assistantTokens = messages.Where(m => m.Role == MessageRole.Assistant).Sum(m => m.TokensUsed ?? 0);

            return new Domain.Conversation.Repositories.TokenUsageStatistics
            {
                ConversationId = conversationId,
                TotalTokens = totalTokens,
                UserTokens = userTokens,
                AssistantTokens = assistantTokens,
                MessageCount = messages.Count,
                StartDate = startDate,
                EndDate = endDate
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取对话的Token使用统计时发生错误: {ConversationId}", conversationId);
            throw;
        }
    }

    // 添加缺失的接口方法的简化实现
    public Task<Message?> GetByIdWithAttachmentsAsync(Guid id, CancellationToken cancellationToken = default)
    {
        return GetByIdAsync(id, cancellationToken);
    }

    public async Task<PagedResult<Message>> GetConversationMessagesAsync(Guid conversationId, int pageNumber = 1, int pageSize = 50, SenderType? senderType = null, bool includeDeleted = false, CancellationToken cancellationToken = default)
    {
        var (messages, totalCount) = await GetByConversationIdAsync(conversationId, includeDeleted, pageNumber, pageSize, cancellationToken);
        return new PagedResult<Message>(messages, totalCount, pageNumber, pageSize);
    }

    public Task<IEnumerable<Message>> GetRecentMessagesAsync(Guid conversationId, int count = 10, bool includeDeleted = false, CancellationToken cancellationToken = default)
    {
        return GetRecentMessagesAsync(conversationId, count, cancellationToken);
    }

    public Task<PagedResult<Message>> SearchMessagesAsync(Guid conversationId, string searchTerm, Guid? userId = null, SenderType? senderType = null, DateTime? startDate = null, DateTime? endDate = null, int pageNumber = 1, int pageSize = 50, CancellationToken cancellationToken = default)
    {
        return Task.FromResult(PagedResult<Message>.Empty(pageNumber, pageSize));
    }

    public Task<Domain.Conversation.Repositories.MessageStatistics> GetUserMessageStatisticsAsync(Guid userId, DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default)
    {
        return Task.FromResult(new Domain.Conversation.Repositories.MessageStatistics());
    }

    public Task<Domain.Conversation.Repositories.MessageStatistics> GetAgentMessageStatisticsAsync(Guid agentId, DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default)
    {
        return Task.FromResult(new Domain.Conversation.Repositories.MessageStatistics());
    }

    public Task<PagedResult<Message>> GetMessagesWithAttachmentsAsync(Guid conversationId, string? fileType = null, int pageNumber = 1, int pageSize = 50, CancellationToken cancellationToken = default)
    {
        return Task.FromResult(PagedResult<Message>.Empty(pageNumber, pageSize));
    }

    public Task<int> PurgeDeletedMessagesAsync(DateTime cutoffDate, int batchSize = 100, CancellationToken cancellationToken = default)
    {
        return Task.FromResult(0);
    }

    public async Task AddRangeAsync(IEnumerable<Message> entities, CancellationToken cancellationToken = default)
    {
        await _dbSet.AddRangeAsync(entities, cancellationToken);
    }
}
