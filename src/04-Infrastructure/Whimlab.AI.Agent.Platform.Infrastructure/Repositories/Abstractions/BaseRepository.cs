using System.Linq.Expressions;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Whimlab.AI.Agent.Platform.Domain.Core.Common;
using Whimlab.AI.Agent.Platform.Domain.Core.Repositories;
using Whimlab.AI.Agent.Platform.Infrastructure.Data;

namespace Whimlab.AI.Agent.Platform.Infrastructure.Repositories.Abstractions;

/// <summary>
/// 基础仓储抽象类
/// </summary>
/// <typeparam name="TEntity">实体类型</typeparam>
/// <typeparam name="TKey">主键类型</typeparam>
public abstract class BaseRepository<TEntity, TKey> : IRepository<TEntity, TKey>
    where TEntity : Entity<TKey>
    where TKey : notnull
{
    protected readonly AgentPlatformDbContext Context;
    protected readonly DbSet<TEntity> DbSet;
    protected readonly ILogger Logger;

    /// <summary>
    /// 初始化基础仓储
    /// </summary>
    /// <param name="context">数据库上下文</param>
    /// <param name="logger">日志记录器</param>
    protected BaseRepository(AgentPlatformDbContext context, ILogger logger)
    {
        Context = context ?? throw new ArgumentNullException(nameof(context));
        Logger = logger ?? throw new ArgumentNullException(nameof(logger));
        DbSet = context.Set<TEntity>();
    }

    /// <summary>
    /// 根据ID获取实体
    /// </summary>
    public virtual async Task<TEntity?> GetByIdAsync(TKey id, CancellationToken cancellationToken = default)
    {
        try
        {
            Logger.LogDebug("根据ID获取实体: {EntityType} - {Id}", typeof(TEntity).Name, id);
            return await DbSet.FindAsync(new object[] { id }, cancellationToken);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "根据ID获取实体时发生错误: {EntityType} - {Id}", typeof(TEntity).Name, id);
            throw;
        }
    }

    /// <summary>
    /// 根据条件获取单个实体
    /// </summary>
    public virtual async Task<TEntity?> GetFirstOrDefaultAsync(Expression<Func<TEntity, bool>> predicate, CancellationToken cancellationToken = default)
    {
        try
        {
            Logger.LogDebug("根据条件获取单个实体: {EntityType}", typeof(TEntity).Name);
            return await DbSet.FirstOrDefaultAsync(predicate, cancellationToken);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "根据条件获取单个实体时发生错误: {EntityType}", typeof(TEntity).Name);
            throw;
        }
    }

    /// <summary>
    /// 获取所有实体
    /// </summary>
    public virtual async Task<IEnumerable<TEntity>> GetAllAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            Logger.LogDebug("获取所有实体: {EntityType}", typeof(TEntity).Name);
            return await DbSet.ToListAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "获取所有实体时发生错误: {EntityType}", typeof(TEntity).Name);
            throw;
        }
    }

    /// <summary>
    /// 根据条件获取实体列表
    /// </summary>
    public virtual async Task<IEnumerable<TEntity>> GetWhereAsync(Expression<Func<TEntity, bool>> predicate, CancellationToken cancellationToken = default)
    {
        try
        {
            Logger.LogDebug("根据条件获取实体列表: {EntityType}", typeof(TEntity).Name);
            return await DbSet.Where(predicate).ToListAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "根据条件获取实体列表时发生错误: {EntityType}", typeof(TEntity).Name);
            throw;
        }
    }

    /// <summary>
    /// 分页查询
    /// </summary>
    public virtual async Task<PagedResult<TEntity>> GetPagedAsync<TOrderKey>(
        int pageNumber,
        int pageSize,
        Expression<Func<TEntity, bool>>? predicate = null,
        Expression<Func<TEntity, TOrderKey>>? orderBy = null,
        bool ascending = true,
        CancellationToken cancellationToken = default)
    {
        try
        {
            Logger.LogDebug("分页查询: {EntityType} - 页码: {PageNumber}, 页大小: {PageSize}", 
                typeof(TEntity).Name, pageNumber, pageSize);

            var query = DbSet.AsQueryable();

            // 应用筛选条件
            if (predicate != null)
            {
                query = query.Where(predicate);
            }

            // 获取总数
            var totalCount = await query.CountAsync(cancellationToken);

            // 应用排序
            if (orderBy != null)
            {
                query = ascending ? query.OrderBy(orderBy) : query.OrderByDescending(orderBy);
            }
            else
            {
                // 默认按创建时间降序排序
                query = query.OrderByDescending(e => e.CreatedAt);
            }

            // 应用分页
            var items = await query
                .Skip((pageNumber - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync(cancellationToken);

            return new PagedResult<TEntity>(items, totalCount, pageNumber, pageSize);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "分页查询时发生错误: {EntityType}", typeof(TEntity).Name);
            throw;
        }
    }

    /// <summary>
    /// 检查实体是否存在
    /// </summary>
    public virtual async Task<bool> ExistsAsync(Expression<Func<TEntity, bool>> predicate, CancellationToken cancellationToken = default)
    {
        try
        {
            Logger.LogDebug("检查实体是否存在: {EntityType}", typeof(TEntity).Name);
            return await DbSet.AnyAsync(predicate, cancellationToken);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "检查实体是否存在时发生错误: {EntityType}", typeof(TEntity).Name);
            throw;
        }
    }

    /// <summary>
    /// 获取实体数量
    /// </summary>
    public virtual async Task<int> CountAsync(Expression<Func<TEntity, bool>>? predicate = null, CancellationToken cancellationToken = default)
    {
        try
        {
            Logger.LogDebug("获取实体数量: {EntityType}", typeof(TEntity).Name);
            return predicate == null 
                ? await DbSet.CountAsync(cancellationToken)
                : await DbSet.CountAsync(predicate, cancellationToken);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "获取实体数量时发生错误: {EntityType}", typeof(TEntity).Name);
            throw;
        }
    }

    /// <summary>
    /// 添加实体
    /// </summary>
    public virtual async Task AddAsync(TEntity entity, CancellationToken cancellationToken = default)
    {
        try
        {
            Logger.LogDebug("添加实体: {EntityType} - {Id}", typeof(TEntity).Name, entity.Id);
            await DbSet.AddAsync(entity, cancellationToken);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "添加实体时发生错误: {EntityType} - {Id}", typeof(TEntity).Name, entity.Id);
            throw;
        }
    }

    /// <summary>
    /// 批量添加实体
    /// </summary>
    public virtual async Task AddRangeAsync(IEnumerable<TEntity> entities, CancellationToken cancellationToken = default)
    {
        try
        {
            var entityList = entities.ToList();
            Logger.LogDebug("批量添加实体: {EntityType} - 数量: {Count}", typeof(TEntity).Name, entityList.Count);
            await DbSet.AddRangeAsync(entityList, cancellationToken);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "批量添加实体时发生错误: {EntityType}", typeof(TEntity).Name);
            throw;
        }
    }

    /// <summary>
    /// 更新实体
    /// </summary>
    public virtual Task UpdateAsync(TEntity entity, CancellationToken cancellationToken = default)
    {
        try
        {
            Logger.LogDebug("更新实体: {EntityType} - {Id}", typeof(TEntity).Name, entity.Id);
            DbSet.Update(entity);
            return Task.CompletedTask;
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "更新实体时发生错误: {EntityType} - {Id}", typeof(TEntity).Name, entity.Id);
            throw;
        }
    }

    /// <summary>
    /// 删除实体
    /// </summary>
    public virtual Task DeleteAsync(TEntity entity, CancellationToken cancellationToken = default)
    {
        try
        {
            Logger.LogDebug("删除实体: {EntityType} - {Id}", typeof(TEntity).Name, entity.Id);
            DbSet.Remove(entity);
            return Task.CompletedTask;
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "删除实体时发生错误: {EntityType} - {Id}", typeof(TEntity).Name, entity.Id);
            throw;
        }
    }

    /// <summary>
    /// 根据ID删除实体
    /// </summary>
    public virtual async Task DeleteByIdAsync(TKey id, CancellationToken cancellationToken = default)
    {
        try
        {
            Logger.LogDebug("根据ID删除实体: {EntityType} - {Id}", typeof(TEntity).Name, id);
            var entity = await GetByIdAsync(id, cancellationToken);
            if (entity != null)
            {
                await DeleteAsync(entity, cancellationToken);
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "根据ID删除实体时发生错误: {EntityType} - {Id}", typeof(TEntity).Name, id);
            throw;
        }
    }

    /// <summary>
    /// 批量删除实体
    /// </summary>
    public virtual Task DeleteRangeAsync(IEnumerable<TEntity> entities, CancellationToken cancellationToken = default)
    {
        try
        {
            var entityList = entities.ToList();
            Logger.LogDebug("批量删除实体: {EntityType} - 数量: {Count}", typeof(TEntity).Name, entityList.Count);
            DbSet.RemoveRange(entityList);
            return Task.CompletedTask;
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "批量删除实体时发生错误: {EntityType}", typeof(TEntity).Name);
            throw;
        }
    }

    /// <summary>
    /// 保存更改
    /// </summary>
    public virtual async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            Logger.LogDebug("保存更改: {EntityType}", typeof(TEntity).Name);
            return await Context.SaveChangesAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "保存更改时发生错误: {EntityType}", typeof(TEntity).Name);
            throw;
        }
    }

    /// <summary>
    /// 应用包含导航属性
    /// </summary>
    /// <param name="query">查询</param>
    /// <param name="includeProperties">包含的属性</param>
    /// <returns>应用包含后的查询</returns>
    protected virtual IQueryable<TEntity> ApplyIncludes(IQueryable<TEntity> query, params Expression<Func<TEntity, object>>[] includeProperties)
    {
        return includeProperties.Aggregate(query, (current, includeProperty) => current.Include(includeProperty));
    }
}

/// <summary>
/// 聚合根仓储基类
/// </summary>
/// <typeparam name="TAggregateRoot">聚合根类型</typeparam>
/// <typeparam name="TKey">主键类型</typeparam>
public abstract class BaseAggregateRepository<TAggregateRoot, TKey> : BaseRepository<TAggregateRoot, TKey>, IAggregateRepository<TAggregateRoot, TKey>
    where TAggregateRoot : AggregateRoot<TKey>
    where TKey : notnull
{
    /// <summary>
    /// 初始化聚合根仓储
    /// </summary>
    /// <param name="context">数据库上下文</param>
    /// <param name="logger">日志记录器</param>
    protected BaseAggregateRepository(AgentPlatformDbContext context, ILogger logger)
        : base(context, logger)
    {
    }

    /// <summary>
    /// 根据版本号获取聚合根
    /// </summary>
    public virtual async Task<TAggregateRoot?> GetByIdAndVersionAsync(TKey id, long expectedVersion, CancellationToken cancellationToken = default)
    {
        try
        {
            Logger.LogDebug("根据ID和版本号获取聚合根: {AggregateType} - {Id}, 版本: {Version}",
                typeof(TAggregateRoot).Name, id, expectedVersion);

            var aggregate = await GetByIdAsync(id, cancellationToken);

            if (aggregate != null && aggregate.Version != expectedVersion)
            {
                Logger.LogWarning("聚合根版本不匹配: {AggregateType} - {Id}, 期望版本: {ExpectedVersion}, 实际版本: {ActualVersion}",
                    typeof(TAggregateRoot).Name, id, expectedVersion, aggregate.Version);
                return null;
            }

            return aggregate;
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "根据ID和版本号获取聚合根时发生错误: {AggregateType} - {Id}",
                typeof(TAggregateRoot).Name, id);
            throw;
        }
    }

    /// <summary>
    /// 保存聚合根（包含领域事件处理）
    /// </summary>
    public virtual async Task SaveAggregateAsync(TAggregateRoot aggregateRoot, CancellationToken cancellationToken = default)
    {
        try
        {
            Logger.LogDebug("保存聚合根: {AggregateType} - {Id}",
                typeof(TAggregateRoot).Name, aggregateRoot.Id);

            // 检查是否为新实体
            var existingEntity = await Context.Entry(aggregateRoot).GetDatabaseValuesAsync(cancellationToken);
            if (existingEntity == null)
            {
                await AddAsync(aggregateRoot, cancellationToken);
            }
            else
            {
                await UpdateAsync(aggregateRoot, cancellationToken);
            }

            // 处理领域事件（如果需要）
            await ProcessDomainEventsAsync(aggregateRoot, cancellationToken);

            // 保存更改
            await SaveChangesAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "保存聚合根时发生错误: {AggregateType} - {Id}",
                typeof(TAggregateRoot).Name, aggregateRoot.Id);
            throw;
        }
    }

    /// <summary>
    /// 处理领域事件
    /// </summary>
    /// <param name="aggregateRoot">聚合根</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>任务</returns>
    protected virtual Task ProcessDomainEventsAsync(TAggregateRoot aggregateRoot, CancellationToken cancellationToken = default)
    {
        // 默认实现：清空领域事件
        // 在实际项目中，这里应该发布领域事件到事件总线
        aggregateRoot.ClearDomainEvents();
        return Task.CompletedTask;
    }
}
