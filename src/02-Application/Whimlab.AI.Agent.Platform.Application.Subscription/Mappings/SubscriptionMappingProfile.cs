using AutoMapper;
using Whimlab.AI.Agent.Platform.Application.Subscription.DTOs;
using Whimlab.AI.Agent.Platform.Domain.Subscription.Entities;
using Whimlab.AI.Agent.Platform.Domain.Subscription.ValueObjects;

namespace Whimlab.AI.Agent.Platform.Application.Subscription.Mappings;

/// <summary>
/// 订阅模块映射配置
/// </summary>
public class SubscriptionMappingProfile : Profile
{
    /// <summary>
    /// 初始化映射配置
    /// </summary>
    public SubscriptionMappingProfile()
    {
        CreateSubscriptionPlanMappings();
        CreateCustomerSubscriptionMappings();
        CreateQuotaMappings();
        CreateHistoryMappings();
    }

    /// <summary>
    /// 创建订阅计划映射
    /// </summary>
    private void CreateSubscriptionPlanMappings()
    {
        // SubscriptionPlan -> SubscriptionPlanDto
        CreateMap<SubscriptionPlan, SubscriptionPlanDto>()
            .ForMember(dest => dest.PlanType, opt => opt.Ignore()) // 暂时忽略，因为领域模型中没有
            .ForMember(dest => dest.MonthlyPrice, opt => opt.MapFrom(src => src.MonthlyPrice.Amount))
            .ForMember(dest => dest.YearlyPrice, opt => opt.MapFrom(src => src.YearlyPrice.Amount))
            .ForMember(dest => dest.QuotaLimits, opt => opt.MapFrom(src => src.Quotas))
            .ForMember(dest => dest.Features, opt => opt.MapFrom(src => src.Features.Select(f => f.Name)));

        // CreateSubscriptionPlanCommand -> SubscriptionPlan (需要在处理器中手动处理)
        // UpdateSubscriptionPlanCommand -> SubscriptionPlan (需要在处理器中手动处理)
    }

    /// <summary>
    /// 创建客户订阅映射
    /// </summary>
    private void CreateCustomerSubscriptionMappings()
    {
        // CustomerSubscription -> CustomerSubscriptionDto
        CreateMap<CustomerSubscription, CustomerSubscriptionDto>()
            .ForMember(dest => dest.PlanName, opt => opt.Ignore()) // 需要在查询时单独处理
            .ForMember(dest => dest.QuotaUsages, opt => opt.MapFrom(src => src.QuotaUsages))
            .ForMember(dest => dest.History, opt => opt.MapFrom(src => new List<SubscriptionHistoryDto>())) // 默认空列表

            .ForMember(dest => dest.AutoRenew, opt => opt.MapFrom(src => src.AutoRenew));

        // CreateCustomerSubscriptionCommand -> CustomerSubscription (需要在处理器中手动处理)
    }

    /// <summary>
    /// 创建配额映射
    /// </summary>
    private void CreateQuotaMappings()
    {
        // PlanQuota -> QuotaLimitDto
        CreateMap<PlanQuota, QuotaLimitDto>()
            .ForMember(dest => dest.QuotaType, opt => opt.MapFrom(src => src.QuotaType))
            .ForMember(dest => dest.LimitValue, opt => opt.MapFrom(src => src.Limit))
            .ForMember(dest => dest.ResetPeriod, opt => opt.MapFrom(src => src.Period))
            .ForMember(dest => dest.IsUnlimited, opt => opt.MapFrom(src => src.Limit == 0));

        // QuotaUsage -> QuotaUsageDto
        CreateMap<QuotaUsage, QuotaUsageDto>()
            .ForMember(dest => dest.QuotaLimit, opt => opt.Ignore()) // 需要从计划中获取
            .ForMember(dest => dest.RemainingQuota, opt => opt.Ignore()) // 需要计算
            .ForMember(dest => dest.UsageRate, opt => opt.Ignore()) // 需要计算
            .ForMember(dest => dest.ResetPeriod, opt => opt.Ignore()) // 需要从计划中获取
            .ForMember(dest => dest.IsUnlimited, opt => opt.Ignore()) // 需要从计划中获取
            .ForMember(dest => dest.IsOverLimit, opt => opt.Ignore()) // 需要计算
            .ForMember(dest => dest.CurrentUsage, opt => opt.MapFrom(src => src.CumulativeAmount))

            .ForMember(dest => dest.NextResetDate, opt => opt.Ignore()); // 需要根据重置周期计算

        // CreateQuotaLimitDto -> QuotaLimit (需要在处理器中手动处理)
    }

    /// <summary>
    /// 创建历史记录映射
    /// </summary>
    private void CreateHistoryMappings()
    {
        // 暂时不实现历史记录映射，因为领域模型中可能没有对应的实体
    }
}
