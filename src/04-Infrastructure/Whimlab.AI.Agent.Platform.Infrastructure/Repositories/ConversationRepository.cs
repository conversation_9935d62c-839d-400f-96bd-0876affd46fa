using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System.Linq.Expressions;
using Whimlab.AI.Agent.Platform.Domain.Conversation.Entities;
using Whimlab.AI.Agent.Platform.Domain.Conversation.Repositories;
using Whimlab.AI.Agent.Platform.Domain.Core.Common;
using Whimlab.AI.Agent.Platform.Infrastructure.Data;
using Whimlab.AI.Agent.Platform.Infrastructure.Models.Statistics;
using Whimlab.AI.Agent.Platform.Shared.Common.Enums;

namespace Whimlab.AI.Agent.Platform.Infrastructure.Repositories;

/// <summary>
/// 对话仓储实现
/// </summary>
public class ConversationRepository : BaseRepository<Conversation, Guid>, IConversationRepository
{
    /// <summary>
    /// 初始化对话仓储
    /// </summary>
    /// <param name="context">数据库上下文</param>
    /// <param name="logger">日志记录器</param>
    public ConversationRepository(
        AgentPlatformDbContext context,
        ILogger<ConversationRepository> logger) : base(context, logger)
    {
    }

    /// <summary>
    /// 根据用户ID获取对话列表
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>对话列表</returns>
    public async Task<List<Conversation>> GetByUserIdAsync(Guid userId, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("根据用户ID获取对话列表: {UserId}", userId);

            return await _dbSet
                .Include(c => c.Messages.OrderBy(m => m.CreatedAt))
                .Where(c => c.UserId == userId)
                .OrderByDescending(c => c.UpdatedAt)
                .ToListAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "根据用户ID获取对话列表时发生错误: {UserId}", userId);
            throw;
        }
    }

    /// <summary>
    /// 根据智能体ID获取对话列表
    /// </summary>
    /// <param name="agentId">智能体ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>对话列表</returns>
    public async Task<List<Conversation>> GetByAgentIdAsync(Guid agentId, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("根据智能体ID获取对话列表: {AgentId}", agentId);

            return await _dbSet
                .Include(c => c.Messages.OrderBy(m => m.CreatedAt))
                .Where(c => c.AgentId == agentId)
                .OrderByDescending(c => c.UpdatedAt)
                .ToListAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "根据智能体ID获取对话列表时发生错误: {AgentId}", agentId);
            throw;
        }
    }

    /// <summary>
    /// 根据状态获取对话列表
    /// </summary>
    /// <param name="status">对话状态</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>对话列表</returns>
    public async Task<List<Conversation>> GetByStatusAsync(ConversationStatus status, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("根据状态获取对话列表: {Status}", status);

            return await _dbSet
                .Include(c => c.Messages.OrderBy(m => m.CreatedAt))
                .Where(c => c.Status == status)
                .OrderByDescending(c => c.UpdatedAt)
                .ToListAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "根据状态获取对话列表时发生错误: {Status}", status);
            throw;
        }
    }

    /// <summary>
    /// 获取活跃对话列表
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>活跃对话列表</returns>
    public async Task<List<Conversation>> GetActiveConversationsAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("获取活跃对话列表");

            return await _dbSet
                .Include(c => c.Messages.OrderBy(m => m.CreatedAt))
                .Where(c => c.Status == ConversationStatus.Active)
                .OrderByDescending(c => c.UpdatedAt)
                .ToListAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取活跃对话列表时发生错误");
            throw;
        }
    }

    /// <summary>
    /// 根据用户ID和智能体ID获取对话
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="agentId">智能体ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>对话</returns>
    public async Task<Conversation?> GetByUserAndAgentAsync(Guid userId, Guid agentId, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("根据用户ID和智能体ID获取对话: {UserId}, {AgentId}", userId, agentId);

            return await _dbSet
                .Include(c => c.Messages.OrderBy(m => m.CreatedAt))
                .Where(c => c.UserId == userId && c.AgentId == agentId && c.Status == ConversationStatus.Active)
                .OrderByDescending(c => c.UpdatedAt)
                .FirstOrDefaultAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "根据用户ID和智能体ID获取对话时发生错误: {UserId}, {AgentId}", userId, agentId);
            throw;
        }
    }

    /// <summary>
    /// 获取最近的对话列表
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="count">数量</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>最近的对话列表</returns>
    public async Task<List<Conversation>> GetRecentConversationsAsync(Guid userId, int count, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("获取最近的对话列表: {UserId}, 数量: {Count}", userId, count);

            return await _dbSet
                .Include(c => c.Messages.OrderBy(m => m.CreatedAt))
                .Where(c => c.UserId == userId)
                .OrderByDescending(c => c.UpdatedAt)
                .Take(count)
                .ToListAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取最近的对话列表时发生错误: {UserId}, 数量: {Count}", userId, count);
            throw;
        }
    }



    /// <summary>
    /// 搜索对话
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="searchTerm">搜索词</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>匹配的对话列表</returns>
    public async Task<List<Conversation>> SearchConversationsAsync(Guid userId, string searchTerm, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("搜索对话: {UserId}, 搜索词: {SearchTerm}", userId, searchTerm);

            if (string.IsNullOrWhiteSpace(searchTerm))
            {
                return new List<Conversation>();
            }

            var searchTermLower = searchTerm.ToLower();

            return await _dbSet
                .Include(c => c.Messages.OrderBy(m => m.CreatedAt))
                .Where(c => c.UserId == userId)
                .Where(c => c.Title.ToLower().Contains(searchTermLower) ||
                           c.Messages.Any(m => m.Content.ToLower().Contains(searchTermLower)))
                .OrderByDescending(c => c.UpdatedAt)
                .ToListAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "搜索对话时发生错误: {UserId}, 搜索词: {SearchTerm}", userId, searchTerm);
            throw;
        }
    }

    /// <summary>
    /// 获取对话统计信息（重载方法 - 按用户ID）
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>对话统计信息</returns>
    public async Task<ConversationStatistics> GetStatisticsByUserAsync(Guid userId, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("获取对话统计信息: {UserId}", userId);

            var conversations = await _dbSet
                .Where(c => c.UserId == userId)
                .ToListAsync(cancellationToken);

            var totalConversations = conversations.Count;
            var activeConversations = conversations.Count(c => c.Status == ConversationStatus.Active);
            var archivedConversations = conversations.Count(c => c.Status == ConversationStatus.Archived);

            var totalMessages = await _context.Messages
                .Where(m => conversations.Select(c => c.Id).Contains(m.ConversationId))
                .CountAsync(cancellationToken);

            var totalTokensUsed = conversations.Sum(c => c.TotalTokensUsed);

            return new ConversationStatistics
            {
                TotalConversations = totalConversations,
                ActiveConversations = activeConversations,
                ArchivedConversations = archivedConversations,
                TotalMessages = totalMessages,
                TotalTokensUsed = totalTokensUsed,
                AverageMessagesPerConversation = totalConversations > 0 ? (double)totalMessages / totalConversations : 0,
                AverageTokensPerConversation = totalConversations > 0 ? (double)totalTokensUsed / totalConversations : 0
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取对话统计信息时发生错误: {UserId}", userId);
            throw;
        }
    }

    /// <summary>
    /// 重写GetByIdAsync以包含消息
    /// </summary>
    /// <param name="id">对话ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>对话</returns>
    public override async Task<Conversation?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("获取对话详情: {ConversationId}", id);

            return await _dbSet
                .Include(c => c.Messages.OrderBy(m => m.CreatedAt))
                .FirstOrDefaultAsync(c => c.Id == id, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取对话详情时发生错误: {ConversationId}", id);
            throw;
        }
    }

    /// <summary>
    /// 根据ID获取对话及其消息
    /// </summary>
    /// <param name="id">对话ID</param>
    /// <param name="includeDeleted">是否包含已删除的消息</param>
    /// <param name="messageLimit">消息数量限制</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>对话</returns>
    public async Task<Conversation?> GetByIdWithMessagesAsync(Guid id, bool includeDeleted = false, int? messageLimit = null, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("根据ID获取对话及其消息: {ConversationId}, 包含已删除: {IncludeDeleted}, 消息限制: {MessageLimit}", id, includeDeleted, messageLimit);

            var query = _dbSet.AsQueryable();

            if (includeDeleted)
            {
                query = query.Include(c => c.Messages.OrderBy(m => m.CreatedAt));
            }
            else
            {
                query = query.Include(c => c.Messages.Where(m => !m.IsDeleted).OrderBy(m => m.CreatedAt));
            }

            if (messageLimit.HasValue)
            {
                query = query.Include(c => c.Messages.OrderByDescending(m => m.CreatedAt).Take(messageLimit.Value));
            }

            return await query.FirstOrDefaultAsync(c => c.Id == id, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "根据ID获取对话及其消息时发生错误: {ConversationId}", id);
            throw;
        }
    }

    /// <summary>
    /// 获取用户对话列表
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="pageNumber">页码</param>
    /// <param name="pageSize">页大小</param>
    /// <param name="searchTerm">搜索词</param>
    /// <param name="status">对话状态</param>
    /// <param name="agentId">智能体ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>用户对话列表</returns>
    public async Task<PagedResult<Conversation>> GetUserConversationsAsync(
        Guid userId,
        int pageNumber = 1,
        int pageSize = 20,
        string? searchTerm = null,
        ConversationStatus? status = null,
        Guid? agentId = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("获取用户对话列表: {UserId}, 页码: {PageNumber}, 页大小: {PageSize}, 搜索词: {SearchTerm}, 状态: {Status}, 智能体ID: {AgentId}",
                userId, pageNumber, pageSize, searchTerm, status, agentId);

            var query = _dbSet.Where(c => c.UserId == userId);

            if (status.HasValue)
            {
                query = query.Where(c => c.Status == status.Value);
            }

            if (agentId.HasValue)
            {
                query = query.Where(c => c.AgentId == agentId.Value);
            }

            if (!string.IsNullOrWhiteSpace(searchTerm))
            {
                var searchTermLower = searchTerm.ToLower();
                query = query.Where(c => c.Title.ToLower().Contains(searchTermLower));
            }

            var totalCount = await query.CountAsync(cancellationToken);

            var conversations = await query
                .OrderByDescending(c => c.LastActiveAt)
                .Skip((pageNumber - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync(cancellationToken);

            return new PagedResult<Conversation>(conversations, totalCount, pageNumber, pageSize);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取用户对话列表时发生错误: {UserId}", userId);
            throw;
        }
    }

    /// <summary>
    /// 获取智能体对话列表
    /// </summary>
    /// <param name="agentId">智能体ID</param>
    /// <param name="pageNumber">页码</param>
    /// <param name="pageSize">页大小</param>
    /// <param name="status">对话状态</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>智能体对话列表</returns>
    public async Task<PagedResult<Conversation>> GetAgentConversationsAsync(
        Guid agentId,
        int pageNumber = 1,
        int pageSize = 20,
        ConversationStatus? status = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("获取智能体对话列表: {AgentId}, 页码: {PageNumber}, 页大小: {PageSize}, 状态: {Status}",
                agentId, pageNumber, pageSize, status);

            var query = _dbSet.Where(c => c.AgentId == agentId);

            if (status.HasValue)
            {
                query = query.Where(c => c.Status == status.Value);
            }

            var totalCount = await query.CountAsync(cancellationToken);

            var conversations = await query
                .OrderByDescending(c => c.LastActiveAt)
                .Skip((pageNumber - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync(cancellationToken);

            return new PagedResult<Conversation>(conversations, totalCount, pageNumber, pageSize);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取智能体对话列表时发生错误: {AgentId}", agentId);
            throw;
        }
    }

    /// <summary>
    /// 获取需要自动归档的对话列表
    /// </summary>
    /// <param name="inactiveThreshold">非活跃时间阈值</param>
    /// <param name="batchSize">批次大小</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>需要自动归档的对话列表</returns>
    public async Task<IEnumerable<Conversation>> GetConversationsForAutoArchiveAsync(
        TimeSpan inactiveThreshold,
        int batchSize = 100,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("获取需要自动归档的对话列表，非活跃时间阈值: {InactiveThreshold}, 批次大小: {BatchSize}",
                inactiveThreshold, batchSize);

            var cutoffDate = DateTime.UtcNow.Subtract(inactiveThreshold);

            return await _dbSet
                .Where(c => c.Status == ConversationStatus.Active && c.LastActiveAt < cutoffDate)
                .OrderBy(c => c.LastActiveAt)
                .Take(batchSize)
                .ToListAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取需要自动归档的对话列表时发生错误，非活跃时间阈值: {InactiveThreshold}", inactiveThreshold);
            throw;
        }
    }

    /// <summary>
    /// 获取对话统计信息
    /// </summary>
    /// <param name="userId">用户ID（可选）</param>
    /// <param name="agentId">智能体ID（可选）</param>
    /// <param name="startDate">开始日期（可选）</param>
    /// <param name="endDate">结束日期（可选）</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>对话统计信息</returns>
    public async Task<ConversationStatistics> GetStatisticsAsync(
        Guid? userId = null,
        Guid? agentId = null,
        DateTime? startDate = null,
        DateTime? endDate = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("获取对话统计信息: 用户ID: {UserId}, 智能体ID: {AgentId}, 开始日期: {StartDate}, 结束日期: {EndDate}",
                userId, agentId, startDate, endDate);

            var query = _dbSet.AsQueryable();

            if (userId.HasValue)
            {
                query = query.Where(c => c.UserId == userId.Value);
            }

            if (agentId.HasValue)
            {
                query = query.Where(c => c.AgentId == agentId.Value);
            }

            if (startDate.HasValue)
            {
                query = query.Where(c => c.CreatedAt >= startDate.Value);
            }

            if (endDate.HasValue)
            {
                query = query.Where(c => c.CreatedAt <= endDate.Value);
            }

            var conversations = await query.ToListAsync(cancellationToken);

            var totalConversations = conversations.Count;
            var activeConversations = conversations.Count(c => c.Status == ConversationStatus.Active);
            var archivedConversations = conversations.Count(c => c.Status == ConversationStatus.Archived);
            var totalMessages = conversations.Sum(c => c.MessageCount);
            var totalTokensUsed = conversations.Sum(c => c.TotalTokensUsed);

            return new ConversationStatistics
            {
                TotalConversations = totalConversations,
                ActiveConversations = activeConversations,
                ArchivedConversations = archivedConversations,
                TotalMessages = totalMessages,
                TotalTokensUsed = totalTokensUsed,
                AverageMessagesPerConversation = totalConversations > 0 ? (double)totalMessages / totalConversations : 0,
                AverageTokensPerConversation = totalConversations > 0 ? (double)totalTokensUsed / totalConversations : 0
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取对话统计信息时发生错误");
            throw;
        }
    }

    /// <summary>
    /// 获取Token使用统计
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="startDate">开始日期</param>
    /// <param name="endDate">结束日期</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>Token使用统计</returns>
    public async Task<Domain.Conversation.Repositories.TokenUsageStatistics> GetTokenUsageStatisticsAsync(
        Guid userId,
        DateTime startDate,
        DateTime endDate,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("获取Token使用统计: {UserId}, 开始日期: {StartDate}, 结束日期: {EndDate}",
                userId, startDate, endDate);

            var conversations = await _dbSet
                .Where(c => c.UserId == userId && c.CreatedAt >= startDate && c.CreatedAt <= endDate)
                .ToListAsync(cancellationToken);

            var totalTokens = conversations.Sum(c => c.TotalTokensUsed);
            var conversationCount = conversations.Count;

            return new Domain.Conversation.Repositories.TokenUsageStatistics
            {
                UserId = userId,
                TotalTokens = totalTokens,
                ConversationCount = conversationCount,
                StartDate = startDate,
                EndDate = endDate,
                AverageTokensPerConversation = conversationCount > 0 ? (double)totalTokens / conversationCount : 0
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取Token使用统计时发生错误: {UserId}", userId);
            throw;
        }
    }
}
