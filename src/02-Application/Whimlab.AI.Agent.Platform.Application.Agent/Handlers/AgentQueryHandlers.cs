using AutoMapper;
using Microsoft.Extensions.Logging;
using Whimlab.AI.Agent.Platform.Application.Core.Common;
using Whimlab.AI.Agent.Platform.Application.Agent.DTOs;
using Whimlab.AI.Agent.Platform.Application.Agent.Queries;
using Whimlab.AI.Agent.Platform.Domain.Core.Common;
using Whimlab.AI.Agent.Platform.Domain.Agent.Repositories;

namespace Whimlab.AI.Agent.Platform.Application.Agent.Handlers;

/// <summary>
/// 根据ID获取智能体查询处理器
/// </summary>
public class GetAgentByIdQueryHandler : IQueryHandler<GetAgentByIdQuery, AgentDto?>
{
    private readonly IAgentRepository _agentRepository;
    private readonly IMapper _mapper;
    private readonly ILogger<GetAgentByIdQueryHandler> _logger;

    /// <summary>
    /// 初始化查询处理器
    /// </summary>
    public GetAgentByIdQueryHandler(
        IAgentRepository agentRepository,
        I<PERSON>apper mapper,
        ILogger<GetAgentByIdQueryHandler> logger)
    {
        _agentRepository = agentRepository;
        _mapper = mapper;
        _logger = logger;
    }

    /// <summary>
    /// 处理查询
    /// </summary>
    public async Task<Result<AgentDto?>> Handle(GetAgentByIdQuery request, CancellationToken cancellationToken)
    {
        try
        {
            var agent = await _agentRepository.GetByIdAsync(request.Id, cancellationToken);

            if (agent == null)
            {
                return Result.Success<AgentDto?>(null);
            }

            var agentDto = _mapper.Map<AgentDto>(agent);
            return Result.Success<AgentDto?>(agentDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取智能体时发生错误: {AgentId}", request.Id);
            return Result.Failure<AgentDto?>("获取智能体时发生内部错误", "INTERNAL_ERROR");
        }
    }
}

/// <summary>
/// 获取用户智能体列表查询处理器
/// </summary>
public class GetUserAgentsQueryHandler : IQueryHandler<GetUserAgentsQuery, PagedResult<AgentSummaryDto>>
{
    private readonly IAgentRepository _agentRepository;
    private readonly IMapper _mapper;
    private readonly ILogger<GetUserAgentsQueryHandler> _logger;

    /// <summary>
    /// 初始化查询处理器
    /// </summary>
    public GetUserAgentsQueryHandler(
        IAgentRepository agentRepository,
        IMapper mapper,
        ILogger<GetUserAgentsQueryHandler> logger)
    {
        _agentRepository = agentRepository;
        _mapper = mapper;
        _logger = logger;
    }

    /// <summary>
    /// 处理查询
    /// </summary>
    public async Task<Result<PagedResult<AgentSummaryDto>>> Handle(GetUserAgentsQuery request, CancellationToken cancellationToken)
    {
        try
        {
            request.Validate();

            var agents = await _agentRepository.GetUserAgentsAsync(
                request.CreatedBy,
                request.PageNumber,
                request.PageSize,
                request.SearchTerm,
                request.AgentType,
                request.Status,
                cancellationToken);

            var agentDtos = agents.Items.Select(a => _mapper.Map<AgentSummaryDto>(a));
            var result = new PagedResult<AgentSummaryDto>(agentDtos, agents.TotalCount, request.PageNumber, request.PageSize);

            return Result.Success(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取用户智能体列表时发生错误: {CreatedBy}", request.CreatedBy);
            return Result.Failure<PagedResult<AgentSummaryDto>>("获取智能体列表时发生内部错误", "INTERNAL_ERROR");
        }
    }
}

/// <summary>
/// 获取公开智能体列表查询处理器
/// </summary>
public class GetPublicAgentsQueryHandler : IQueryHandler<GetPublicAgentsQuery, PagedResult<AgentSummaryDto>>
{
    private readonly IAgentRepository _agentRepository;
    private readonly IMapper _mapper;
    private readonly ILogger<GetPublicAgentsQueryHandler> _logger;

    /// <summary>
    /// 初始化查询处理器
    /// </summary>
    public GetPublicAgentsQueryHandler(
        IAgentRepository agentRepository,
        IMapper mapper,
        ILogger<GetPublicAgentsQueryHandler> logger)
    {
        _agentRepository = agentRepository;
        _mapper = mapper;
        _logger = logger;
    }

    /// <summary>
    /// 处理查询
    /// </summary>
    public async Task<Result<PagedResult<AgentSummaryDto>>> Handle(GetPublicAgentsQuery request, CancellationToken cancellationToken)
    {
        try
        {
            request.Validate();

            var agents = await _agentRepository.GetPublicAgentsAsync(
                request.PageNumber,
                request.PageSize,
                request.SearchTerm,
                request.Category,
                request.AgentType,
                request.SortBy,
                request.SortDirection,
                cancellationToken);

            var agentDtos = agents.Items.Select(a => _mapper.Map<AgentSummaryDto>(a));
            var result = new PagedResult<AgentSummaryDto>(agentDtos, agents.TotalCount, request.PageNumber, request.PageSize);

            return Result.Success(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取公开智能体列表时发生错误");
            return Result.Failure<PagedResult<AgentSummaryDto>>("获取智能体列表时发生内部错误", "INTERNAL_ERROR");
        }
    }
}

/// <summary>
/// 获取热门智能体查询处理器
/// </summary>
public class GetPopularAgentsQueryHandler : IQueryHandler<GetPopularAgentsQuery, IEnumerable<AgentSummaryDto>>
{
    private readonly IAgentRepository _agentRepository;
    private readonly IMapper _mapper;
    private readonly ILogger<GetPopularAgentsQueryHandler> _logger;

    /// <summary>
    /// 初始化查询处理器
    /// </summary>
    public GetPopularAgentsQueryHandler(
        IAgentRepository agentRepository,
        IMapper mapper,
        ILogger<GetPopularAgentsQueryHandler> logger)
    {
        _agentRepository = agentRepository;
        _mapper = mapper;
        _logger = logger;
    }

    /// <summary>
    /// 处理查询
    /// </summary>
    public async Task<Result<IEnumerable<AgentSummaryDto>>> Handle(GetPopularAgentsQuery request, CancellationToken cancellationToken)
    {
        try
        {
            var agents = await _agentRepository.GetPopularAgentsAsync(
                request.Count,
                cancellationToken);

            var agentDtos = agents.Select(a => _mapper.Map<AgentSummaryDto>(a));
            return Result.Success(agentDtos);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取热门智能体时发生错误");
            return Result.Failure<IEnumerable<AgentSummaryDto>>("获取热门智能体时发生内部错误", "INTERNAL_ERROR");
        }
    }
}


