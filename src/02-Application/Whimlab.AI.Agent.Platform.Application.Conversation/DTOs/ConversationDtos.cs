using Whimlab.AI.Agent.Platform.Shared.Common.Enums;

namespace Whimlab.AI.Agent.Platform.Application.Conversation.DTOs;

/// <summary>
/// 对话DTO
/// </summary>
public class ConversationDto
{
    /// <summary>
    /// 对话ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// 对话标题
    /// </summary>
    public string Title { get; set; } = string.Empty;

    /// <summary>
    /// 用户ID
    /// </summary>
    public Guid UserId { get; set; }

    /// <summary>
    /// 智能体ID
    /// </summary>
    public Guid AgentId { get; set; }

    /// <summary>
    /// 智能体名称
    /// </summary>
    public string AgentName { get; set; } = string.Empty;

    /// <summary>
    /// 对话状态
    /// </summary>
    public ConversationStatus Status { get; set; }

    /// <summary>
    /// 消息数量
    /// </summary>
    public int MessageCount { get; set; }

    /// <summary>
    /// 总Token使用量
    /// </summary>
    public long TotalTokens { get; set; }

    /// <summary>
    /// 输入Token数量
    /// </summary>
    public long InputTokens { get; set; }

    /// <summary>
    /// 输出Token数量
    /// </summary>
    public long OutputTokens { get; set; }

    /// <summary>
    /// 估算成本
    /// </summary>
    public decimal EstimatedCost { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedAt { get; set; }

    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime UpdatedAt { get; set; }

    /// <summary>
    /// 最后活动时间
    /// </summary>
    public DateTime LastActivityAt { get; set; }

    /// <summary>
    /// 归档时间
    /// </summary>
    public DateTime? ArchivedAt { get; set; }

    /// <summary>
    /// 对话摘要
    /// </summary>
    public string? Summary { get; set; }

    /// <summary>
    /// 标签列表
    /// </summary>
    public List<string> Tags { get; set; } = new();

    /// <summary>
    /// 消息列表
    /// </summary>
    public List<MessageDto> Messages { get; set; } = new();
}

/// <summary>
/// 对话简要信息DTO
/// </summary>
public class ConversationSummaryDto
{
    /// <summary>
    /// 对话ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// 对话标题
    /// </summary>
    public string Title { get; set; } = string.Empty;

    /// <summary>
    /// 智能体名称
    /// </summary>
    public string AgentName { get; set; } = string.Empty;

    /// <summary>
    /// 对话状态
    /// </summary>
    public ConversationStatus Status { get; set; }

    /// <summary>
    /// 消息数量
    /// </summary>
    public int MessageCount { get; set; }

    /// <summary>
    /// 总Token使用量
    /// </summary>
    public long TotalTokens { get; set; }

    /// <summary>
    /// 最后活动时间
    /// </summary>
    public DateTime LastActivityAt { get; set; }

    /// <summary>
    /// 对话摘要
    /// </summary>
    public string? Summary { get; set; }

    /// <summary>
    /// 标签列表
    /// </summary>
    public List<string> Tags { get; set; } = new();
}

/// <summary>
/// 创建对话DTO
/// </summary>
public class CreateConversationDto
{
    /// <summary>
    /// 对话标题
    /// </summary>
    public string Title { get; set; } = string.Empty;

    /// <summary>
    /// 用户ID
    /// </summary>
    public Guid UserId { get; set; }

    /// <summary>
    /// 智能体ID
    /// </summary>
    public Guid AgentId { get; set; }

    /// <summary>
    /// 初始消息内容
    /// </summary>
    public string? InitialMessage { get; set; }

    /// <summary>
    /// 标签列表
    /// </summary>
    public List<string> Tags { get; set; } = new();
}

/// <summary>
/// 更新对话DTO
/// </summary>
public class UpdateConversationDto
{
    /// <summary>
    /// 对话ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// 对话标题
    /// </summary>
    public string Title { get; set; } = string.Empty;

    /// <summary>
    /// 对话摘要
    /// </summary>
    public string? Summary { get; set; }

    /// <summary>
    /// 标签列表
    /// </summary>
    public List<string> Tags { get; set; } = new();
}

/// <summary>
/// 消息DTO
/// </summary>
public class MessageDto
{
    /// <summary>
    /// 消息ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// 对话ID
    /// </summary>
    public Guid ConversationId { get; set; }

    /// <summary>
    /// 消息角色
    /// </summary>
    public MessageRole Role { get; set; }

    /// <summary>
    /// 消息内容
    /// </summary>
    public string Content { get; set; } = string.Empty;

    /// <summary>
    /// 消息类型
    /// </summary>
    public MessageType MessageType { get; set; }

    /// <summary>
    /// 输入Token数量
    /// </summary>
    public int InputTokens { get; set; }

    /// <summary>
    /// 输出Token数量
    /// </summary>
    public int OutputTokens { get; set; }

    /// <summary>
    /// 处理时间（毫秒）
    /// </summary>
    public long ProcessingTimeMs { get; set; }

    /// <summary>
    /// 模型名称
    /// </summary>
    public string? ModelName { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedAt { get; set; }

    /// <summary>
    /// 错误信息
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// 附件列表
    /// </summary>
    public List<MessageAttachmentDto> Attachments { get; set; } = new();

    /// <summary>
    /// 元数据（JSON格式）
    /// </summary>
    public string? Metadata { get; set; }
}

/// <summary>
/// 创建消息DTO
/// </summary>
public class CreateMessageDto
{
    /// <summary>
    /// 对话ID
    /// </summary>
    public Guid ConversationId { get; set; }

    /// <summary>
    /// 消息角色
    /// </summary>
    public MessageRole Role { get; set; }

    /// <summary>
    /// 消息内容
    /// </summary>
    public string Content { get; set; } = string.Empty;

    /// <summary>
    /// 消息类型
    /// </summary>
    public MessageType MessageType { get; set; } = MessageType.Text;

    /// <summary>
    /// 附件列表
    /// </summary>
    public List<CreateMessageAttachmentDto> Attachments { get; set; } = new();

    /// <summary>
    /// 元数据（JSON格式）
    /// </summary>
    public string? Metadata { get; set; }
}

/// <summary>
/// 消息附件DTO
/// </summary>
public class MessageAttachmentDto
{
    /// <summary>
    /// 附件ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// 文件名
    /// </summary>
    public string FileName { get; set; } = string.Empty;

    /// <summary>
    /// 文件类型
    /// </summary>
    public string ContentType { get; set; } = string.Empty;

    /// <summary>
    /// 文件大小
    /// </summary>
    public long FileSize { get; set; }

    /// <summary>
    /// 文件URL
    /// </summary>
    public string FileUrl { get; set; } = string.Empty;

    /// <summary>
    /// 缩略图URL
    /// </summary>
    public string? ThumbnailUrl { get; set; }

    /// <summary>
    /// 上传时间
    /// </summary>
    public DateTime UploadedAt { get; set; }
}

/// <summary>
/// 创建消息附件DTO
/// </summary>
public class CreateMessageAttachmentDto
{
    /// <summary>
    /// 文件名
    /// </summary>
    public string FileName { get; set; } = string.Empty;

    /// <summary>
    /// 文件类型
    /// </summary>
    public string ContentType { get; set; } = string.Empty;

    /// <summary>
    /// 文件大小
    /// </summary>
    public long FileSize { get; set; }

    /// <summary>
    /// 文件内容（Base64编码）
    /// </summary>
    public string FileContent { get; set; } = string.Empty;
}

/// <summary>
/// Token使用统计DTO
/// </summary>
public class TokenUsageStatisticsDto
{
    /// <summary>
    /// 用户ID
    /// </summary>
    public Guid UserId { get; set; }

    /// <summary>
    /// 统计日期
    /// </summary>
    public DateTime Date { get; set; }

    /// <summary>
    /// 总Token使用量
    /// </summary>
    public long TotalTokens { get; set; }

    /// <summary>
    /// 输入Token数量
    /// </summary>
    public long InputTokens { get; set; }

    /// <summary>
    /// 输出Token数量
    /// </summary>
    public long OutputTokens { get; set; }

    /// <summary>
    /// 对话数量
    /// </summary>
    public int ConversationCount { get; set; }

    /// <summary>
    /// 消息数量
    /// </summary>
    public int MessageCount { get; set; }

    /// <summary>
    /// 估算成本
    /// </summary>
    public decimal EstimatedCost { get; set; }

    /// <summary>
    /// 按智能体分组的使用统计
    /// </summary>
    public Dictionary<Guid, AgentUsageDto> AgentUsage { get; set; } = new();

    /// <summary>
    /// 按模型分组的使用统计
    /// </summary>
    public Dictionary<string, ModelUsageDto> ModelUsage { get; set; } = new();
}

/// <summary>
/// 智能体使用统计DTO
/// </summary>
public class AgentUsageDto
{
    /// <summary>
    /// 智能体ID
    /// </summary>
    public Guid AgentId { get; set; }

    /// <summary>
    /// 智能体名称
    /// </summary>
    public string AgentName { get; set; } = string.Empty;

    /// <summary>
    /// Token使用量
    /// </summary>
    public long TokenCount { get; set; }

    /// <summary>
    /// 对话数量
    /// </summary>
    public int ConversationCount { get; set; }

    /// <summary>
    /// 消息数量
    /// </summary>
    public int MessageCount { get; set; }

    /// <summary>
    /// 估算成本
    /// </summary>
    public decimal EstimatedCost { get; set; }
}

/// <summary>
/// 模型使用统计DTO
/// </summary>
public class ModelUsageDto
{
    /// <summary>
    /// 模型名称
    /// </summary>
    public string ModelName { get; set; } = string.Empty;

    /// <summary>
    /// Token使用量
    /// </summary>
    public long TokenCount { get; set; }

    /// <summary>
    /// 请求次数
    /// </summary>
    public int RequestCount { get; set; }

    /// <summary>
    /// 平均处理时间（毫秒）
    /// </summary>
    public double AverageProcessingTime { get; set; }

    /// <summary>
    /// 估算成本
    /// </summary>
    public decimal EstimatedCost { get; set; }
}

/// <summary>
/// 对话统计DTO
/// </summary>
public class ConversationStatisticsDto
{
    /// <summary>
    /// 总对话数
    /// </summary>
    public int TotalConversations { get; set; }

    /// <summary>
    /// 活跃对话数
    /// </summary>
    public int ActiveConversations { get; set; }

    /// <summary>
    /// 已归档对话数
    /// </summary>
    public int ArchivedConversations { get; set; }

    /// <summary>
    /// 总消息数
    /// </summary>
    public long TotalMessages { get; set; }

    /// <summary>
    /// 总Token使用量
    /// </summary>
    public long TotalTokens { get; set; }

    /// <summary>
    /// 总估算成本
    /// </summary>
    public decimal TotalEstimatedCost { get; set; }

    /// <summary>
    /// 平均对话长度（消息数）
    /// </summary>
    public double AverageConversationLength { get; set; }

    /// <summary>
    /// 平均Token使用量
    /// </summary>
    public double AverageTokenUsage { get; set; }

    /// <summary>
    /// 按日期分组的统计
    /// </summary>
    public Dictionary<DateTime, DailyStatisticsDto> DailyStatistics { get; set; } = new();

    /// <summary>
    /// 最受欢迎的智能体
    /// </summary>
    public List<AgentUsageDto> TopAgents { get; set; } = new();
}

/// <summary>
/// 每日统计DTO
/// </summary>
public class DailyStatisticsDto
{
    /// <summary>
    /// 日期
    /// </summary>
    public DateTime Date { get; set; }

    /// <summary>
    /// 对话数量
    /// </summary>
    public int ConversationCount { get; set; }

    /// <summary>
    /// 消息数量
    /// </summary>
    public int MessageCount { get; set; }

    /// <summary>
    /// Token使用量
    /// </summary>
    public long TokenCount { get; set; }

    /// <summary>
    /// 估算成本
    /// </summary>
    public decimal EstimatedCost { get; set; }
}
