using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Whimlab.AI.Agent.Platform.Domain.Agent.Entities;
using Whimlab.AI.Agent.Platform.Domain.Conversation.Entities;
using Whimlab.AI.Agent.Platform.Domain.Identity.Entities;
using Whimlab.AI.Agent.Platform.Domain.Subscription.Entities;
using Whimlab.AI.Agent.Platform.Infrastructure.Configurations;

namespace Whimlab.AI.Agent.Platform.Infrastructure.Data;

/// <summary>
/// 智能体平台数据库上下文
/// </summary>
public class AgentPlatformDbContext : DbContext
{
    private readonly ILogger<AgentPlatformDbContext> _logger;

    /// <summary>
    /// 初始化数据库上下文
    /// </summary>
    /// <param name="options">数据库上下文选项</param>
    /// <param name="logger">日志记录器</param>
    public AgentPlatformDbContext(
        DbContextOptions<AgentPlatformDbContext> options,
        ILogger<AgentPlatformDbContext> logger) : base(options)
    {
        _logger = logger;
    }

    #region Identity Module DbSets

    /// <summary>
    /// 用户实体集
    /// </summary>
    public DbSet<User> Users { get; set; } = null!;

    /// <summary>
    /// 角色实体集
    /// </summary>
    public DbSet<Role> Roles { get; set; } = null!;

    /// <summary>
    /// 用户角色关联实体集
    /// </summary>
    public DbSet<UserRole> UserRoles { get; set; } = null!;

    /// <summary>
    /// 权限实体集
    /// </summary>
    public DbSet<Permission> Permissions { get; set; } = null!;

    /// <summary>
    /// 角色权限关联实体集
    /// </summary>
    public DbSet<RolePermission> RolePermissions { get; set; } = null!;

    /// <summary>
    /// 用户权限关联实体集
    /// </summary>
    public DbSet<UserPermission> UserPermissions { get; set; } = null!;

    /// <summary>
    /// 资源权限实体集
    /// </summary>
    public DbSet<ResourcePermission> ResourcePermissions { get; set; } = null!;

    #endregion

    #region Agent Module DbSets

    /// <summary>
    /// 智能体实体集
    /// </summary>
    public DbSet<Domain.Agent.Entities.Agent> Agents { get; set; } = null!;

    #endregion

    #region Conversation Module DbSets

    /// <summary>
    /// 对话实体集
    /// </summary>
    public DbSet<Conversation> Conversations { get; set; } = null!;

    /// <summary>
    /// 消息实体集
    /// </summary>
    public DbSet<Message> Messages { get; set; } = null!;

    #endregion

    #region Subscription Module DbSets

    /// <summary>
    /// 客户订阅实体集
    /// </summary>
    public DbSet<CustomerSubscription> CustomerSubscriptions { get; set; } = null!;

    /// <summary>
    /// 订阅计划实体集
    /// </summary>
    public DbSet<SubscriptionPlan> SubscriptionPlans { get; set; } = null!;

    /// <summary>
    /// 配额使用记录实体集
    /// </summary>
    public DbSet<QuotaUsageRecord> QuotaUsageRecords { get; set; } = null!;

    #endregion

    /// <summary>
    /// 配置实体映射
    /// </summary>
    /// <param name="modelBuilder">模型构建器</param>
    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        try
        {
            _logger.LogDebug("开始配置实体映射");

            // 应用所有配置
            modelBuilder.ApplyConfigurationsFromAssembly(typeof(AgentPlatformDbContext).Assembly);

            _logger.LogDebug("实体映射配置完成");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "配置实体映射时发生错误");
            throw;
        }
    }

    /// <summary>
    /// 配置数据库上下文选项
    /// </summary>
    /// <param name="optionsBuilder">选项构建器</param>
    protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
    {
        base.OnConfiguring(optionsBuilder);

        if (!optionsBuilder.IsConfigured)
        {
            _logger.LogWarning("数据库连接字符串未配置，使用内存数据库");
            optionsBuilder.UseInMemoryDatabase("AgentPlatformDb");
        }

        // 启用敏感数据日志记录（仅在开发环境）
        if (_logger.IsEnabled(LogLevel.Debug))
        {
            optionsBuilder.EnableSensitiveDataLogging();
            optionsBuilder.EnableDetailedErrors();
        }
    }

    /// <summary>
    /// 保存更改前的处理
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>受影响的行数</returns>
    public override async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            // 在保存前处理审计字段
            ProcessAuditFields();

            var result = await base.SaveChangesAsync(cancellationToken);

            _logger.LogDebug("数据库保存成功，受影响的行数: {AffectedRows}", result);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "保存数据库更改时发生错误");
            throw;
        }
    }

    /// <summary>
    /// 处理审计字段
    /// </summary>
    private void ProcessAuditFields()
    {
        var entries = ChangeTracker.Entries()
            .Where(e => e.State == EntityState.Added || e.State == EntityState.Modified);

        foreach (var entry in entries)
        {
            // 处理创建时间
            if (entry.State == EntityState.Added)
            {
                if (entry.Entity.GetType().GetProperty("CreatedAt") != null)
                {
                    entry.Property("CreatedAt").CurrentValue = DateTime.UtcNow;
                }
            }

            // 处理更新时间
            if (entry.State == EntityState.Modified)
            {
                if (entry.Entity.GetType().GetProperty("UpdatedAt") != null)
                {
                    entry.Property("UpdatedAt").CurrentValue = DateTime.UtcNow;
                }
            }
        }
    }
}
