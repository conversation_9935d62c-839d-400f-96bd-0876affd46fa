using AutoMapper;
using Microsoft.Extensions.Logging;
using Whimlab.AI.Agent.Platform.Application.Core.Common;
using Whimlab.AI.Agent.Platform.Application.Conversation.Commands;
using Whimlab.AI.Agent.Platform.Application.Conversation.DTOs;
using Whimlab.AI.Agent.Platform.Domain.Conversation.Entities;
using Whimlab.AI.Agent.Platform.Domain.Conversation.Repositories;

namespace Whimlab.AI.Agent.Platform.Application.Conversation.Handlers;

/// <summary>
/// 创建对话命令处理器
/// </summary>
public class CreateConversationCommandHandler : ICommandHandler<CreateConversationCommand, ConversationDto>
{
    private readonly IConversationRepository _conversationRepository;
    private readonly IMapper _mapper;
    private readonly ILogger<CreateConversationCommandHandler> _logger;

    /// <summary>
    /// 初始化创建对话命令处理器
    /// </summary>
    public CreateConversationCommandHandler(
        IConversationRepository conversationRepository,
        IMapper mapper,
        ILogger<CreateConversationCommandHandler> logger)
    {
        _conversationRepository = conversationRepository;
        _mapper = mapper;
        _logger = logger;
    }

    /// <summary>
    /// 处理创建对话命令
    /// </summary>
    public async Task<Result<ConversationDto>> Handle(CreateConversationCommand request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("开始创建对话: {Title} - 用户: {UserId}", request.Title, request.UserId);

            // 创建对话实体
            var conversation = Domain.Conversation.Entities.Conversation.Create(
                request.Title,
                request.UserId,
                request.AgentId);

            // 如果有初始消息，创建消息
            if (!string.IsNullOrWhiteSpace(request.InitialMessage))
            {
                conversation.AddUserMessage(request.InitialMessage);
            }

            // 保存对话
            await _conversationRepository.AddAsync(conversation, cancellationToken);
            await _conversationRepository.SaveChangesAsync(cancellationToken);

            // 重新获取对话
            var createdConversation = await _conversationRepository.GetByIdAsync(conversation.Id, cancellationToken);
            if (createdConversation == null)
            {
                return Result.Failure<ConversationDto>("对话创建失败", "CONVERSATION_CREATION_FAILED");
            }

            var conversationDto = _mapper.Map<ConversationDto>(createdConversation);

            _logger.LogInformation("对话创建成功: {ConversationId} - {Title}", conversation.Id, request.Title);

            return Result.Success(conversationDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "创建对话时发生错误: {Title}", request.Title);
            return Result.Failure<ConversationDto>("创建对话时发生内部错误", "INTERNAL_ERROR");
        }
    }
}

/// <summary>
/// 更新对话命令处理器
/// </summary>
public class UpdateConversationCommandHandler : ICommandHandler<UpdateConversationCommand, ConversationDto>
{
    private readonly IConversationRepository _conversationRepository;
    private readonly IMapper _mapper;
    private readonly ILogger<UpdateConversationCommandHandler> _logger;

    /// <summary>
    /// 初始化更新对话命令处理器
    /// </summary>
    public UpdateConversationCommandHandler(
        IConversationRepository conversationRepository,
        IMapper mapper,
        ILogger<UpdateConversationCommandHandler> logger)
    {
        _conversationRepository = conversationRepository;
        _mapper = mapper;
        _logger = logger;
    }

    /// <summary>
    /// 处理更新对话命令
    /// </summary>
    public async Task<Result<ConversationDto>> Handle(UpdateConversationCommand request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("开始更新对话: {ConversationId}", request.Id);

            // 获取对话
            var conversation = await _conversationRepository.GetByIdAsync(request.Id, cancellationToken);
            if (conversation == null)
            {
                return Result.Failure<ConversationDto>("对话不存在", "CONVERSATION_NOT_FOUND");
            }

            // 检查权限（简化版本）
            if (conversation.UserId != request.OperatorId)
            {
                return Result.Failure<ConversationDto>("没有权限修改此对话", "PERMISSION_DENIED");
            }

            // 更新对话信息
            conversation.UpdateTitle(request.Title);

            if (!string.IsNullOrWhiteSpace(request.Summary))
            {
                conversation.UpdateContext(request.Summary);
            }

            // 保存更改
            await _conversationRepository.UpdateAsync(conversation, cancellationToken);
            await _conversationRepository.SaveChangesAsync(cancellationToken);

            // 重新获取对话
            var updatedConversation = await _conversationRepository.GetByIdAsync(conversation.Id, cancellationToken);
            var conversationDto = _mapper.Map<ConversationDto>(updatedConversation);

            _logger.LogInformation("对话更新成功: {ConversationId}", request.Id);

            return Result.Success(conversationDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新对话时发生错误: {ConversationId}", request.Id);
            return Result.Failure<ConversationDto>("更新对话时发生内部错误", "INTERNAL_ERROR");
        }
    }
}

/// <summary>
/// 归档对话命令处理器
/// </summary>
public class ArchiveConversationCommandHandler : ICommandHandler<ArchiveConversationCommand>
{
    private readonly IConversationRepository _conversationRepository;
    private readonly ILogger<ArchiveConversationCommandHandler> _logger;

    /// <summary>
    /// 初始化归档对话命令处理器
    /// </summary>
    public ArchiveConversationCommandHandler(
        IConversationRepository conversationRepository,
        ILogger<ArchiveConversationCommandHandler> logger)
    {
        _conversationRepository = conversationRepository;
        _logger = logger;
    }

    /// <summary>
    /// 处理归档对话命令
    /// </summary>
    public async Task<Result> Handle(ArchiveConversationCommand request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("开始归档对话: {ConversationId}", request.Id);

            // 获取对话
            var conversation = await _conversationRepository.GetByIdAsync(request.Id, cancellationToken);
            if (conversation == null)
            {
                return Result.Failure("对话不存在", "CONVERSATION_NOT_FOUND");
            }

            // 检查权限（简化版本）
            if (conversation.UserId != request.OperatorId)
            {
                return Result.Failure("没有权限归档此对话", "PERMISSION_DENIED");
            }

            // 归档对话
            conversation.Archive();

            // 保存更改
            await _conversationRepository.UpdateAsync(conversation, cancellationToken);
            await _conversationRepository.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("对话归档成功: {ConversationId}", request.Id);

            return Result.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "归档对话时发生错误: {ConversationId}", request.Id);
            return Result.Failure("归档对话时发生内部错误", "INTERNAL_ERROR");
        }
    }
}

/// <summary>
/// 删除对话命令处理器
/// </summary>
public class DeleteConversationCommandHandler : ICommandHandler<DeleteConversationCommand>
{
    private readonly IConversationRepository _conversationRepository;
    private readonly ILogger<DeleteConversationCommandHandler> _logger;

    /// <summary>
    /// 初始化删除对话命令处理器
    /// </summary>
    public DeleteConversationCommandHandler(
        IConversationRepository conversationRepository,
        ILogger<DeleteConversationCommandHandler> logger)
    {
        _conversationRepository = conversationRepository;
        _logger = logger;
    }

    /// <summary>
    /// 处理删除对话命令
    /// </summary>
    public async Task<Result> Handle(DeleteConversationCommand request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("开始删除对话: {ConversationId}", request.Id);

            // 获取对话
            var conversation = await _conversationRepository.GetByIdAsync(request.Id, cancellationToken);
            if (conversation == null)
            {
                return Result.Failure("对话不存在", "CONVERSATION_NOT_FOUND");
            }

            // 检查权限（简化版本）
            if (conversation.UserId != request.OperatorId)
            {
                return Result.Failure("没有权限删除此对话", "PERMISSION_DENIED");
            }

            // 删除对话
            await _conversationRepository.DeleteAsync(conversation, cancellationToken);
            await _conversationRepository.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("对话删除成功: {ConversationId}", request.Id);

            return Result.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "删除对话时发生错误: {ConversationId}", request.Id);
            return Result.Failure("删除对话时发生内部错误", "INTERNAL_ERROR");
        }
    }
}
