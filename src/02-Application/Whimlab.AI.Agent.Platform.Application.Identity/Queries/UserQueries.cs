using Whimlab.AI.Agent.Platform.Application.Core.Common;
using Whimlab.AI.Agent.Platform.Application.Identity.DTOs;
using Whimlab.AI.Agent.Platform.Domain.Core.Common;
using Whimlab.AI.Agent.Platform.Shared.Common.Enums;

namespace Whimlab.AI.Agent.Platform.Application.Identity.Queries;

/// <summary>
/// 根据ID获取用户查询
/// </summary>
public class GetUserByIdQuery : IQuery<UserDto?>
{
    /// <summary>
    /// 用户ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// 是否包含角色信息
    /// </summary>
    public bool IncludeRoles { get; set; } = true;

    /// <summary>
    /// 初始化查询
    /// </summary>
    /// <param name="id">用户ID</param>
    /// <param name="includeRoles">是否包含角色信息</param>
    public GetUserByIdQuery(Guid id, bool includeRoles = true)
    {
        Id = id;
        IncludeRoles = includeRoles;
    }
}

/// <summary>
/// 根据用户名获取用户查询
/// </summary>
public class GetUserByUsernameQuery : IQuery<UserDto?>
{
    /// <summary>
    /// 用户名
    /// </summary>
    public string Username { get; set; }

    /// <summary>
    /// 是否包含角色信息
    /// </summary>
    public bool IncludeRoles { get; set; } = true;

    /// <summary>
    /// 初始化查询
    /// </summary>
    /// <param name="username">用户名</param>
    /// <param name="includeRoles">是否包含角色信息</param>
    public GetUserByUsernameQuery(string username, bool includeRoles = true)
    {
        Username = username;
        IncludeRoles = includeRoles;
    }
}

/// <summary>
/// 根据邮箱获取用户查询
/// </summary>
public class GetUserByEmailQuery : IQuery<UserDto?>
{
    /// <summary>
    /// 邮箱地址
    /// </summary>
    public string Email { get; set; }

    /// <summary>
    /// 是否包含角色信息
    /// </summary>
    public bool IncludeRoles { get; set; } = true;

    /// <summary>
    /// 初始化查询
    /// </summary>
    /// <param name="email">邮箱地址</param>
    /// <param name="includeRoles">是否包含角色信息</param>
    public GetUserByEmailQuery(string email, bool includeRoles = true)
    {
        Email = email;
        IncludeRoles = includeRoles;
    }
}

/// <summary>
/// 获取用户列表查询
/// </summary>
public class GetUsersQuery : PagedQuery, IQuery<PagedResult<UserSummaryDto>>
{
    /// <summary>
    /// 用户类型过滤
    /// </summary>
    public UserType? UserType { get; set; }

    /// <summary>
    /// 激活状态过滤
    /// </summary>
    public bool? IsActive { get; set; }

    /// <summary>
    /// 邮箱验证状态过滤
    /// </summary>
    public bool? IsEmailVerified { get; set; }

    /// <summary>
    /// 角色ID过滤
    /// </summary>
    public Guid? RoleId { get; set; }

    /// <summary>
    /// 创建时间范围开始
    /// </summary>
    public DateTime? CreatedFrom { get; set; }

    /// <summary>
    /// 创建时间范围结束
    /// </summary>
    public DateTime? CreatedTo { get; set; }
}

/// <summary>
/// 检查用户名是否可用查询
/// </summary>
public class CheckUsernameAvailabilityQuery : IQuery<bool>
{
    /// <summary>
    /// 用户名
    /// </summary>
    public string Username { get; set; }

    /// <summary>
    /// 排除的用户ID
    /// </summary>
    public Guid? ExcludeUserId { get; set; }

    /// <summary>
    /// 初始化查询
    /// </summary>
    /// <param name="username">用户名</param>
    /// <param name="excludeUserId">排除的用户ID</param>
    public CheckUsernameAvailabilityQuery(string username, Guid? excludeUserId = null)
    {
        Username = username;
        ExcludeUserId = excludeUserId;
    }
}

/// <summary>
/// 检查邮箱是否可用查询
/// </summary>
public class CheckEmailAvailabilityQuery : IQuery<bool>
{
    /// <summary>
    /// 邮箱地址
    /// </summary>
    public string Email { get; set; }

    /// <summary>
    /// 排除的用户ID
    /// </summary>
    public Guid? ExcludeUserId { get; set; }

    /// <summary>
    /// 初始化查询
    /// </summary>
    /// <param name="email">邮箱地址</param>
    /// <param name="excludeUserId">排除的用户ID</param>
    public CheckEmailAvailabilityQuery(string email, Guid? excludeUserId = null)
    {
        Email = email;
        ExcludeUserId = excludeUserId;
    }
}

/// <summary>
/// 获取用户权限查询
/// </summary>
public class GetUserPermissionsQuery : IQuery<IEnumerable<string>>
{
    /// <summary>
    /// 用户ID
    /// </summary>
    public Guid UserId { get; set; }

    /// <summary>
    /// 初始化查询
    /// </summary>
    /// <param name="userId">用户ID</param>
    public GetUserPermissionsQuery(Guid userId)
    {
        UserId = userId;
    }
}

/// <summary>
/// 检查用户权限查询
/// </summary>
public class CheckUserPermissionQuery : IQuery<bool>
{
    /// <summary>
    /// 用户ID
    /// </summary>
    public Guid UserId { get; set; }

    /// <summary>
    /// 权限名称
    /// </summary>
    public string PermissionName { get; set; }

    /// <summary>
    /// 初始化查询
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="permissionName">权限名称</param>
    public CheckUserPermissionQuery(Guid userId, string permissionName)
    {
        UserId = userId;
        PermissionName = permissionName;
    }
}

/// <summary>
/// 获取用户统计信息查询
/// </summary>
public class GetUserStatisticsQuery : IQuery<UserStatisticsDto>
{
    /// <summary>
    /// 统计时间范围开始
    /// </summary>
    public DateTime? StartDate { get; set; }

    /// <summary>
    /// 统计时间范围结束
    /// </summary>
    public DateTime? EndDate { get; set; }
}

/// <summary>
/// 用户统计信息DTO
/// </summary>
public class UserStatisticsDto
{
    /// <summary>
    /// 总用户数
    /// </summary>
    public int TotalUsers { get; set; }

    /// <summary>
    /// 活跃用户数
    /// </summary>
    public int ActiveUsers { get; set; }

    /// <summary>
    /// 已验证邮箱用户数
    /// </summary>
    public int VerifiedEmailUsers { get; set; }

    /// <summary>
    /// 已验证手机用户数
    /// </summary>
    public int VerifiedPhoneUsers { get; set; }

    /// <summary>
    /// 新注册用户数（指定时间范围内）
    /// </summary>
    public int NewRegistrations { get; set; }

    /// <summary>
    /// 按用户类型分组的统计
    /// </summary>
    public Dictionary<UserType, int> UsersByType { get; set; } = new();

    /// <summary>
    /// 按日期分组的注册统计
    /// </summary>
    public Dictionary<DateTime, int> RegistrationsByDate { get; set; } = new();
}
