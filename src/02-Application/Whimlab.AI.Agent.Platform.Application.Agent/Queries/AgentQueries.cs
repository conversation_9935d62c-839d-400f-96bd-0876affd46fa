using Whimlab.AI.Agent.Platform.Application.Core.Common;
using Whimlab.AI.Agent.Platform.Application.Agent.DTOs;
using Whimlab.AI.Agent.Platform.Domain.Core.Common;
using Whimlab.AI.Agent.Platform.Shared.Common.Enums;

namespace Whimlab.AI.Agent.Platform.Application.Agent.Queries;

/// <summary>
/// 根据ID获取智能体查询
/// </summary>
public class GetAgentByIdQuery : IQuery<AgentDto?>
{
    /// <summary>
    /// 智能体ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// 是否包含版本信息
    /// </summary>
    public bool IncludeVersions { get; set; } = false;

    /// <summary>
    /// 初始化查询
    /// </summary>
    /// <param name="id">智能体ID</param>
    /// <param name="includeVersions">是否包含版本信息</param>
    public GetAgentByIdQuery(Guid id, bool includeVersions = false)
    {
        Id = id;
        IncludeVersions = includeVersions;
    }
}

/// <summary>
/// 获取用户的智能体列表查询
/// </summary>
public class GetUserAgentsQuery : PagedQuery, IQuery<PagedResult<AgentSummaryDto>>
{
    /// <summary>
    /// 创建者ID
    /// </summary>
    public Guid CreatedBy { get; set; }

    /// <summary>
    /// 智能体类型过滤
    /// </summary>
    public AgentType? AgentType { get; set; }

    /// <summary>
    /// 状态过滤
    /// </summary>
    public AgentStatus? Status { get; set; }

    /// <summary>
    /// 分类过滤
    /// </summary>
    public string? Category { get; set; }

    /// <summary>
    /// 初始化查询
    /// </summary>
    /// <param name="createdBy">创建者ID</param>
    public GetUserAgentsQuery(Guid createdBy)
    {
        CreatedBy = createdBy;
    }
}

/// <summary>
/// 获取公开智能体列表查询
/// </summary>
public class GetPublicAgentsQuery : PagedQuery, IQuery<PagedResult<AgentSummaryDto>>
{
    /// <summary>
    /// 智能体类型过滤
    /// </summary>
    public AgentType? AgentType { get; set; }

    /// <summary>
    /// 分类过滤
    /// </summary>
    public string? Category { get; set; }

    /// <summary>
    /// 标签过滤
    /// </summary>
    public List<string> Tags { get; set; } = new();

    /// <summary>
    /// 最小评分过滤
    /// </summary>
    public double? MinRating { get; set; }
}

/// <summary>
/// 获取热门智能体查询
/// </summary>
public class GetPopularAgentsQuery : IQuery<IEnumerable<AgentSummaryDto>>
{
    /// <summary>
    /// 数量限制
    /// </summary>
    public int Count { get; set; } = 10;

    /// <summary>
    /// 智能体类型过滤
    /// </summary>
    public AgentType? AgentType { get; set; }

    /// <summary>
    /// 初始化查询
    /// </summary>
    /// <param name="count">数量限制</param>
    /// <param name="agentType">智能体类型过滤</param>
    public GetPopularAgentsQuery(int count = 10, AgentType? agentType = null)
    {
        Count = count;
        AgentType = agentType;
    }
}

/// <summary>
/// 根据标签获取智能体查询
/// </summary>
public class GetAgentsByTagsQuery : PagedQuery, IQuery<PagedResult<AgentSummaryDto>>
{
    /// <summary>
    /// 标签列表
    /// </summary>
    public List<string> Tags { get; set; } = new();

    /// <summary>
    /// 初始化查询
    /// </summary>
    /// <param name="tags">标签列表</param>
    public GetAgentsByTagsQuery(IEnumerable<string> tags)
    {
        Tags = tags.ToList();
    }
}

/// <summary>
/// 检查智能体名称是否可用查询
/// </summary>
public class CheckAgentNameAvailabilityQuery : IQuery<bool>
{
    /// <summary>
    /// 智能体名称
    /// </summary>
    public string Name { get; set; }

    /// <summary>
    /// 创建者ID
    /// </summary>
    public Guid CreatedBy { get; set; }

    /// <summary>
    /// 排除的智能体ID
    /// </summary>
    public Guid? ExcludeAgentId { get; set; }

    /// <summary>
    /// 初始化查询
    /// </summary>
    /// <param name="name">智能体名称</param>
    /// <param name="createdBy">创建者ID</param>
    /// <param name="excludeAgentId">排除的智能体ID</param>
    public CheckAgentNameAvailabilityQuery(string name, Guid createdBy, Guid? excludeAgentId = null)
    {
        Name = name;
        CreatedBy = createdBy;
        ExcludeAgentId = excludeAgentId;
    }
}

/// <summary>
/// 获取智能体统计信息查询
/// </summary>
public class GetAgentStatisticsQuery : IQuery<AgentStatisticsDto>
{
    /// <summary>
    /// 创建者ID（可选，为空则获取全局统计）
    /// </summary>
    public Guid? CreatedBy { get; set; }

    /// <summary>
    /// 统计时间范围开始
    /// </summary>
    public DateTime? StartDate { get; set; }

    /// <summary>
    /// 统计时间范围结束
    /// </summary>
    public DateTime? EndDate { get; set; }

    /// <summary>
    /// 初始化查询
    /// </summary>
    /// <param name="createdBy">创建者ID</param>
    /// <param name="startDate">开始时间</param>
    /// <param name="endDate">结束时间</param>
    public GetAgentStatisticsQuery(Guid? createdBy = null, DateTime? startDate = null, DateTime? endDate = null)
    {
        CreatedBy = createdBy;
        StartDate = startDate;
        EndDate = endDate;
    }
}

/// <summary>
/// 搜索智能体查询
/// </summary>
public class SearchAgentsQuery : PagedQuery, IQuery<PagedResult<AgentSummaryDto>>
{
    /// <summary>
    /// 搜索关键词
    /// </summary>
    public string Query { get; set; } = string.Empty;

    /// <summary>
    /// 智能体类型过滤
    /// </summary>
    public AgentType? AgentType { get; set; }

    /// <summary>
    /// 分类过滤
    /// </summary>
    public string? Category { get; set; }

    /// <summary>
    /// 标签过滤
    /// </summary>
    public List<string> Tags { get; set; } = new();

    /// <summary>
    /// 最小评分过滤
    /// </summary>
    public double? MinRating { get; set; }

    /// <summary>
    /// 是否只搜索公开智能体
    /// </summary>
    public bool PublicOnly { get; set; } = true;

    /// <summary>
    /// 初始化查询
    /// </summary>
    /// <param name="query">搜索关键词</param>
    public SearchAgentsQuery(string query)
    {
        Query = query;
    }
}

/// <summary>
/// 智能体统计信息DTO
/// </summary>
public class AgentStatisticsDto
{
    /// <summary>
    /// 总数量
    /// </summary>
    public int TotalCount { get; set; }

    /// <summary>
    /// 已发布数量
    /// </summary>
    public int PublishedCount { get; set; }

    /// <summary>
    /// 草稿数量
    /// </summary>
    public int DraftCount { get; set; }

    /// <summary>
    /// 已归档数量
    /// </summary>
    public int ArchivedCount { get; set; }

    /// <summary>
    /// 总使用次数
    /// </summary>
    public long TotalUsageCount { get; set; }

    /// <summary>
    /// 平均评分
    /// </summary>
    public double AverageRating { get; set; }

    /// <summary>
    /// 按类型分组的统计
    /// </summary>
    public Dictionary<AgentType, int> CountByType { get; set; } = new();

    /// <summary>
    /// 按分类分组的统计
    /// </summary>
    public Dictionary<string, int> CountByCategory { get; set; } = new();

    /// <summary>
    /// 按日期分组的创建统计
    /// </summary>
    public Dictionary<DateTime, int> CreationsByDate { get; set; } = new();

    /// <summary>
    /// 按日期分组的使用统计
    /// </summary>
    public Dictionary<DateTime, long> UsageByDate { get; set; } = new();

    /// <summary>
    /// 最受欢迎的智能体
    /// </summary>
    public List<AgentSummaryDto> TopAgents { get; set; } = new();
}
