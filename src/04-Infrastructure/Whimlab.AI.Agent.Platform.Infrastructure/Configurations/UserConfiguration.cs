using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Whimlab.AI.Agent.Platform.Domain.Identity.Entities;

namespace Whimlab.AI.Agent.Platform.Infrastructure.Configurations;

/// <summary>
/// 用户实体配置
/// </summary>
public class UserConfiguration : IEntityTypeConfiguration<User>
{
    /// <summary>
    /// 配置用户实体
    /// </summary>
    /// <param name="builder">实体类型构建器</param>
    public void Configure(EntityTypeBuilder<User> builder)
    {
        // 表名
        builder.ToTable("Users", "Identity");

        // 主键
        builder.HasKey(u => u.Id);

        // 属性配置
        builder.Property(u => u.Id)
            .IsRequired()
            .HasComment("用户ID");

        builder.Property(u => u.Username)
            .IsRequired()
            .HasMaxLength(50)
            .HasComment("用户名");

        // 配置值对象
        builder.OwnsOne(u => u.Email, email =>
        {
            email.Property(e => e.Value)
                .IsRequired()
                .HasMaxLength(255)
                .HasColumnName("Email")
                .HasComment("邮箱地址");
        });

        builder.OwnsOne(u => u.Password, password =>
        {
            password.Property(p => p.Hash)
                .IsRequired()
                .HasMaxLength(255)
                .HasColumnName("PasswordHash")
                .HasComment("密码哈希");
        });

        builder.OwnsOne(u => u.PhoneNumber, phone =>
        {
            phone.Property(p => p.Value)
                .HasMaxLength(20)
                .HasColumnName("PhoneNumber")
                .HasComment("电话号码");
        });

        builder.Property(u => u.DisplayName)
            .IsRequired()
            .HasMaxLength(100)
            .HasComment("显示名称");

        builder.Property(u => u.AvatarUrl)
            .HasMaxLength(500)
            .HasComment("头像URL");

        builder.Property(u => u.UserType)
            .IsRequired()
            .HasConversion<string>()
            .HasMaxLength(50)
            .HasComment("用户类型");

        builder.Property(u => u.IsActive)
            .IsRequired()
            .HasDefaultValue(true)
            .HasComment("是否激活");

        builder.Property(u => u.IsEmailVerified)
            .IsRequired()
            .HasDefaultValue(false)
            .HasComment("邮箱是否已确认");

        builder.Property(u => u.IsPhoneVerified)
            .IsRequired()
            .HasDefaultValue(false)
            .HasComment("电话是否已确认");

        builder.Property(u => u.IsLocked)
            .IsRequired()
            .HasDefaultValue(false)
            .HasComment("是否已锁定");

        builder.Property(u => u.LockoutEnd)
            .HasComment("锁定到期时间");

        builder.Property(u => u.FailedLoginAttempts)
            .IsRequired()
            .HasDefaultValue(0)
            .HasComment("失败登录次数");

        builder.Property(u => u.LastLoginAt)
            .HasComment("最后登录时间");

        builder.Property(u => u.LastLoginIp)
            .HasMaxLength(45)
            .HasComment("最后登录IP");

        builder.Property(u => u.PasswordChangedAt)
            .IsRequired()
            .HasComment("密码最后修改时间");

        builder.Property(u => u.EmailVerificationToken)
            .HasMaxLength(255)
            .HasComment("邮箱验证令牌");

        builder.Property(u => u.EmailVerificationTokenExpiry)
            .HasComment("邮箱验证令牌过期时间");

        builder.Property(u => u.PasswordResetToken)
            .HasMaxLength(255)
            .HasComment("密码重置令牌");

        builder.Property(u => u.PasswordResetTokenExpiry)
            .HasComment("密码重置令牌过期时间");

        builder.Property(u => u.CreatedAt)
            .IsRequired()
            .HasDefaultValueSql("GETUTCDATE()")
            .HasComment("创建时间");

        builder.Property(u => u.UpdatedAt)
            .HasComment("更新时间");

        // 索引
        builder.HasIndex(u => u.Username)
            .IsUnique()
            .HasDatabaseName("IX_Users_Username");

        builder.HasIndex("Email")
            .IsUnique()
            .HasDatabaseName("IX_Users_Email");

        builder.HasIndex("PhoneNumber")
            .HasDatabaseName("IX_Users_PhoneNumber");

        builder.HasIndex(u => u.IsActive)
            .HasDatabaseName("IX_Users_IsActive");

        builder.HasIndex(u => u.UserType)
            .HasDatabaseName("IX_Users_UserType");

        builder.HasIndex(u => u.CreatedAt)
            .HasDatabaseName("IX_Users_CreatedAt");

        // 关系配置 - 由于User实体没有导航属性，这里不配置关系
    }
}
