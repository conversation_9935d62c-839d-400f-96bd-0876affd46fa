using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Whimlab.AI.Agent.Platform.Domain.Conversation.Entities;
using Whimlab.AI.Agent.Platform.Shared.Common.Enums;

namespace Whimlab.AI.Agent.Platform.Infrastructure.Configurations;

/// <summary>
/// 对话实体配置
/// </summary>
public class ConversationConfiguration : IEntityTypeConfiguration<Conversation>
{
    /// <summary>
    /// 配置对话实体
    /// </summary>
    /// <param name="builder">实体类型构建器</param>
    public void Configure(EntityTypeBuilder<Conversation> builder)
    {
        // 表名
        builder.ToTable("Conversations", "Conversation");

        // 主键
        builder.HasKey(c => c.Id);

        // 属性配置
        builder.Property(c => c.Id)
            .IsRequired()
            .HasComment("对话ID");

        builder.Property(c => c.Title)
            .IsRequired()
            .HasMaxLength(200)
            .HasComment("对话标题");

        builder.Property(c => c.UserId)
            .IsRequired()
            .HasComment("用户ID");

        builder.Property(c => c.AgentId)
            .IsRequired()
            .HasComment("智能体ID");

        builder.Property(c => c.Status)
            .IsRequired()
            .HasConversion<string>()
            .HasMaxLength(50)
            .HasComment("对话状态");

        builder.Property(c => c.MessageCount)
            .IsRequired()
            .HasDefaultValue(0)
            .HasComment("消息总数");

        builder.Property(c => c.TotalTokensUsed)
            .IsRequired()
            .HasDefaultValue(0L)
            .HasComment("总Token消耗");

        builder.Property(c => c.LastActiveAt)
            .IsRequired()
            .HasDefaultValueSql("GETUTCDATE()")
            .HasComment("最后活跃时间");

        builder.Property(c => c.Context)
            .HasMaxLength(2000)
            .HasComment("对话上下文");

        builder.Property(c => c.CreatedAt)
            .IsRequired()
            .HasDefaultValueSql("GETUTCDATE()")
            .HasComment("创建时间");

        builder.Property(c => c.UpdatedAt)
            .HasComment("更新时间");

        // 索引
        builder.HasIndex(c => c.UserId)
            .HasDatabaseName("IX_Conversations_UserId");

        builder.HasIndex(c => c.AgentId)
            .HasDatabaseName("IX_Conversations_AgentId");

        builder.HasIndex(c => c.Status)
            .HasDatabaseName("IX_Conversations_Status");

        builder.HasIndex(c => c.LastActiveAt)
            .HasDatabaseName("IX_Conversations_LastActiveAt");

        builder.HasIndex(c => c.CreatedAt)
            .HasDatabaseName("IX_Conversations_CreatedAt");

        // 复合索引
        builder.HasIndex(c => new { c.UserId, c.Status })
            .HasDatabaseName("IX_Conversations_UserId_Status");

        builder.HasIndex(c => new { c.AgentId, c.Status })
            .HasDatabaseName("IX_Conversations_AgentId_Status");

        builder.HasIndex(c => new { c.UserId, c.LastActiveAt })
            .HasDatabaseName("IX_Conversations_UserId_LastActiveAt");

        // 关系配置
        builder.HasMany(c => c.Messages)
            .WithOne()
            .HasForeignKey(m => m.ConversationId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}
