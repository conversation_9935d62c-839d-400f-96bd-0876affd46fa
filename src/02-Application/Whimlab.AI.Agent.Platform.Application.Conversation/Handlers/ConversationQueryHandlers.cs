using AutoMapper;
using Microsoft.Extensions.Logging;
using Whimlab.AI.Agent.Platform.Application.Core.Common;
using Whimlab.AI.Agent.Platform.Application.Conversation.DTOs;
using Whimlab.AI.Agent.Platform.Application.Conversation.Queries;
using Whimlab.AI.Agent.Platform.Domain.Core.Common;
using Whimlab.AI.Agent.Platform.Domain.Conversation.Repositories;
using Whimlab.AI.Agent.Platform.Shared.Common.Enums;

namespace Whimlab.AI.Agent.Platform.Application.Conversation.Handlers;

/// <summary>
/// 根据ID获取对话查询处理器
/// </summary>
public class GetConversationByIdQueryHandler : IQueryHandler<GetConversationByIdQuery, ConversationDto?>
{
    private readonly IConversationRepository _conversationRepository;
    private readonly IMapper _mapper;
    private readonly ILogger<GetConversationByIdQueryHandler> _logger;

    /// <summary>
    /// 初始化查询处理器
    /// </summary>
    public GetConversationByIdQueryHandler(
        IConversationRepository conversationRepository,
        IMapper mapper,
        ILogger<GetConversationByIdQueryHandler> logger)
    {
        _conversationRepository = conversationRepository;
        _mapper = mapper;
        _logger = logger;
    }

    /// <summary>
    /// 处理查询
    /// </summary>
    public async Task<Result<ConversationDto?>> Handle(GetConversationByIdQuery request, CancellationToken cancellationToken)
    {
        try
        {
            var conversation = await _conversationRepository.GetByIdAsync(request.Id, cancellationToken);

            if (conversation == null)
            {
                return Result.Success<ConversationDto?>(null);
            }

            var conversationDto = _mapper.Map<ConversationDto>(conversation);
            return Result.Success<ConversationDto?>(conversationDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取对话时发生错误: {ConversationId}", request.Id);
            return Result.Failure<ConversationDto?>("获取对话时发生内部错误", "INTERNAL_ERROR");
        }
    }
}

/// <summary>
/// 获取用户对话列表查询处理器
/// </summary>
public class GetUserConversationsQueryHandler : IQueryHandler<GetUserConversationsQuery, PagedResult<ConversationSummaryDto>>
{
    private readonly IConversationRepository _conversationRepository;
    private readonly IMapper _mapper;
    private readonly ILogger<GetUserConversationsQueryHandler> _logger;

    /// <summary>
    /// 初始化查询处理器
    /// </summary>
    public GetUserConversationsQueryHandler(
        IConversationRepository conversationRepository,
        IMapper mapper,
        ILogger<GetUserConversationsQueryHandler> logger)
    {
        _conversationRepository = conversationRepository;
        _mapper = mapper;
        _logger = logger;
    }

    /// <summary>
    /// 处理查询
    /// </summary>
    public async Task<Result<PagedResult<ConversationSummaryDto>>> Handle(GetUserConversationsQuery request, CancellationToken cancellationToken)
    {
        try
        {
            request.Validate();

            var conversations = await _conversationRepository.GetUserConversationsAsync(
                request.UserId,
                request.PageNumber,
                request.PageSize,
                request.SearchTerm,
                request.Status,
                request.AgentId,
                cancellationToken);

            var conversationDtos = conversations.Items.Select(c => _mapper.Map<ConversationSummaryDto>(c));
            var result = new PagedResult<ConversationSummaryDto>(conversationDtos, conversations.TotalCount, request.PageNumber, request.PageSize);

            return Result.Success(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取用户对话列表时发生错误: {UserId}", request.UserId);
            return Result.Failure<PagedResult<ConversationSummaryDto>>("获取对话列表时发生内部错误", "INTERNAL_ERROR");
        }
    }
}

/// <summary>
/// 获取对话消息查询处理器
/// </summary>
public class GetConversationMessagesQueryHandler : IQueryHandler<GetConversationMessagesQuery, PagedResult<MessageDto>>
{
    private readonly IMessageRepository _messageRepository;
    private readonly IMapper _mapper;
    private readonly ILogger<GetConversationMessagesQueryHandler> _logger;

    /// <summary>
    /// 初始化查询处理器
    /// </summary>
    public GetConversationMessagesQueryHandler(
        IMessageRepository messageRepository,
        IMapper mapper,
        ILogger<GetConversationMessagesQueryHandler> logger)
    {
        _messageRepository = messageRepository;
        _mapper = mapper;
        _logger = logger;
    }

    /// <summary>
    /// 处理查询
    /// </summary>
    public async Task<Result<PagedResult<MessageDto>>> Handle(GetConversationMessagesQuery request, CancellationToken cancellationToken)
    {
        try
        {
            request.Validate();

            var messages = await _messageRepository.GetConversationMessagesAsync(
                request.ConversationId,
                request.PageNumber,
                request.PageSize,
                null,
                false,
                cancellationToken);

            var messageDtos = messages.Items.Select(m => _mapper.Map<MessageDto>(m));
            var result = new PagedResult<MessageDto>(messageDtos, messages.TotalCount, request.PageNumber, request.PageSize);

            return Result.Success(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取对话消息时发生错误: {ConversationId}", request.ConversationId);
            return Result.Failure<PagedResult<MessageDto>>("获取消息列表时发生内部错误", "INTERNAL_ERROR");
        }
    }
}

/// <summary>
/// 根据ID获取消息查询处理器
/// </summary>
public class GetMessageByIdQueryHandler : IQueryHandler<GetMessageByIdQuery, MessageDto?>
{
    private readonly IMessageRepository _messageRepository;
    private readonly IMapper _mapper;
    private readonly ILogger<GetMessageByIdQueryHandler> _logger;

    /// <summary>
    /// 初始化查询处理器
    /// </summary>
    public GetMessageByIdQueryHandler(
        IMessageRepository messageRepository,
        IMapper mapper,
        ILogger<GetMessageByIdQueryHandler> logger)
    {
        _messageRepository = messageRepository;
        _mapper = mapper;
        _logger = logger;
    }

    /// <summary>
    /// 处理查询
    /// </summary>
    public async Task<Result<MessageDto?>> Handle(GetMessageByIdQuery request, CancellationToken cancellationToken)
    {
        try
        {
            var message = await _messageRepository.GetByIdAsync(request.Id, cancellationToken);

            if (message == null)
            {
                return Result.Success<MessageDto?>(null);
            }

            var messageDto = _mapper.Map<MessageDto>(message);
            return Result.Success<MessageDto?>(messageDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取消息时发生错误: {MessageId}", request.Id);
            return Result.Failure<MessageDto?>("获取消息时发生内部错误", "INTERNAL_ERROR");
        }
    }
}

/// <summary>
/// 获取Token使用统计查询处理器
/// </summary>
public class GetTokenUsageStatisticsQueryHandler : IQueryHandler<GetTokenUsageStatisticsQuery, TokenUsageStatisticsDto>
{
    private readonly IConversationRepository _conversationRepository;
    private readonly ILogger<GetTokenUsageStatisticsQueryHandler> _logger;

    /// <summary>
    /// 初始化查询处理器
    /// </summary>
    public GetTokenUsageStatisticsQueryHandler(
        IConversationRepository conversationRepository,
        ILogger<GetTokenUsageStatisticsQueryHandler> logger)
    {
        _conversationRepository = conversationRepository;
        _logger = logger;
    }

    /// <summary>
    /// 处理查询
    /// </summary>
    public async Task<Result<TokenUsageStatisticsDto>> Handle(GetTokenUsageStatisticsQuery request, CancellationToken cancellationToken)
    {
        try
        {
            var domainStatistics = await _conversationRepository.GetTokenUsageStatisticsAsync(
                request.UserId,
                request.StartDate,
                request.EndDate,
                cancellationToken);

            // 转换为应用层DTO
            var statistics = new TokenUsageStatisticsDto
            {
                UserId = domainStatistics.UserId,
                Date = request.StartDate,
                TotalTokens = domainStatistics.TotalTokensUsed,
                InputTokens = 0, // 需要从领域模型中获取
                OutputTokens = 0, // 需要从领域模型中获取
                ConversationCount = 0, // 需要从领域模型中获取
                MessageCount = 0, // 需要从领域模型中获取
                EstimatedCost = 0, // 需要计算
                AgentUsage = new Dictionary<Guid, AgentUsageDto>(),
                ModelUsage = new Dictionary<string, ModelUsageDto>()
            };

            return Result.Success(statistics);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取Token使用统计时发生错误: {UserId}", request.UserId);
            return Result.Failure<TokenUsageStatisticsDto>("获取统计信息时发生内部错误", "INTERNAL_ERROR");
        }
    }
}

/// <summary>
/// 获取对话统计查询处理器
/// </summary>
public class GetConversationStatisticsQueryHandler : IQueryHandler<GetConversationStatisticsQuery, ConversationStatisticsDto>
{
    private readonly IConversationRepository _conversationRepository;
    private readonly ILogger<GetConversationStatisticsQueryHandler> _logger;

    /// <summary>
    /// 初始化查询处理器
    /// </summary>
    public GetConversationStatisticsQueryHandler(
        IConversationRepository conversationRepository,
        ILogger<GetConversationStatisticsQueryHandler> logger)
    {
        _conversationRepository = conversationRepository;
        _logger = logger;
    }

    /// <summary>
    /// 处理查询
    /// </summary>
    public async Task<Result<ConversationStatisticsDto>> Handle(GetConversationStatisticsQuery request, CancellationToken cancellationToken)
    {
        try
        {
            var domainStatistics = await _conversationRepository.GetStatisticsAsync(
                request.UserId,
                null,
                request.StartDate,
                request.EndDate,
                cancellationToken);

            // 转换为应用层DTO
            var statistics = new ConversationStatisticsDto
            {
                TotalConversations = domainStatistics.TotalConversations,
                ActiveConversations = domainStatistics.ActiveConversations,
                ArchivedConversations = domainStatistics.ArchivedConversations,
                TotalMessages = domainStatistics.TotalMessages,
                TotalTokens = domainStatistics.TotalTokensUsed,
                TotalEstimatedCost = 0, // 需要计算
                AverageConversationLength = domainStatistics.AverageMessagesPerConversation,
                AverageTokenUsage = domainStatistics.AverageTokensPerConversation,
                DailyStatistics = new Dictionary<DateTime, DailyStatisticsDto>(),
                TopAgents = new List<AgentUsageDto>()
            };

            return Result.Success(statistics);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取对话统计时发生错误");
            return Result.Failure<ConversationStatisticsDto>("获取统计信息时发生内部错误", "INTERNAL_ERROR");
        }
    }
}

/// <summary>
/// 获取最近对话查询处理器
/// </summary>
public class GetRecentConversationsQueryHandler : IQueryHandler<GetRecentConversationsQuery, IEnumerable<ConversationSummaryDto>>
{
    private readonly IConversationRepository _conversationRepository;
    private readonly IMapper _mapper;
    private readonly ILogger<GetRecentConversationsQueryHandler> _logger;

    /// <summary>
    /// 初始化查询处理器
    /// </summary>
    public GetRecentConversationsQueryHandler(
        IConversationRepository conversationRepository,
        IMapper mapper,
        ILogger<GetRecentConversationsQueryHandler> logger)
    {
        _conversationRepository = conversationRepository;
        _mapper = mapper;
        _logger = logger;
    }

    /// <summary>
    /// 处理查询
    /// </summary>
    public async Task<Result<IEnumerable<ConversationSummaryDto>>> Handle(GetRecentConversationsQuery request, CancellationToken cancellationToken)
    {
        try
        {
            // 使用现有的GetUserConversationsAsync方法获取最近对话
            var conversations = await _conversationRepository.GetUserConversationsAsync(
                request.UserId,
                1,
                request.Count,
                null,
                ConversationStatus.Active,
                null,
                cancellationToken);

            var conversationDtos = conversations.Items.Select(c => _mapper.Map<ConversationSummaryDto>(c));
            return Result.Success(conversationDtos);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取最近对话时发生错误: {UserId}", request.UserId);
            return Result.Failure<IEnumerable<ConversationSummaryDto>>("获取最近对话时发生内部错误", "INTERNAL_ERROR");
        }
    }
}
