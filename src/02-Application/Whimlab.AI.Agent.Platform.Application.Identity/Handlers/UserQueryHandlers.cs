using AutoMapper;
using Microsoft.Extensions.Logging;
using Whimlab.AI.Agent.Platform.Application.Core.Common;
using Whimlab.AI.Agent.Platform.Application.Identity.DTOs;
using Whimlab.AI.Agent.Platform.Application.Identity.Queries;
using Whimlab.AI.Agent.Platform.Domain.Core.Common;
using Whimlab.AI.Agent.Platform.Domain.Identity.Repositories;
using Whimlab.AI.Agent.Platform.Domain.Identity.Services;

namespace Whimlab.AI.Agent.Platform.Application.Identity.Handlers;

/// <summary>
/// 根据ID获取用户查询处理器
/// </summary>
public class GetUserByIdQueryHandler : IQueryHandler<GetUserByIdQuery, UserDto?>
{
    private readonly IUserRepository _userRepository;
    private readonly IMapper _mapper;
    private readonly ILogger<GetUserByIdQueryHandler> _logger;

    /// <summary>
    /// 初始化查询处理器
    /// </summary>
    /// <param name="userRepository">用户仓储</param>
    /// <param name="mapper">映射器</param>
    /// <param name="logger">日志记录器</param>
    public GetUserByIdQueryHandler(
        IUserRepository userRepository,
        IMapper mapper,
        ILogger<GetUserByIdQueryHandler> logger)
    {
        _userRepository = userRepository;
        _mapper = mapper;
        _logger = logger;
    }

    /// <summary>
    /// 处理查询
    /// </summary>
    /// <param name="request">查询请求</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>用户DTO</returns>
    public async Task<Result<UserDto?>> Handle(GetUserByIdQuery request, CancellationToken cancellationToken)
    {
        try
        {
            var user = await _userRepository.GetByIdAsync(request.Id, cancellationToken);

            if (user == null)
            {
                return Result.Success<UserDto?>(null);
            }

            var userDto = _mapper.Map<UserDto>(user);
            return Result.Success<UserDto?>(userDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取用户时发生错误: {UserId}", request.Id);
            return Result.Failure<UserDto?>("获取用户时发生内部错误", "INTERNAL_ERROR");
        }
    }
}

/// <summary>
/// 根据用户名获取用户查询处理器
/// </summary>
public class GetUserByUsernameQueryHandler : IQueryHandler<GetUserByUsernameQuery, UserDto?>
{
    private readonly IUserRepository _userRepository;
    private readonly IMapper _mapper;
    private readonly ILogger<GetUserByUsernameQueryHandler> _logger;

    /// <summary>
    /// 初始化查询处理器
    /// </summary>
    /// <param name="userRepository">用户仓储</param>
    /// <param name="mapper">映射器</param>
    /// <param name="logger">日志记录器</param>
    public GetUserByUsernameQueryHandler(
        IUserRepository userRepository,
        IMapper mapper,
        ILogger<GetUserByUsernameQueryHandler> logger)
    {
        _userRepository = userRepository;
        _mapper = mapper;
        _logger = logger;
    }

    /// <summary>
    /// 处理查询
    /// </summary>
    /// <param name="request">查询请求</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>用户DTO</returns>
    public async Task<Result<UserDto?>> Handle(GetUserByUsernameQuery request, CancellationToken cancellationToken)
    {
        try
        {
            var user = await _userRepository.GetByUsernameAsync(request.Username, cancellationToken);

            if (user == null)
            {
                return Result.Success<UserDto?>(null);
            }

            var userDto = _mapper.Map<UserDto>(user);
            return Result.Success<UserDto?>(userDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "根据用户名获取用户时发生错误: {Username}", request.Username);
            return Result.Failure<UserDto?>("获取用户时发生内部错误", "INTERNAL_ERROR");
        }
    }
}

/// <summary>
/// 获取用户列表查询处理器
/// </summary>
public class GetUsersQueryHandler : IQueryHandler<GetUsersQuery, PagedResult<UserSummaryDto>>
{
    private readonly IUserRepository _userRepository;
    private readonly IMapper _mapper;
    private readonly ILogger<GetUsersQueryHandler> _logger;

    /// <summary>
    /// 初始化查询处理器
    /// </summary>
    /// <param name="userRepository">用户仓储</param>
    /// <param name="mapper">映射器</param>
    /// <param name="logger">日志记录器</param>
    public GetUsersQueryHandler(
        IUserRepository userRepository,
        IMapper mapper,
        ILogger<GetUsersQueryHandler> logger)
    {
        _userRepository = userRepository;
        _mapper = mapper;
        _logger = logger;
    }

    /// <summary>
    /// 处理查询
    /// </summary>
    /// <param name="request">查询请求</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>分页用户列表</returns>
    public async Task<Result<PagedResult<UserSummaryDto>>> Handle(GetUsersQuery request, CancellationToken cancellationToken)
    {
        try
        {
            request.Validate();

            var (users, totalCount) = await _userRepository.GetUsersAsync(
                request.UserType,
                request.IsActive,
                request.SearchTerm,
                request.PageNumber,
                request.PageSize,
                cancellationToken);

            var userDtos = users.Select(u => _mapper.Map<UserSummaryDto>(u));
            var result = new PagedResult<UserSummaryDto>(userDtos, totalCount, request.PageNumber, request.PageSize);

            return Result.Success(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取用户列表时发生错误");
            return Result.Failure<PagedResult<UserSummaryDto>>("获取用户列表时发生内部错误", "INTERNAL_ERROR");
        }
    }
}

/// <summary>
/// 检查用户名可用性查询处理器
/// </summary>
public class CheckUsernameAvailabilityQueryHandler : IQueryHandler<CheckUsernameAvailabilityQuery, bool>
{
    private readonly IUserDomainService _userDomainService;
    private readonly ILogger<CheckUsernameAvailabilityQueryHandler> _logger;

    /// <summary>
    /// 初始化查询处理器
    /// </summary>
    /// <param name="userDomainService">用户领域服务</param>
    /// <param name="logger">日志记录器</param>
    public CheckUsernameAvailabilityQueryHandler(
        IUserDomainService userDomainService,
        ILogger<CheckUsernameAvailabilityQueryHandler> logger)
    {
        _userDomainService = userDomainService;
        _logger = logger;
    }

    /// <summary>
    /// 处理查询
    /// </summary>
    /// <param name="request">查询请求</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>是否可用</returns>
    public async Task<Result<bool>> Handle(CheckUsernameAvailabilityQuery request, CancellationToken cancellationToken)
    {
        try
        {
            var isAvailable = await _userDomainService.IsUsernameAvailableAsync(
                request.Username, request.ExcludeUserId, cancellationToken);

            return Result.Success(isAvailable);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "检查用户名可用性时发生错误: {Username}", request.Username);
            return Result.Failure<bool>("检查用户名可用性时发生内部错误", "INTERNAL_ERROR");
        }
    }
}

/// <summary>
/// 获取用户权限查询处理器
/// </summary>
public class GetUserPermissionsQueryHandler : IQueryHandler<GetUserPermissionsQuery, IEnumerable<string>>
{
    private readonly IUserDomainService _userDomainService;
    private readonly IUserRepository _userRepository;
    private readonly ILogger<GetUserPermissionsQueryHandler> _logger;

    /// <summary>
    /// 初始化查询处理器
    /// </summary>
    /// <param name="userDomainService">用户领域服务</param>
    /// <param name="userRepository">用户仓储</param>
    /// <param name="logger">日志记录器</param>
    public GetUserPermissionsQueryHandler(
        IUserDomainService userDomainService,
        IUserRepository userRepository,
        ILogger<GetUserPermissionsQueryHandler> logger)
    {
        _userDomainService = userDomainService;
        _userRepository = userRepository;
        _logger = logger;
    }

    /// <summary>
    /// 处理查询
    /// </summary>
    /// <param name="request">查询请求</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>权限列表</returns>
    public async Task<Result<IEnumerable<string>>> Handle(GetUserPermissionsQuery request, CancellationToken cancellationToken)
    {
        try
        {
            var user = await _userRepository.GetByIdAsync(request.UserId, cancellationToken);
            if (user == null)
            {
                return Result.Failure<IEnumerable<string>>("用户不存在", "USER_NOT_FOUND");
            }

            var permissions = await _userDomainService.GetUserPermissionsAsync(user, cancellationToken);
            return Result.Success(permissions);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取用户权限时发生错误: {UserId}", request.UserId);
            return Result.Failure<IEnumerable<string>>("获取用户权限时发生内部错误", "INTERNAL_ERROR");
        }
    }
}
