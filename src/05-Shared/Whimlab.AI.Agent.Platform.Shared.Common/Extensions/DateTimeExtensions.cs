using System.Globalization;

namespace Whimlab.AI.Agent.Platform.Shared.Common.Extensions;

/// <summary>
/// DateTime扩展方法
/// </summary>
public static class DateTimeExtensions
{
    /// <summary>
    /// 转换为Unix时间戳（秒）
    /// </summary>
    /// <param name="dateTime">DateTime对象</param>
    /// <returns>Unix时间戳</returns>
    public static long ToUnixTimeSeconds(this DateTime dateTime)
    {
        return ((DateTimeOffset)dateTime).ToUnixTimeSeconds();
    }

    /// <summary>
    /// 转换为Unix时间戳（毫秒）
    /// </summary>
    /// <param name="dateTime">DateTime对象</param>
    /// <returns>Unix时间戳（毫秒）</returns>
    public static long ToUnixTimeMilliseconds(this DateTime dateTime)
    {
        return ((DateTimeOffset)dateTime).ToUnixTimeMilliseconds();
    }

    /// <summary>
    /// 从Unix时间戳（秒）创建DateTime
    /// </summary>
    /// <param name="unixTimeSeconds">Unix时间戳（秒）</param>
    /// <returns>DateTime对象</returns>
    public static DateTime FromUnixTimeSeconds(long unixTimeSeconds)
    {
        return DateTimeOffset.FromUnixTimeSeconds(unixTimeSeconds).DateTime;
    }

    /// <summary>
    /// 从Unix时间戳（毫秒）创建DateTime
    /// </summary>
    /// <param name="unixTimeMilliseconds">Unix时间戳（毫秒）</param>
    /// <returns>DateTime对象</returns>
    public static DateTime FromUnixTimeMilliseconds(long unixTimeMilliseconds)
    {
        return DateTimeOffset.FromUnixTimeMilliseconds(unixTimeMilliseconds).DateTime;
    }

    /// <summary>
    /// 获取一天的开始时间（00:00:00）
    /// </summary>
    /// <param name="dateTime">DateTime对象</param>
    /// <returns>一天的开始时间</returns>
    public static DateTime StartOfDay(this DateTime dateTime)
    {
        return dateTime.Date;
    }

    /// <summary>
    /// 获取一天的结束时间（23:59:59.999）
    /// </summary>
    /// <param name="dateTime">DateTime对象</param>
    /// <returns>一天的结束时间</returns>
    public static DateTime EndOfDay(this DateTime dateTime)
    {
        return dateTime.Date.AddDays(1).AddTicks(-1);
    }

    /// <summary>
    /// 获取一周的开始时间（周一00:00:00）
    /// </summary>
    /// <param name="dateTime">DateTime对象</param>
    /// <returns>一周的开始时间</returns>
    public static DateTime StartOfWeek(this DateTime dateTime)
    {
        var diff = (7 + (dateTime.DayOfWeek - DayOfWeek.Monday)) % 7;
        return dateTime.AddDays(-1 * diff).Date;
    }

    /// <summary>
    /// 获取一周的结束时间（周日23:59:59.999）
    /// </summary>
    /// <param name="dateTime">DateTime对象</param>
    /// <returns>一周的结束时间</returns>
    public static DateTime EndOfWeek(this DateTime dateTime)
    {
        return dateTime.StartOfWeek().AddDays(7).AddTicks(-1);
    }

    /// <summary>
    /// 获取一个月的开始时间（1号00:00:00）
    /// </summary>
    /// <param name="dateTime">DateTime对象</param>
    /// <returns>一个月的开始时间</returns>
    public static DateTime StartOfMonth(this DateTime dateTime)
    {
        return new DateTime(dateTime.Year, dateTime.Month, 1);
    }

    /// <summary>
    /// 获取一个月的结束时间（最后一天23:59:59.999）
    /// </summary>
    /// <param name="dateTime">DateTime对象</param>
    /// <returns>一个月的结束时间</returns>
    public static DateTime EndOfMonth(this DateTime dateTime)
    {
        return dateTime.StartOfMonth().AddMonths(1).AddTicks(-1);
    }

    /// <summary>
    /// 获取一年的开始时间（1月1日00:00:00）
    /// </summary>
    /// <param name="dateTime">DateTime对象</param>
    /// <returns>一年的开始时间</returns>
    public static DateTime StartOfYear(this DateTime dateTime)
    {
        return new DateTime(dateTime.Year, 1, 1);
    }

    /// <summary>
    /// 获取一年的结束时间（12月31日23:59:59.999）
    /// </summary>
    /// <param name="dateTime">DateTime对象</param>
    /// <returns>一年的结束时间</returns>
    public static DateTime EndOfYear(this DateTime dateTime)
    {
        return dateTime.StartOfYear().AddYears(1).AddTicks(-1);
    }

    /// <summary>
    /// 检查是否为今天
    /// </summary>
    /// <param name="dateTime">DateTime对象</param>
    /// <returns>如果是今天返回true，否则返回false</returns>
    public static bool IsToday(this DateTime dateTime)
    {
        return dateTime.Date == DateTime.Today;
    }

    /// <summary>
    /// 检查是否为昨天
    /// </summary>
    /// <param name="dateTime">DateTime对象</param>
    /// <returns>如果是昨天返回true，否则返回false</returns>
    public static bool IsYesterday(this DateTime dateTime)
    {
        return dateTime.Date == DateTime.Today.AddDays(-1);
    }

    /// <summary>
    /// 检查是否为明天
    /// </summary>
    /// <param name="dateTime">DateTime对象</param>
    /// <returns>如果是明天返回true，否则返回false</returns>
    public static bool IsTomorrow(this DateTime dateTime)
    {
        return dateTime.Date == DateTime.Today.AddDays(1);
    }

    /// <summary>
    /// 检查是否为工作日（周一到周五）
    /// </summary>
    /// <param name="dateTime">DateTime对象</param>
    /// <returns>如果是工作日返回true，否则返回false</returns>
    public static bool IsWeekday(this DateTime dateTime)
    {
        return dateTime.DayOfWeek >= DayOfWeek.Monday && dateTime.DayOfWeek <= DayOfWeek.Friday;
    }

    /// <summary>
    /// 检查是否为周末（周六或周日）
    /// </summary>
    /// <param name="dateTime">DateTime对象</param>
    /// <returns>如果是周末返回true，否则返回false</returns>
    public static bool IsWeekend(this DateTime dateTime)
    {
        return dateTime.DayOfWeek == DayOfWeek.Saturday || dateTime.DayOfWeek == DayOfWeek.Sunday;
    }

    /// <summary>
    /// 获取年龄
    /// </summary>
    /// <param name="birthDate">出生日期</param>
    /// <param name="referenceDate">参考日期，默认为今天</param>
    /// <returns>年龄</returns>
    public static int GetAge(this DateTime birthDate, DateTime? referenceDate = null)
    {
        var reference = referenceDate ?? DateTime.Today;
        var age = reference.Year - birthDate.Year;
        
        if (reference.Month < birthDate.Month || 
            (reference.Month == birthDate.Month && reference.Day < birthDate.Day))
        {
            age--;
        }
        
        return age;
    }

    /// <summary>
    /// 格式化为相对时间描述
    /// </summary>
    /// <param name="dateTime">DateTime对象</param>
    /// <param name="referenceTime">参考时间，默认为当前时间</param>
    /// <returns>相对时间描述</returns>
    public static string ToRelativeTimeString(this DateTime dateTime, DateTime? referenceTime = null)
    {
        var reference = referenceTime ?? DateTime.Now;
        var timeSpan = reference - dateTime;

        if (timeSpan.TotalSeconds < 60)
            return "刚刚";

        if (timeSpan.TotalMinutes < 60)
            return $"{(int)timeSpan.TotalMinutes}分钟前";

        if (timeSpan.TotalHours < 24)
            return $"{(int)timeSpan.TotalHours}小时前";

        if (timeSpan.TotalDays < 7)
            return $"{(int)timeSpan.TotalDays}天前";

        if (timeSpan.TotalDays < 30)
            return $"{(int)(timeSpan.TotalDays / 7)}周前";

        if (timeSpan.TotalDays < 365)
            return $"{(int)(timeSpan.TotalDays / 30)}个月前";

        return $"{(int)(timeSpan.TotalDays / 365)}年前";
    }

    /// <summary>
    /// 转换为中文日期格式
    /// </summary>
    /// <param name="dateTime">DateTime对象</param>
    /// <param name="includeTime">是否包含时间</param>
    /// <returns>中文日期格式字符串</returns>
    public static string ToChineseDateString(this DateTime dateTime, bool includeTime = false)
    {
        var culture = new CultureInfo("zh-CN");
        var format = includeTime ? "yyyy年MM月dd日 HH:mm:ss" : "yyyy年MM月dd日";
        return dateTime.ToString(format, culture);
    }

    /// <summary>
    /// 转换为ISO 8601格式字符串
    /// </summary>
    /// <param name="dateTime">DateTime对象</param>
    /// <returns>ISO 8601格式字符串</returns>
    public static string ToIso8601String(this DateTime dateTime)
    {
        return dateTime.ToString("yyyy-MM-ddTHH:mm:ss.fffZ");
    }

    /// <summary>
    /// 获取季度
    /// </summary>
    /// <param name="dateTime">DateTime对象</param>
    /// <returns>季度（1-4）</returns>
    public static int GetQuarter(this DateTime dateTime)
    {
        return (dateTime.Month - 1) / 3 + 1;
    }

    /// <summary>
    /// 获取季度的开始时间
    /// </summary>
    /// <param name="dateTime">DateTime对象</param>
    /// <returns>季度的开始时间</returns>
    public static DateTime StartOfQuarter(this DateTime dateTime)
    {
        var quarter = dateTime.GetQuarter();
        var month = (quarter - 1) * 3 + 1;
        return new DateTime(dateTime.Year, month, 1);
    }

    /// <summary>
    /// 获取季度的结束时间
    /// </summary>
    /// <param name="dateTime">DateTime对象</param>
    /// <returns>季度的结束时间</returns>
    public static DateTime EndOfQuarter(this DateTime dateTime)
    {
        return dateTime.StartOfQuarter().AddMonths(3).AddTicks(-1);
    }

    /// <summary>
    /// 检查是否在指定的日期范围内
    /// </summary>
    /// <param name="dateTime">要检查的日期</param>
    /// <param name="startDate">开始日期</param>
    /// <param name="endDate">结束日期</param>
    /// <param name="inclusive">是否包含边界</param>
    /// <returns>如果在范围内返回true，否则返回false</returns>
    public static bool IsBetween(this DateTime dateTime, DateTime startDate, DateTime endDate, bool inclusive = true)
    {
        if (inclusive)
            return dateTime >= startDate && dateTime <= endDate;
        
        return dateTime > startDate && dateTime < endDate;
    }

    /// <summary>
    /// 获取下一个指定星期几的日期
    /// </summary>
    /// <param name="dateTime">起始日期</param>
    /// <param name="dayOfWeek">目标星期几</param>
    /// <returns>下一个指定星期几的日期</returns>
    public static DateTime GetNextWeekday(this DateTime dateTime, DayOfWeek dayOfWeek)
    {
        var daysUntilTarget = ((int)dayOfWeek - (int)dateTime.DayOfWeek + 7) % 7;
        if (daysUntilTarget == 0)
            daysUntilTarget = 7; // 如果是同一天，则返回下周的这一天
        
        return dateTime.AddDays(daysUntilTarget);
    }

    /// <summary>
    /// 获取上一个指定星期几的日期
    /// </summary>
    /// <param name="dateTime">起始日期</param>
    /// <param name="dayOfWeek">目标星期几</param>
    /// <returns>上一个指定星期几的日期</returns>
    public static DateTime GetPreviousWeekday(this DateTime dateTime, DayOfWeek dayOfWeek)
    {
        var daysSinceTarget = ((int)dateTime.DayOfWeek - (int)dayOfWeek + 7) % 7;
        if (daysSinceTarget == 0)
            daysSinceTarget = 7; // 如果是同一天，则返回上周的这一天
        
        return dateTime.AddDays(-daysSinceTarget);
    }
}
