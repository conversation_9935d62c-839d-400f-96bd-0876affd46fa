using FluentValidation;
using Whimlab.AI.Agent.Platform.Application.Conversation.Commands;
using Whimlab.AI.Agent.Platform.Application.Conversation.DTOs;
using Whimlab.AI.Agent.Platform.Shared.Common.Enums;

namespace Whimlab.AI.Agent.Platform.Application.Conversation.Validators;

/// <summary>
/// 创建对话命令验证器
/// </summary>
public class CreateConversationCommandValidator : AbstractValidator<CreateConversationCommand>
{
    /// <summary>
    /// 初始化验证器
    /// </summary>
    public CreateConversationCommandValidator()
    {
        RuleFor(x => x.Title)
            .NotEmpty().WithMessage("对话标题不能为空")
            .Length(1, 200).WithMessage("对话标题长度必须在1-200个字符之间");

        RuleFor(x => x.UserId)
            .NotEmpty().WithMessage("用户ID不能为空");

        RuleFor(x => x.AgentId)
            .NotEmpty().WithMessage("智能体ID不能为空");

        RuleFor(x => x.InitialMessage)
            .MaximumLength(10000).WithMessage("初始消息长度不能超过10000个字符")
            .When(x => !string.IsNullOrWhiteSpace(x.InitialMessage));

        RuleFor(x => x.Tags)
            .Must(tags => tags.Count <= 10).WithMessage("标签数量不能超过10个")
            .Must(tags => tags.All(tag => !string.IsNullOrWhiteSpace(tag) && tag.Length <= 50))
            .WithMessage("每个标签长度不能超过50个字符且不能为空");
    }
}

/// <summary>
/// 更新对话命令验证器
/// </summary>
public class UpdateConversationCommandValidator : AbstractValidator<UpdateConversationCommand>
{
    /// <summary>
    /// 初始化验证器
    /// </summary>
    public UpdateConversationCommandValidator()
    {
        RuleFor(x => x.Id)
            .NotEmpty().WithMessage("对话ID不能为空");

        RuleFor(x => x.Title)
            .NotEmpty().WithMessage("对话标题不能为空")
            .Length(1, 200).WithMessage("对话标题长度必须在1-200个字符之间");

        RuleFor(x => x.Summary)
            .MaximumLength(1000).WithMessage("对话摘要长度不能超过1000个字符")
            .When(x => !string.IsNullOrWhiteSpace(x.Summary));

        RuleFor(x => x.Tags)
            .Must(tags => tags.Count <= 10).WithMessage("标签数量不能超过10个")
            .Must(tags => tags.All(tag => !string.IsNullOrWhiteSpace(tag) && tag.Length <= 50))
            .WithMessage("每个标签长度不能超过50个字符且不能为空");

        RuleFor(x => x.OperatorId)
            .NotEmpty().WithMessage("操作者ID不能为空");
    }
}

/// <summary>
/// 归档对话命令验证器
/// </summary>
public class ArchiveConversationCommandValidator : AbstractValidator<ArchiveConversationCommand>
{
    /// <summary>
    /// 初始化验证器
    /// </summary>
    public ArchiveConversationCommandValidator()
    {
        RuleFor(x => x.Id)
            .NotEmpty().WithMessage("对话ID不能为空");

        RuleFor(x => x.OperatorId)
            .NotEmpty().WithMessage("操作者ID不能为空");
    }
}

/// <summary>
/// 删除对话命令验证器
/// </summary>
public class DeleteConversationCommandValidator : AbstractValidator<DeleteConversationCommand>
{
    /// <summary>
    /// 初始化验证器
    /// </summary>
    public DeleteConversationCommandValidator()
    {
        RuleFor(x => x.Id)
            .NotEmpty().WithMessage("对话ID不能为空");

        RuleFor(x => x.OperatorId)
            .NotEmpty().WithMessage("操作者ID不能为空");
    }
}

/// <summary>
/// 批量归档对话命令验证器
/// </summary>
public class BatchArchiveConversationsCommandValidator : AbstractValidator<BatchArchiveConversationsCommand>
{
    /// <summary>
    /// 初始化验证器
    /// </summary>
    public BatchArchiveConversationsCommandValidator()
    {
        RuleFor(x => x.UserId)
            .NotEmpty().WithMessage("用户ID不能为空");

        RuleFor(x => x.LastActivityBefore)
            .LessThan(DateTime.UtcNow).WithMessage("最后活动时间必须早于当前时间");

        RuleFor(x => x.BatchSize)
            .InclusiveBetween(1, 1000).WithMessage("批次大小必须在1-1000之间");

        RuleFor(x => x.OperatorId)
            .NotEmpty().WithMessage("操作者ID不能为空");
    }
}

/// <summary>
/// 创建消息命令验证器
/// </summary>
public class CreateMessageCommandValidator : AbstractValidator<CreateMessageCommand>
{
    /// <summary>
    /// 初始化验证器
    /// </summary>
    public CreateMessageCommandValidator()
    {
        RuleFor(x => x.ConversationId)
            .NotEmpty().WithMessage("对话ID不能为空");

        RuleFor(x => x.Role)
            .IsInEnum().WithMessage("消息角色无效");

        RuleFor(x => x.Content)
            .NotEmpty().WithMessage("消息内容不能为空")
            .MaximumLength(50000).WithMessage("消息内容长度不能超过50000个字符");

        RuleFor(x => x.MessageType)
            .IsInEnum().WithMessage("消息类型无效");

        RuleFor(x => x.Attachments)
            .Must(attachments => attachments.Count <= 10).WithMessage("附件数量不能超过10个");

        RuleForEach(x => x.Attachments)
            .SetValidator(new CreateMessageAttachmentDtoValidator());

        RuleFor(x => x.Metadata)
            .Must(BeValidJson).WithMessage("元数据必须是有效的JSON格式")
            .When(x => !string.IsNullOrWhiteSpace(x.Metadata));

        RuleFor(x => x.OperatorId)
            .NotEmpty().WithMessage("操作者ID不能为空");
    }

    /// <summary>
    /// 验证JSON格式
    /// </summary>
    private static bool BeValidJson(string? json)
    {
        if (string.IsNullOrWhiteSpace(json))
            return true;

        try
        {
            System.Text.Json.JsonDocument.Parse(json);
            return true;
        }
        catch
        {
            return false;
        }
    }
}

/// <summary>
/// 发送消息命令验证器
/// </summary>
public class SendMessageCommandValidator : AbstractValidator<SendMessageCommand>
{
    /// <summary>
    /// 初始化验证器
    /// </summary>
    public SendMessageCommandValidator()
    {
        RuleFor(x => x.ConversationId)
            .NotEmpty().WithMessage("对话ID不能为空");

        RuleFor(x => x.Content)
            .NotEmpty().WithMessage("消息内容不能为空")
            .MaximumLength(50000).WithMessage("消息内容长度不能超过50000个字符");

        RuleFor(x => x.MessageType)
            .IsInEnum().WithMessage("消息类型无效");

        RuleFor(x => x.Attachments)
            .Must(attachments => attachments.Count <= 10).WithMessage("附件数量不能超过10个");

        RuleForEach(x => x.Attachments)
            .SetValidator(new CreateMessageAttachmentDtoValidator());

        RuleFor(x => x.Metadata)
            .Must(BeValidJson).WithMessage("元数据必须是有效的JSON格式")
            .When(x => !string.IsNullOrWhiteSpace(x.Metadata));

        RuleFor(x => x.UserId)
            .NotEmpty().WithMessage("用户ID不能为空");
    }

    /// <summary>
    /// 验证JSON格式
    /// </summary>
    private static bool BeValidJson(string? json)
    {
        if (string.IsNullOrWhiteSpace(json))
            return true;

        try
        {
            System.Text.Json.JsonDocument.Parse(json);
            return true;
        }
        catch
        {
            return false;
        }
    }
}

/// <summary>
/// 创建消息附件DTO验证器
/// </summary>
public class CreateMessageAttachmentDtoValidator : AbstractValidator<CreateMessageAttachmentDto>
{
    /// <summary>
    /// 初始化验证器
    /// </summary>
    public CreateMessageAttachmentDtoValidator()
    {
        RuleFor(x => x.FileName)
            .NotEmpty().WithMessage("文件名不能为空")
            .MaximumLength(255).WithMessage("文件名长度不能超过255个字符");

        RuleFor(x => x.ContentType)
            .NotEmpty().WithMessage("文件类型不能为空")
            .MaximumLength(100).WithMessage("文件类型长度不能超过100个字符");

        RuleFor(x => x.FileSize)
            .GreaterThan(0).WithMessage("文件大小必须大于0")
            .LessThanOrEqualTo(100 * 1024 * 1024).WithMessage("文件大小不能超过100MB");

        RuleFor(x => x.FileContent)
            .NotEmpty().WithMessage("文件内容不能为空")
            .Must(BeValidBase64).WithMessage("文件内容必须是有效的Base64格式");
    }

    /// <summary>
    /// 验证Base64格式
    /// </summary>
    private static bool BeValidBase64(string base64)
    {
        try
        {
            Convert.FromBase64String(base64);
            return true;
        }
        catch
        {
            return false;
        }
    }
}
